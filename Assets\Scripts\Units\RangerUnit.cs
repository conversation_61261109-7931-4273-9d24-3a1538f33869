using System.Collections;
using UnityEngine;


public class RangerUnit : SoldierUnit
{
    [SerializeField] private Projectile m_ProjectilePrefab;
    [SerializeField] private float m_delay = 0.62f;


    protected override void OnAttakReady(Unit target)
    {
        PerformAttackAnimation();
        StartCoroutine(ShootProjectile(m_delay, target));
    }

    IEnumerator ShootProjectile(float delay, Unit target)
    {
        yield return new WaitForSeconds(delay);

        if (CurrentState == UnitState.Dead) yield return null;

        if (target != null && target.CurrentState != UnitState.Dead)
        {
            Projectile projectile = Instantiate(m_ProjectilePrefab, transform.position, Quaternion.identity);
            projectile.Initialize(this, target, m_AutoAttackDamage);
        }


    }


}
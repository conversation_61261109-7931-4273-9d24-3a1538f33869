fileFormatVersion: 2
guid: 7fd9f7d97ea76ba4a8998734b8941cfb
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1235903452724194183
    second: <PERSON><PERSON>_<PERSON>_0
  - first:
      213: -7227994795241161344
    second: <PERSON><PERSON>_Red_1
  - first:
      213: -6761793885924507605
    second: <PERSON><PERSON>_Red_2
  - first:
      213: 8838147863419733177
    second: <PERSON><PERSON>_<PERSON>_3
  - first:
      213: 705362571618356652
    second: <PERSON><PERSON>_Red_4
  - first:
      213: -5237204685284653864
    second: <PERSON><PERSON>_Red_5
  - first:
      213: -795475609545570021
    second: <PERSON><PERSON>_Red_6
  - first:
      213: 2606292137879948835
    second: Barr<PERSON>_Red_7
  - first:
      213: 3278922006464453553
    second: <PERSON><PERSON>_Red_8
  - first:
      213: -1611858049236281846
    second: <PERSON><PERSON>_<PERSON>_9
  - first:
      213: 4667250231190558756
    second: <PERSON><PERSON>_<PERSON>_10
  - first:
      213: 3689843565939147757
    second: <PERSON><PERSON>_<PERSON>_11
  - first:
      213: -751311741623594299
    second: <PERSON><PERSON>_Red_12
  - first:
      213: -4532688768257187353
    second: <PERSON><PERSON>_Red_13
  - first:
      213: -3494105244832726409
    second: Barrel_Red_14
  - first:
      213: -4211475759844697773
    second: Barrel_Red_15
  - first:
      213: -1772941181524602865
    second: Barrel_Red_16
  - first:
      213: -454335569907399357
    second: Barrel_Red_17
  - first:
      213: 881422412132157723
    second: Barrel_Red_18
  - first:
      213: -4545803442031372541
    second: Barrel_Red_19
  - first:
      213: 6287177064173362728
    second: Barrel_Red_20
  - first:
      213: -1901058906039841320
    second: Barrel_Red_21
  - first:
      213: 8105678916111890790
    second: Barrel_Red_22
  - first:
      213: 6957373138255738008
    second: Barrel_Red_23
  - first:
      213: -8155608035436112814
    second: Barrel_Red_24
  - first:
      213: 3592700549039835320
    second: Barrel_Red_25
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Barrel_Red_0
      rect:
        serializedVersion: 2
        x: 38
        y: 668
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97412bb9b5039dee0800000000000000
      internalID: -1235903452724194183
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_1
      rect:
        serializedVersion: 2
        x: 34
        y: 540
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08d374f770101bb90800000000000000
      internalID: -7227994795241161344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_2
      rect:
        serializedVersion: 2
        x: 170
        y: 536
        width: 44
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b20cf0949484922a0800000000000000
      internalID: -6761793885924507605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_3
      rect:
        serializedVersion: 2
        x: 294
        y: 536
        width: 52
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9b4c28fb7a867aa70800000000000000
      internalID: 8838147863419733177
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_4
      rect:
        serializedVersion: 2
        x: 420
        y: 537
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cad0b2f8583f9c900800000000000000
      internalID: 705362571618356652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_5
      rect:
        serializedVersion: 2
        x: 550
        y: 537
        width: 52
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d4c89d2616b157b0800000000000000
      internalID: -5237204685284653864
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_6
      rect:
        serializedVersion: 2
        x: 678
        y: 537
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b198c81d527e5f4f0800000000000000
      internalID: -795475609545570021
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_7
      rect:
        serializedVersion: 2
        x: 38
        y: 409
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32e4a3304e86b2420800000000000000
      internalID: 2606292137879948835
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_8
      rect:
        serializedVersion: 2
        x: 36
        y: 281
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1bb56814132118d20800000000000000
      internalID: 3278922006464453553
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_9
      rect:
        serializedVersion: 2
        x: 166
        y: 294
        width: 52
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a06daeae4a781a9e0800000000000000
      internalID: -1611858049236281846
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_10
      rect:
        serializedVersion: 2
        x: 173
        y: 280
        width: 35
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42819fb6b5765c040800000000000000
      internalID: 4667250231190558756
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_11
      rect:
        serializedVersion: 2
        x: 298
        y: 280
        width: 44
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de340907c25f43330800000000000000
      internalID: 3689843565939147757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_12
      rect:
        serializedVersion: 2
        x: 418
        y: 284
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c212fc93fdc295f0800000000000000
      internalID: -751311741623594299
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_13
      rect:
        serializedVersion: 2
        x: 552
        y: 284
        width: 47
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e1a0aa9497a811c0800000000000000
      internalID: -4532688768257187353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_14
      rect:
        serializedVersion: 2
        x: 678
        y: 284
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7728ae9dac1728fc0800000000000000
      internalID: -3494105244832726409
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_15
      rect:
        serializedVersion: 2
        x: 36
        y: 153
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 359d94a1c15dd85c0800000000000000
      internalID: -4211475759844697773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_16
      rect:
        serializedVersion: 2
        x: 166
        y: 152
        width: 52
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f00909b836f3567e0800000000000000
      internalID: -1772941181524602865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_17
      rect:
        serializedVersion: 2
        x: 295
        y: 152
        width: 50
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34548d2d430e1b9f0800000000000000
      internalID: -454335569907399357
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_18
      rect:
        serializedVersion: 2
        x: 56
        y: 87
        width: 10
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b19843c83017b3c00800000000000000
      internalID: 881422412132157723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_19
      rect:
        serializedVersion: 2
        x: 168
        y: 28
        width: 48
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 307d8faaadf0ae0c0800000000000000
      internalID: -4545803442031372541
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_20
      rect:
        serializedVersion: 2
        x: 184
        y: 103
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82e590e9a3a804750800000000000000
      internalID: 6287177064173362728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_21
      rect:
        serializedVersion: 2
        x: 294
        y: 28
        width: 52
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dd07684ff41e95e0800000000000000
      internalID: -1901058906039841320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_22
      rect:
        serializedVersion: 2
        x: 52
        y: 85
        width: 8
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 669f83a47082d7070800000000000000
      internalID: 8105678916111890790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_23
      rect:
        serializedVersion: 2
        x: 70
        y: 80
        width: 14
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 894f39be10e8d8060800000000000000
      internalID: 6957373138255738008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_24
      rect:
        serializedVersion: 2
        x: 332
        y: 96
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2545288b3b571de80800000000000000
      internalID: -8155608035436112814
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Red_25
      rect:
        serializedVersion: 2
        x: 34
        y: 28
        width: 59
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b8ced94d16dbd130800000000000000
      internalID: 3592700549039835320
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Barrel_Red_0: -1235903452724194183
      Barrel_Red_1: -7227994795241161344
      Barrel_Red_10: 4667250231190558756
      Barrel_Red_11: 3689843565939147757
      Barrel_Red_12: -751311741623594299
      Barrel_Red_13: -4532688768257187353
      Barrel_Red_14: -3494105244832726409
      Barrel_Red_15: -4211475759844697773
      Barrel_Red_16: -1772941181524602865
      Barrel_Red_17: -454335569907399357
      Barrel_Red_18: 881422412132157723
      Barrel_Red_19: -4545803442031372541
      Barrel_Red_2: -6761793885924507605
      Barrel_Red_20: 6287177064173362728
      Barrel_Red_21: -1901058906039841320
      Barrel_Red_22: 8105678916111890790
      Barrel_Red_23: 6957373138255738008
      Barrel_Red_24: -8155608035436112814
      Barrel_Red_25: 3592700549039835320
      Barrel_Red_3: 8838147863419733177
      Barrel_Red_4: 705362571618356652
      Barrel_Red_5: -5237204685284653864
      Barrel_Red_6: -795475609545570021
      Barrel_Red_7: 2606292137879948835
      Barrel_Red_8: 3278922006464453553
      Barrel_Red_9: -1611858049236281846
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: 3064cf1602d93f449bfcd31f5b359e34
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5897788223502500155
    second: TNT_Yellow_0
  - first:
      213: 6495355567584913369
    second: TNT_Yellow_1
  - first:
      213: 9117411345305774777
    second: TNT_Yellow_2
  - first:
      213: 2910333032568017722
    second: TNT_Yellow_3
  - first:
      213: 7489665041710281493
    second: TNT_Yellow_4
  - first:
      213: 6334247472151054579
    second: TNT_Yellow_5
  - first:
      213: -5954970504039593574
    second: TNT_Yellow_6
  - first:
      213: 3108974693858557164
    second: TNT_Yellow_7
  - first:
      213: 1755845684380980900
    second: TNT_Yellow_8
  - first:
      213: 6930382179305954324
    second: TNT_Yellow_9
  - first:
      213: -8620162259392710406
    second: TNT_Yellow_10
  - first:
      213: -8382507505125069333
    second: TNT_Yellow_11
  - first:
      213: -934563560996068288
    second: TNT_Yellow_12
  - first:
      213: -3839763707124402121
    second: TNT_Yellow_13
  - first:
      213: 4346732994931675347
    second: TNT_Yellow_14
  - first:
      213: 452591549112123371
    second: TNT_Yellow_15
  - first:
      213: -7971032670180350716
    second: TNT_Yellow_16
  - first:
      213: 1780397320674947424
    second: TNT_Yellow_17
  - first:
      213: -8724881437507540233
    second: TNT_Yellow_18
  - first:
      213: -1510895165611675020
    second: TNT_Yellow_19
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: TNT_Yellow_0
      rect:
        serializedVersion: 2
        x: 57
        y: 440
        width: 88
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ceb6a9cbd8d62ea0800000000000000
      internalID: -5897788223502500155
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_1
      rect:
        serializedVersion: 2
        x: 250
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d721cf8973242a50800000000000000
      internalID: 6495355567584913369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_2
      rect:
        serializedVersion: 2
        x: 443
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ba211cff4d878e70800000000000000
      internalID: 9117411345305774777
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_3
      rect:
        serializedVersion: 2
        x: 634
        y: 440
        width: 88
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a335d483974936820800000000000000
      internalID: 2910333032568017722
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_4
      rect:
        serializedVersion: 2
        x: 824
        y: 440
        width: 88
        height: 67
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5137dc96ca2a0f760800000000000000
      internalID: 7489665041710281493
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_5
      rect:
        serializedVersion: 2
        x: 1016
        y: 440
        width: 84
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f8caf00484c7e750800000000000000
      internalID: 6334247472151054579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_6
      rect:
        serializedVersion: 2
        x: 1099
        y: 487
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a998aca90e1bb5da0800000000000000
      internalID: -5954970504039593574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_7
      rect:
        serializedVersion: 2
        x: 59
        y: 250
        width: 82
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cec3e13130c452b20800000000000000
      internalID: 3108974693858557164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_8
      rect:
        serializedVersion: 2
        x: 252
        y: 250
        width: 82
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a65ee9d8540e5810800000000000000
      internalID: 1755845684380980900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_9
      rect:
        serializedVersion: 2
        x: 443
        y: 250
        width: 85
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41c9a339fd9ad2060800000000000000
      internalID: 6930382179305954324
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_10
      rect:
        serializedVersion: 2
        x: 633
        y: 250
        width: 87
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af83baeb8180f5880800000000000000
      internalID: -8620162259392710406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_11
      rect:
        serializedVersion: 2
        x: 824
        y: 250
        width: 84
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bed69f526d95bab80800000000000000
      internalID: -8382507505125069333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_12
      rect:
        serializedVersion: 2
        x: 1017
        y: 250
        width: 82
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0442ece8463c703f0800000000000000
      internalID: -934563560996068288
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_13
      rect:
        serializedVersion: 2
        x: 54
        y: 56
        width: 79
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 738b967904b66bac0800000000000000
      internalID: -3839763707124402121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_14
      rect:
        serializedVersion: 2
        x: 242
        y: 56
        width: 78
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d05f5001a2b25c30800000000000000
      internalID: 4346732994931675347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_15
      rect:
        serializedVersion: 2
        x: 439
        y: 56
        width: 77
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: beffb21bd9de74600800000000000000
      internalID: 452591549112123371
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_16
      rect:
        serializedVersion: 2
        x: 633
        y: 56
        width: 76
        height: 65
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 405edd0d404316190800000000000000
      internalID: -7971032670180350716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_17
      rect:
        serializedVersion: 2
        x: 824
        y: 56
        width: 75
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06133e12eed35b810800000000000000
      internalID: 1780397320674947424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_18
      rect:
        serializedVersion: 2
        x: 1014
        y: 56
        width: 66
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7faa6f91f8efae680800000000000000
      internalID: -8724881437507540233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Yellow_19
      rect:
        serializedVersion: 2
        x: 1210
        y: 56
        width: 68
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4727cd81ad8380be0800000000000000
      internalID: -1510895165611675020
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      TNT_Yellow_0: -5897788223502500155
      TNT_Yellow_1: 6495355567584913369
      TNT_Yellow_10: -8620162259392710406
      TNT_Yellow_11: -8382507505125069333
      TNT_Yellow_12: -934563560996068288
      TNT_Yellow_13: -3839763707124402121
      TNT_Yellow_14: 4346732994931675347
      TNT_Yellow_15: 452591549112123371
      TNT_Yellow_16: -7971032670180350716
      TNT_Yellow_17: 1780397320674947424
      TNT_Yellow_18: -8724881437507540233
      TNT_Yellow_19: -1510895165611675020
      TNT_Yellow_2: 9117411345305774777
      TNT_Yellow_3: 2910333032568017722
      TNT_Yellow_4: 7489665041710281493
      TNT_Yellow_5: 6334247472151054579
      TNT_Yellow_6: -5954970504039593574
      TNT_Yellow_7: 3108974693858557164
      TNT_Yellow_8: 1755845684380980900
      TNT_Yellow_9: 6930382179305954324
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

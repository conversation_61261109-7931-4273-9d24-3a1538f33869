fileFormatVersion: 2
guid: f1754f80454710241bc2f8acd46b69f5
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7965885453348062566
    second: HappySheep_All_0
  - first:
      213: 4932472319623443587
    second: HappySheep_All_1
  - first:
      213: -7625975663000953744
    second: HappySheep_All_2
  - first:
      213: -7949685157368943142
    second: HappySheep_All_3
  - first:
      213: -3512647757082788427
    second: HappySheep_All_4
  - first:
      213: -8327599238442022124
    second: HappySheep_All_5
  - first:
      213: -2192518879918380244
    second: HappySheep_All_6
  - first:
      213: 6100468968865793786
    second: HappySheep_All_7
  - first:
      213: -7412088679705387033
    second: HappySheep_All_8
  - first:
      213: -1228813952043584534
    second: HappySheep_All_9
  - first:
      213: -2120751546683228147
    second: HappySheep_All_10
  - first:
      213: 1956652923719774452
    second: HappySheep_All_11
  - first:
      213: -7238370263485564898
    second: HappySheep_All_12
  - first:
      213: -1397879521566131551
    second: HappySheep_All_13
  - first:
      213: 4517993989966707144
    second: HappySheep_All_14
  - first:
      213: 3956068124173570873
    second: HappySheep_All_15
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: HappySheep_All_0
      rect:
        serializedVersion: 2
        x: 42
        y: 169
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9abcbf926d737190800000000000000
      internalID: -7965885453348062566
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_1
      rect:
        serializedVersion: 2
        x: 170
        y: 169
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3843a9bf079a37440800000000000000
      internalID: 4932472319623443587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_2
      rect:
        serializedVersion: 2
        x: 298
        y: 169
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07cdd05e9871b2690800000000000000
      internalID: -7625975663000953744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_3
      rect:
        serializedVersion: 2
        x: 426
        y: 169
        width: 45
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: add24d6f77b0da190800000000000000
      internalID: -7949685157368943142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_4
      rect:
        serializedVersion: 2
        x: 553
        y: 169
        width: 48
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bdf10ac971904fc0800000000000000
      internalID: -3512647757082788427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_5
      rect:
        serializedVersion: 2
        x: 681
        y: 169
        width: 48
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41b89d83d9c6e6c80800000000000000
      internalID: -8327599238442022124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_6
      rect:
        serializedVersion: 2
        x: 810
        y: 169
        width: 46
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2ba027f1bb9291e0800000000000000
      internalID: -2192518879918380244
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_7
      rect:
        serializedVersion: 2
        x: 938
        y: 169
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af60a8c943839a450800000000000000
      internalID: 6100468968865793786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_8
      rect:
        serializedVersion: 2
        x: 42
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ef5772ed98f22990800000000000000
      internalID: -7412088679705387033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_9
      rect:
        serializedVersion: 2
        x: 172
        y: 41
        width: 40
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae3f56d983062fee0800000000000000
      internalID: -1228813952043584534
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_10
      rect:
        serializedVersion: 2
        x: 298
        y: 58
        width: 47
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d00061b54b39192e0800000000000000
      internalID: -2120751546683228147
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_11
      rect:
        serializedVersion: 2
        x: 426
        y: 59
        width: 47
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f0fb03a77d672b10800000000000000
      internalID: 1956652923719774452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_12
      rect:
        serializedVersion: 2
        x: 554
        y: 41
        width: 47
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e18313c88942c8b90800000000000000
      internalID: -7238370263485564898
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_13
      rect:
        serializedVersion: 2
        x: 677
        y: 41
        width: 56
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1aaf14e3bfbb99ce0800000000000000
      internalID: -1397879521566131551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_14
      rect:
        serializedVersion: 2
        x: 305
        y: 41
        width: 28
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c9b055b89323be30800000000000000
      internalID: 4517993989966707144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_All_15
      rect:
        serializedVersion: 2
        x: 434
        y: 41
        width: 26
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9339c90bff6c6e630800000000000000
      internalID: 3956068124173570873
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      HappySheep_All_0: -7965885453348062566
      HappySheep_All_1: 4932472319623443587
      HappySheep_All_10: -2120751546683228147
      HappySheep_All_11: 1956652923719774452
      HappySheep_All_12: -7238370263485564898
      HappySheep_All_13: -1397879521566131551
      HappySheep_All_14: 4517993989966707144
      HappySheep_All_15: 3956068124173570873
      HappySheep_All_2: -7625975663000953744
      HappySheep_All_3: -7949685157368943142
      HappySheep_All_4: -3512647757082788427
      HappySheep_All_5: -8327599238442022124
      HappySheep_All_6: -2192518879918380244
      HappySheep_All_7: 6100468968865793786
      HappySheep_All_8: -7412088679705387033
      HappySheep_All_9: -1228813952043584534
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

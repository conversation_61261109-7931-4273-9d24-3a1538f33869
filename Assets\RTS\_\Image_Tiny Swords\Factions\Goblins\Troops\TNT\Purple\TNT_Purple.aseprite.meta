fileFormatVersion: 2
guid: a78e96d5f086e7e4b91e82ffec2b7966
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44705883, y: -0.826087}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 85
      height: 69
    spriteID: f7e147a20eb929843a63c453137c54b2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.44186047, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 4
      width: 86
      height: 68
    spriteID: 969b6580d52c33243919b9148ef084ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 4}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44578314, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 380
      y: 4
      width: 83
      height: 68
    spriteID: e0731ce1dec8af141b0a2ba4f55ece0a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 380, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.43373492, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 88
      y: 81
      width: 83
      height: 68
    spriteID: 9a2070e1084714e4bb44819ebb442ecc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 88, y: 81}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.4302326, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 286
      y: 4
      width: 86
      height: 66
    spriteID: 4cb5226ad54ffc64997fcbbdad776f37
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 286, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.4534884, y: -0.8769231}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 179
      y: 81
      width: 86
      height: 65
    spriteID: 825d8fb800e62d34f998c5fc9ed77bd2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 179, y: 81}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.44827586, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 191
      y: 4
      width: 87
      height: 66
    spriteID: d1337a09b835835409f73a75ce86d3e7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 191, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.45000002, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 164
      width: 80
      height: 64
    spriteID: fe93ce977d2930c4fa6f19e3ddf39868
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 164}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.4375, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 236
      width: 80
      height: 67
    spriteID: 88ba0c59405387d419fc50b05c98a7e4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 236}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.43373492, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 185
      y: 164
      width: 83
      height: 60
    spriteID: 9afe3e4bedb0b404bbe67b8acd79fc9f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 185, y: 164}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44705883, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 85
      height: 64
    spriteID: aa547b32ddaedc14e969ab2fdd9ab182
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47560975, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 358
      y: 81
      width: 82
      height: 67
    spriteID: c319d4e40ee06f74bba92405893838df
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 358, y: 81}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.475, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 276
      y: 164
      width: 80
      height: 60
    spriteID: 246c9933441e97e4990277d8c403b52a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 276, y: 164}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5324675, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 81
      width: 77
      height: 72
    spriteID: ab9640509fcd987499f3166f1b99da9f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 81}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.59210527, y: -0.76}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 81
      width: 76
      height: 75
    spriteID: 79bf5838821191a4eb0cc37339557fb7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 81}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.53333336, y: -0.95}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 231
      y: 311
      width: 75
      height: 60
    spriteID: d3abd0cae98507340ad0be30647246ab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 231, y: 311}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.51351345, y: -0.9047619}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 364
      y: 164
      width: 74
      height: 63
    spriteID: 3872b1ae5a449794ba0c7af0b431343d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 364, y: 164}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.53424656, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 76
      y: 311
      width: 73
      height: 68
    spriteID: d72985aeefc421a439658d9969cc21c9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 76, y: 311}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.640625, y: -0.7037037}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 311
      width: 64
      height: 81
    spriteID: 6866cb71f6a903d47b2499e65208dc53
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 311}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5606061, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 157
      y: 311
      width: 66
      height: 72
    spriteID: ceecdca4531737c4bbd6097466147505
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 157, y: 311}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 481
      y: 164
      width: 27
      height: 51
    spriteID: 918df552608c7274c8e2d5a7a366a305
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 481, y: 164}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.44444445, y: -1.4423077}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 446
      y: 164
      width: 27
      height: 52
    spriteID: cf099e4dd9de7844db4d9fd82d73888d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 446, y: 164}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.46153846, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 314
      y: 311
      width: 26
      height: 51
    spriteID: 9d07485a5bd0f3b419847229e3a744b6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 314, y: 311}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.48, y: -1.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 483
      y: 81
      width: 25
      height: 50
    spriteID: b64bb5d2149c0a84594b44f61001b0c5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 483, y: 81}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 448
      y: 81
      width: 27
      height: 51
    spriteID: 1a7108c4a2ce732449d94ee0d9046e89
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 448, y: 81}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.42857143, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 471
      y: 4
      width: 28
      height: 51
    spriteID: e3235bd0d850ff54a8c7b56066c5b950
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 471, y: 4}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2876978351
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: TNT_Purple
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 85
        height: 69
      spriteId: f7e147a20eb929843a63c453137c54b2
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 86
        height: 68
      spriteId: 969b6580d52c33243919b9148ef084ca
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 83
        height: 68
      spriteId: e0731ce1dec8af141b0a2ba4f55ece0a
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 57
        width: 83
        height: 68
      spriteId: 9a2070e1084714e4bb44819ebb442ecc
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 86
        height: 66
      spriteId: 4cb5226ad54ffc64997fcbbdad776f37
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 86
        height: 65
      spriteId: 825d8fb800e62d34f998c5fc9ed77bd2
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 87
        height: 66
      spriteId: d1337a09b835835409f73a75ce86d3e7
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 80
        height: 64
      spriteId: fe93ce977d2930c4fa6f19e3ddf39868
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 59
        width: 80
        height: 67
      spriteId: 88ba0c59405387d419fc50b05c98a7e4
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 83
        height: 60
      spriteId: 9afe3e4bedb0b404bbe67b8acd79fc9f
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 85
        height: 64
      spriteId: aa547b32ddaedc14e969ab2fdd9ab182
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 59
        width: 82
        height: 67
      spriteId: c319d4e40ee06f74bba92405893838df
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 80
        height: 60
      spriteId: 246c9933441e97e4990277d8c403b52a
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 77
        height: 72
      spriteId: ab9640509fcd987499f3166f1b99da9f
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 57
        width: 76
        height: 75
      spriteId: 79bf5838821191a4eb0cc37339557fb7
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 57
        width: 75
        height: 60
      spriteId: d3abd0cae98507340ad0be30647246ab
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 74
        height: 63
      spriteId: 3872b1ae5a449794ba0c7af0b431343d
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 73
        height: 68
      spriteId: d72985aeefc421a439658d9969cc21c9
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 64
        height: 81
      spriteId: 6866cb71f6a903d47b2499e65208dc53
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 66
        height: 72
      spriteId: ceecdca4531737c4bbd6097466147505
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: 918df552608c7274c8e2d5a7a366a305
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 52
      spriteId: cf099e4dd9de7844db4d9fd82d73888d
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 26
        height: 51
      spriteId: 9d07485a5bd0f3b419847229e3a744b6
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 25
        height: 50
      spriteId: b64bb5d2149c0a84594b44f61001b0c5
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: 1a7108c4a2ce732449d94ee0d9046e89
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 28
        height: 51
      spriteId: e3235bd0d850ff54a8c7b56066c5b950
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

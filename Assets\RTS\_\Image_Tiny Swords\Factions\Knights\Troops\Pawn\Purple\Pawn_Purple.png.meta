fileFormatVersion: 2
guid: 3cd49fea45e86114a802eda5c692b3e9
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7151335025817718036
    second: Pawn_Purple_0
  - first:
      213: -3865839798968672670
    second: Pawn_Purple_1
  - first:
      213: 6771056470937026359
    second: Pawn_Purple_2
  - first:
      213: -4853929738237644547
    second: Pawn_Purple_3
  - first:
      213: 4568895902392004395
    second: Pawn_Purple_4
  - first:
      213: 3284034557990988991
    second: Pawn_Purple_5
  - first:
      213: 4761863107256100790
    second: Pawn_Purple_6
  - first:
      213: 7531144225999517546
    second: Pawn_Purple_7
  - first:
      213: -2617460027535299998
    second: Pawn_Purple_8
  - first:
      213: -3063927701526178213
    second: Pawn_Purple_9
  - first:
      213: -3731301376949449952
    second: Pawn_Purple_10
  - first:
      213: -6172527571848726407
    second: Pawn_Purple_11
  - first:
      213: 8196110310354184791
    second: Pawn_Purple_12
  - first:
      213: -7486086201510711056
    second: Pawn_Purple_13
  - first:
      213: -8418445176967834016
    second: Pawn_Purple_14
  - first:
      213: -7400455970905636487
    second: Pawn_Purple_15
  - first:
      213: 6437052481192889849
    second: Pawn_Purple_16
  - first:
      213: 4912451503480231519
    second: Pawn_Purple_17
  - first:
      213: -270537393679614710
    second: Pawn_Purple_18
  - first:
      213: 1637584025718678340
    second: Pawn_Purple_19
  - first:
      213: -3248387377611410972
    second: Pawn_Purple_20
  - first:
      213: 8252961560705683489
    second: Pawn_Purple_21
  - first:
      213: -3192857241777885443
    second: Pawn_Purple_22
  - first:
      213: 5375620822502470317
    second: Pawn_Purple_23
  - first:
      213: -8701289565642555058
    second: Pawn_Purple_24
  - first:
      213: 679294208129956716
    second: Pawn_Purple_25
  - first:
      213: -3829936944210463740
    second: Pawn_Purple_26
  - first:
      213: -234185425200715177
    second: Pawn_Purple_27
  - first:
      213: -9174161106731178513
    second: Pawn_Purple_28
  - first:
      213: 6062053083740925302
    second: Pawn_Purple_29
  - first:
      213: -5631751580675010236
    second: Pawn_Purple_30
  - first:
      213: 7287202921120189195
    second: Pawn_Purple_31
  - first:
      213: -5086714982081308554
    second: Pawn_Purple_32
  - first:
      213: 850815460180444741
    second: Pawn_Purple_33
  - first:
      213: -1401859000641279537
    second: Pawn_Purple_34
  - first:
      213: 6013456965159996525
    second: Pawn_Purple_35
  - first:
      213: 4340158914627717868
    second: Pawn_Purple_36
  - first:
      213: -638862258060804151
    second: Pawn_Purple_37
  - first:
      213: 8664554167700266599
    second: Pawn_Purple_38
  - first:
      213: 5591179768726034879
    second: Pawn_Purple_39
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Pawn_Purple_0
      rect:
        serializedVersion: 2
        x: 66
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce27354feaa51cc90800000000000000
      internalID: -7151335025817718036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_1
      rect:
        serializedVersion: 2
        x: 257
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26e97e75f27c95ac0800000000000000
      internalID: -3865839798968672670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_2
      rect:
        serializedVersion: 2
        x: 449
        y: 1023
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73fbf485cff97fd50800000000000000
      internalID: 6771056470937026359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_3
      rect:
        serializedVersion: 2
        x: 642
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df0f3abce9063acb0800000000000000
      internalID: -4853929738237644547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_4
      rect:
        serializedVersion: 2
        x: 835
        y: 1023
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2f20ddbd9af76f30800000000000000
      internalID: 4568895902392004395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_5
      rect:
        serializedVersion: 2
        x: 1027
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbce82be70c339d20800000000000000
      internalID: 3284034557990988991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_6
      rect:
        serializedVersion: 2
        x: 64
        y: 831
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b7a9014449851240800000000000000
      internalID: 4761863107256100790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_7
      rect:
        serializedVersion: 2
        x: 257
        y: 831
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a677fe695cff38860800000000000000
      internalID: 7531144225999517546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_8
      rect:
        serializedVersion: 2
        x: 450
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26a69fc09f9ecabd0800000000000000
      internalID: -2617460027535299998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_9
      rect:
        serializedVersion: 2
        x: 641
        y: 831
        width: 64
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b569d052dfdba75d0800000000000000
      internalID: -3063927701526178213
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_10
      rect:
        serializedVersion: 2
        x: 834
        y: 831
        width: 61
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02b800dba21c73cc0800000000000000
      internalID: -3731301376949449952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_11
      rect:
        serializedVersion: 2
        x: 1026
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 978f6a970e6c65aa0800000000000000
      internalID: -6172527571848726407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_12
      rect:
        serializedVersion: 2
        x: 63
        y: 639
        width: 67
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75ae39407ee6eb170800000000000000
      internalID: 8196110310354184791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_13
      rect:
        serializedVersion: 2
        x: 254
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f0300143441c1890800000000000000
      internalID: -7486086201510711056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_14
      rect:
        serializedVersion: 2
        x: 446
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0665bb1e6bcab2b80800000000000000
      internalID: -8418445176967834016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_15
      rect:
        serializedVersion: 2
        x: 639
        y: 639
        width: 94
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9714046f08c4c4990800000000000000
      internalID: -7400455970905636487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_16
      rect:
        serializedVersion: 2
        x: 679
        y: 696
        width: 24
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fd8c0e6021055950800000000000000
      internalID: 6437052481192889849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_17
      rect:
        serializedVersion: 2
        x: 832
        y: 639
        width: 69
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f56d5497d988c2440800000000000000
      internalID: 4912451503480231519
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_18
      rect:
        serializedVersion: 2
        x: 884
        y: 642
        width: 39
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0d344f7cabde3cf0800000000000000
      internalID: -270537393679614710
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_19
      rect:
        serializedVersion: 2
        x: 1029
        y: 639
        width: 78
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 443e0debefdd9b610800000000000000
      internalID: 1637584025718678340
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_20
      rect:
        serializedVersion: 2
        x: 42
        y: 447
        width: 83
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e1fb9804e86be2d0800000000000000
      internalID: -3248387377611410972
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_21
      rect:
        serializedVersion: 2
        x: 228
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 120d0da30d8688270800000000000000
      internalID: 8252961560705683489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_22
      rect:
        serializedVersion: 2
        x: 420
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfeab4d4141b0b3d0800000000000000
      internalID: -3192857241777885443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_23
      rect:
        serializedVersion: 2
        x: 628
        y: 473
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da2f37428aa0a9a40800000000000000
      internalID: 5375620822502470317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_24
      rect:
        serializedVersion: 2
        x: 632
        y: 447
        width: 108
        height: 56
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e410010dd3fce3780800000000000000
      internalID: -8701289565642555058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_25
      rect:
        serializedVersion: 2
        x: 830
        y: 461
        width: 11
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6bf095bb765d6900800000000000000
      internalID: 679294208129956716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_26
      rect:
        serializedVersion: 2
        x: 841
        y: 447
        width: 90
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 404b03024a459dac0800000000000000
      internalID: -3829936944210463740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_27
      rect:
        serializedVersion: 2
        x: 1018
        y: 439
        width: 71
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75a6657b89100ccf0800000000000000
      internalID: -234185425200715177
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_28
      rect:
        serializedVersion: 2
        x: 66
        y: 255
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe565198415dea080800000000000000
      internalID: -9174161106731178513
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_29
      rect:
        serializedVersion: 2
        x: 257
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67d60f9592db02450800000000000000
      internalID: 6062053083740925302
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_30
      rect:
        serializedVersion: 2
        x: 449
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4411f9296cff7d1b0800000000000000
      internalID: -5631751580675010236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_31
      rect:
        serializedVersion: 2
        x: 642
        y: 255
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0bce8f8278512560800000000000000
      internalID: 7287202921120189195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_32
      rect:
        serializedVersion: 2
        x: 835
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 678d263acab5869b0800000000000000
      internalID: -5086714982081308554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_33
      rect:
        serializedVersion: 2
        x: 1027
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 54647793724becb00800000000000000
      internalID: 850815460180444741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_34
      rect:
        serializedVersion: 2
        x: 64
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc9a7919aa89b8ce0800000000000000
      internalID: -1401859000641279537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_35
      rect:
        serializedVersion: 2
        x: 257
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6cc578cf37147350800000000000000
      internalID: 6013456965159996525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_36
      rect:
        serializedVersion: 2
        x: 450
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cea4127a9875b3c30800000000000000
      internalID: 4340158914627717868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_37
      rect:
        serializedVersion: 2
        x: 644
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9cfb1ef392e4227f0800000000000000
      internalID: -638862258060804151
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_38
      rect:
        serializedVersion: 2
        x: 835
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76e97e6db1eae3870800000000000000
      internalID: 8664554167700266599
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Purple_39
      rect:
        serializedVersion: 2
        x: 1026
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb91446506cd79d40800000000000000
      internalID: 5591179768726034879
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Pawn_Purple_0: -7151335025817718036
      Pawn_Purple_1: -3865839798968672670
      Pawn_Purple_10: -3731301376949449952
      Pawn_Purple_11: -6172527571848726407
      Pawn_Purple_12: 8196110310354184791
      Pawn_Purple_13: -7486086201510711056
      Pawn_Purple_14: -8418445176967834016
      Pawn_Purple_15: -7400455970905636487
      Pawn_Purple_16: 6437052481192889849
      Pawn_Purple_17: 4912451503480231519
      Pawn_Purple_18: -270537393679614710
      Pawn_Purple_19: 1637584025718678340
      Pawn_Purple_2: 6771056470937026359
      Pawn_Purple_20: -3248387377611410972
      Pawn_Purple_21: 8252961560705683489
      Pawn_Purple_22: -3192857241777885443
      Pawn_Purple_23: 5375620822502470317
      Pawn_Purple_24: -8701289565642555058
      Pawn_Purple_25: 679294208129956716
      Pawn_Purple_26: -3829936944210463740
      Pawn_Purple_27: -234185425200715177
      Pawn_Purple_28: -9174161106731178513
      Pawn_Purple_29: 6062053083740925302
      Pawn_Purple_3: -4853929738237644547
      Pawn_Purple_30: -5631751580675010236
      Pawn_Purple_31: 7287202921120189195
      Pawn_Purple_32: -5086714982081308554
      Pawn_Purple_33: 850815460180444741
      Pawn_Purple_34: -1401859000641279537
      Pawn_Purple_35: 6013456965159996525
      Pawn_Purple_36: 4340158914627717868
      Pawn_Purple_37: -638862258060804151
      Pawn_Purple_38: 8664554167700266599
      Pawn_Purple_39: 5591179768726034879
      Pawn_Purple_4: 4568895902392004395
      Pawn_Purple_5: 3284034557990988991
      Pawn_Purple_6: 4761863107256100790
      Pawn_Purple_7: 7531144225999517546
      Pawn_Purple_8: -2617460027535299998
      Pawn_Purple_9: -3063927701526178213
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

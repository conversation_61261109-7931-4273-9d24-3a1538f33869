fileFormatVersion: 2
guid: 7d963e9cb3e709048b07265c77acec48
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4395808749404051810
    second: Pawn_Yellow_0
  - first:
      213: -9087515697109334980
    second: Pawn_Yellow_1
  - first:
      213: 8592677695197669246
    second: Pawn_Yellow_2
  - first:
      213: -5003277896764827871
    second: Pawn_Yellow_3
  - first:
      213: -4118437947477486848
    second: Pawn_Yellow_4
  - first:
      213: -7270609572154940968
    second: Pawn_Yellow_5
  - first:
      213: 2822699327164071804
    second: Pawn_Yellow_6
  - first:
      213: -1342357598996002271
    second: Pawn_Yellow_7
  - first:
      213: 8594182011973782910
    second: Pawn_Yellow_8
  - first:
      213: -2237012461462855798
    second: Pawn_Yellow_9
  - first:
      213: -467210078447886118
    second: Pawn_Yellow_10
  - first:
      213: -5625628886745068247
    second: Pawn_Yellow_11
  - first:
      213: 5999807969417979040
    second: Pawn_Yellow_12
  - first:
      213: -7225809559945409280
    second: Pawn_Yellow_13
  - first:
      213: -5027556623335025101
    second: Pawn_Yellow_14
  - first:
      213: 7061088719577547973
    second: Pawn_Yellow_15
  - first:
      213: 7971543263634557757
    second: Pawn_Yellow_16
  - first:
      213: -2361462099429993445
    second: Pawn_Yellow_17
  - first:
      213: 4189249671110922131
    second: Pawn_Yellow_18
  - first:
      213: -2839513072367789732
    second: Pawn_Yellow_19
  - first:
      213: -3450361000477017983
    second: Pawn_Yellow_20
  - first:
      213: 4884206483115173913
    second: Pawn_Yellow_21
  - first:
      213: 1659224078882008886
    second: Pawn_Yellow_22
  - first:
      213: 1968107888784983274
    second: Pawn_Yellow_23
  - first:
      213: -5353022244876327398
    second: Pawn_Yellow_24
  - first:
      213: 5515166622349800880
    second: Pawn_Yellow_25
  - first:
      213: -9121767028776928626
    second: Pawn_Yellow_26
  - first:
      213: -5299919123602252656
    second: Pawn_Yellow_27
  - first:
      213: 2879458841118628245
    second: Pawn_Yellow_28
  - first:
      213: -7807918204438299798
    second: Pawn_Yellow_29
  - first:
      213: -4407209655405436420
    second: Pawn_Yellow_30
  - first:
      213: 2734341331503122054
    second: Pawn_Yellow_31
  - first:
      213: -7918056851071978976
    second: Pawn_Yellow_32
  - first:
      213: -6377858246066441742
    second: Pawn_Yellow_33
  - first:
      213: 2328372838046930906
    second: Pawn_Yellow_34
  - first:
      213: -487463906889501766
    second: Pawn_Yellow_35
  - first:
      213: -1404305167985753339
    second: Pawn_Yellow_36
  - first:
      213: 6767447540326418551
    second: Pawn_Yellow_37
  - first:
      213: 2645291163241951748
    second: Pawn_Yellow_38
  - first:
      213: -368434098431560332
    second: Pawn_Yellow_39
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Pawn_Yellow_0
      rect:
        serializedVersion: 2
        x: 66
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e96a73f7b33fef2c0800000000000000
      internalID: -4395808749404051810
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_1
      rect:
        serializedVersion: 2
        x: 257
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3403070f98a2e180800000000000000
      internalID: -9087515697109334980
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_2
      rect:
        serializedVersion: 2
        x: 449
        y: 1023
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e77edab76d25f3770800000000000000
      internalID: 8592677695197669246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_3
      rect:
        serializedVersion: 2
        x: 642
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 127d4cae249c09ab0800000000000000
      internalID: -5003277896764827871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_4
      rect:
        serializedVersion: 2
        x: 835
        y: 1023
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0039d17d18e58d6c0800000000000000
      internalID: -4118437947477486848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_5
      rect:
        serializedVersion: 2
        x: 1027
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d1662f8d1b991b90800000000000000
      internalID: -7270609572154940968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_6
      rect:
        serializedVersion: 2
        x: 64
        y: 831
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7f395f251e3c2720800000000000000
      internalID: 2822699327164071804
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_7
      rect:
        serializedVersion: 2
        x: 257
        y: 831
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12a0c4630ecfe5de0800000000000000
      internalID: -1342357598996002271
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_8
      rect:
        serializedVersion: 2
        x: 450
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7dcfb6810ba44770800000000000000
      internalID: 8594182011973782910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_9
      rect:
        serializedVersion: 2
        x: 641
        y: 831
        width: 64
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8f526cb40984f0e0800000000000000
      internalID: -2237012461462855798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_10
      rect:
        serializedVersion: 2
        x: 834
        y: 831
        width: 61
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad0a1a6b8e22489f0800000000000000
      internalID: -467210078447886118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_11
      rect:
        serializedVersion: 2
        x: 1026
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92d42675550cde1b0800000000000000
      internalID: -5625628886745068247
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_12
      rect:
        serializedVersion: 2
        x: 63
        y: 639
        width: 67
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a467b15f89934350800000000000000
      internalID: 5999807969417979040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_13
      rect:
        serializedVersion: 2
        x: 254
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00d05dc3d74c8bb90800000000000000
      internalID: -7225809559945409280
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_14
      rect:
        serializedVersion: 2
        x: 446
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33614f163e78a3ab0800000000000000
      internalID: -5027556623335025101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_15
      rect:
        serializedVersion: 2
        x: 639
        y: 639
        width: 94
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c400f977c60ef160800000000000000
      internalID: 7061088719577547973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_16
      rect:
        serializedVersion: 2
        x: 679
        y: 696
        width: 24
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3b889cfc5c90ae60800000000000000
      internalID: 7971543263634557757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_17
      rect:
        serializedVersion: 2
        x: 832
        y: 639
        width: 69
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b145dec6bb66a3fd0800000000000000
      internalID: -2361462099429993445
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_18
      rect:
        serializedVersion: 2
        x: 884
        y: 642
        width: 39
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 39f0df95164332a30800000000000000
      internalID: 4189249671110922131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_19
      rect:
        serializedVersion: 2
        x: 1029
        y: 639
        width: 78
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c599072b7e50898d0800000000000000
      internalID: -2839513072367789732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_20
      rect:
        serializedVersion: 2
        x: 42
        y: 447
        width: 83
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 188b4bb63fadd10d0800000000000000
      internalID: -3450361000477017983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_21
      rect:
        serializedVersion: 2
        x: 228
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9186db33bef28c340800000000000000
      internalID: 4884206483115173913
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_22
      rect:
        serializedVersion: 2
        x: 420
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63f6980428fb60710800000000000000
      internalID: 1659224078882008886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_23
      rect:
        serializedVersion: 2
        x: 628
        y: 473
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae44ca682bf105b10800000000000000
      internalID: 1968107888784983274
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_24
      rect:
        serializedVersion: 2
        x: 632
        y: 447
        width: 108
        height: 56
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a16e16b31ae36b5b0800000000000000
      internalID: -5353022244876327398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_25
      rect:
        serializedVersion: 2
        x: 830
        y: 461
        width: 11
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b5304a82dec98c40800000000000000
      internalID: 5515166622349800880
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_26
      rect:
        serializedVersion: 2
        x: 841
        y: 447
        width: 90
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8a71ed6739f86180800000000000000
      internalID: -9121767028776928626
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_27
      rect:
        serializedVersion: 2
        x: 1018
        y: 439
        width: 71
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09c536323a7e276b0800000000000000
      internalID: -5299919123602252656
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_28
      rect:
        serializedVersion: 2
        x: 66
        y: 255
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 599db3a5f84e5f720800000000000000
      internalID: 2879458841118628245
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_29
      rect:
        serializedVersion: 2
        x: 257
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6b362dd2c3b4a390800000000000000
      internalID: -7807918204438299798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_30
      rect:
        serializedVersion: 2
        x: 449
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf123e63b2276d2c0800000000000000
      internalID: -4407209655405436420
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_31
      rect:
        serializedVersion: 2
        x: 642
        y: 255
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68689ee24f452f520800000000000000
      internalID: 2734341331503122054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_32
      rect:
        serializedVersion: 2
        x: 835
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 022b180ee396d1290800000000000000
      internalID: -7918056851071978976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_33
      rect:
        serializedVersion: 2
        x: 1027
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f1f96f67bb4d77a0800000000000000
      internalID: -6377858246066441742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_34
      rect:
        serializedVersion: 2
        x: 64
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad7947c82ca005020800000000000000
      internalID: 2328372838046930906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_35
      rect:
        serializedVersion: 2
        x: 257
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab3f60dc82e2c39f0800000000000000
      internalID: -487463906889501766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_36
      rect:
        serializedVersion: 2
        x: 450
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 503c6f9d3e7e28ce0800000000000000
      internalID: -1404305167985753339
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_37
      rect:
        serializedVersion: 2
        x: 644
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7784f35beadcaed50800000000000000
      internalID: 6767447540326418551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_38
      rect:
        serializedVersion: 2
        x: 835
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40234803c46f5b420800000000000000
      internalID: 2645291163241951748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Yellow_39
      rect:
        serializedVersion: 2
        x: 1026
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47578d7b32f03eaf0800000000000000
      internalID: -368434098431560332
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Pawn_Yellow_0: -4395808749404051810
      Pawn_Yellow_1: -9087515697109334980
      Pawn_Yellow_10: -467210078447886118
      Pawn_Yellow_11: -5625628886745068247
      Pawn_Yellow_12: 5999807969417979040
      Pawn_Yellow_13: -7225809559945409280
      Pawn_Yellow_14: -5027556623335025101
      Pawn_Yellow_15: 7061088719577547973
      Pawn_Yellow_16: 7971543263634557757
      Pawn_Yellow_17: -2361462099429993445
      Pawn_Yellow_18: 4189249671110922131
      Pawn_Yellow_19: -2839513072367789732
      Pawn_Yellow_2: 8592677695197669246
      Pawn_Yellow_20: -3450361000477017983
      Pawn_Yellow_21: 4884206483115173913
      Pawn_Yellow_22: 1659224078882008886
      Pawn_Yellow_23: 1968107888784983274
      Pawn_Yellow_24: -5353022244876327398
      Pawn_Yellow_25: 5515166622349800880
      Pawn_Yellow_26: -9121767028776928626
      Pawn_Yellow_27: -5299919123602252656
      Pawn_Yellow_28: 2879458841118628245
      Pawn_Yellow_29: -7807918204438299798
      Pawn_Yellow_3: -5003277896764827871
      Pawn_Yellow_30: -4407209655405436420
      Pawn_Yellow_31: 2734341331503122054
      Pawn_Yellow_32: -7918056851071978976
      Pawn_Yellow_33: -6377858246066441742
      Pawn_Yellow_34: 2328372838046930906
      Pawn_Yellow_35: -487463906889501766
      Pawn_Yellow_36: -1404305167985753339
      Pawn_Yellow_37: 6767447540326418551
      Pawn_Yellow_38: 2645291163241951748
      Pawn_Yellow_39: -368434098431560332
      Pawn_Yellow_4: -4118437947477486848
      Pawn_Yellow_5: -7270609572154940968
      Pawn_Yellow_6: 2822699327164071804
      Pawn_Yellow_7: -1342357598996002271
      Pawn_Yellow_8: 8594182011973782910
      Pawn_Yellow_9: -2237012461462855798
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

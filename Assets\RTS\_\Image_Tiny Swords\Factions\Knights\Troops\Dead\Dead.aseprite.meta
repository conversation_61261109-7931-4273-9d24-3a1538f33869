fileFormatVersion: 2
guid: af6ec47acf234a746be35f8c8771a48b
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_1
    originalName: 
    pivot: {x: 0.48214287, y: -0.453125}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 79
      width: 56
      height: 64
    spriteID: ba014c000a6696e418478e54eda24672
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 79}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.48333335, y: -0.43283582}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 60
      height: 67
    spriteID: 8ed6e1eaf55d409448867bffbbe68b10
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.4871795, y: -0.5178572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 151
      width: 39
      height: 56
    spriteID: 68fdca5ac6c770843b57c872c60e330b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 151}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.47272727, y: -0.8787879}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 215
      width: 55
      height: 33
    spriteID: d2be453a5203b044aadac0f0ef50fbfc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 215}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.42105263, y: -0.6744186}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 209
      width: 38
      height: 43
    spriteID: 146a098c4c5298547aa8ca2815ef7b2b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 209}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.4390244, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 113
      width: 41
      height: 40
    spriteID: fa501ea60ce4c2b4d93fee3e08dec10a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 113}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.4468085, y: -0.85294116}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 4
      width: 47
      height: 34
    spriteID: 9744bc1e4d9faa54c90d34f796b3cbd3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 4}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.41025642, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 56
      width: 39
      height: 49
    spriteID: 07a91494da9688747bf9e5691fe5116f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 56}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.4390244, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 161
      width: 41
      height: 40
    spriteID: 1cff593d7fa2f8e41825f267cb3f09cf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 161}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48780486, y: -0.9117647}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 46
      width: 41
      height: 34
    spriteID: b6d805255722a3f4fa9c822de513ebef
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 46}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.48780486, y: -1.1481482}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 88
      width: 41
      height: 27
    spriteID: cd2364951d9dae84186336b189cd1c3c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 88}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.46875, y: -1.8235294}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 123
      width: 32
      height: 17
    spriteID: d0a41019e7d1ad54386f28f342e13a97
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 123}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.48, y: -3.6666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 148
      width: 25
      height: 9
    spriteID: 1c41445d9c04b9f4cac04d26159d9877
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 148}
  - name: Frame_0
    originalName: 
    pivot: {x: 0.47727275, y: -0.97727275}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 4
      width: 44
      height: 44
    spriteID: fefbf2717fcfea84b9f08ef0bb9a836f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 4}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 4136222740
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Dead
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 56
        height: 64
      spriteId: ba014c000a6696e418478e54eda24672
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 60
        height: 67
      spriteId: 8ed6e1eaf55d409448867bffbbe68b10
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 45
        y: 29
        width: 39
        height: 56
      spriteId: 68fdca5ac6c770843b57c872c60e330b
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 29
        width: 55
        height: 33
      spriteId: d2be453a5203b044aadac0f0ef50fbfc
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 29
        width: 38
        height: 43
      spriteId: 146a098c4c5298547aa8ca2815ef7b2b
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 29
        width: 41
        height: 40
      spriteId: fa501ea60ce4c2b4d93fee3e08dec10a
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 29
        width: 47
        height: 34
      spriteId: 9744bc1e4d9faa54c90d34f796b3cbd3
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 29
        width: 39
        height: 49
      spriteId: 07a91494da9688747bf9e5691fe5116f
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 29
        width: 41
        height: 40
      spriteId: 1cff593d7fa2f8e41825f267cb3f09cf
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 44
        y: 31
        width: 41
        height: 34
      spriteId: b6d805255722a3f4fa9c822de513ebef
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 44
        y: 31
        width: 41
        height: 27
      spriteId: cd2364951d9dae84186336b189cd1c3c
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 49
        y: 31
        width: 32
        height: 17
      spriteId: d0a41019e7d1ad54386f28f342e13a97
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 52
        y: 33
        width: 25
        height: 9
      spriteId: 1c41445d9c04b9f4cac04d26159d9877
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 43
        width: 44
        height: 44
      spriteId: fefbf2717fcfea84b9f08ef0bb9a836f
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 256, y: 256}

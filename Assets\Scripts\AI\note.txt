# Manhattan Distance

d = |x1 - x2| + |y1 - y2|

if(dstX > dstY)
{
    return 14 * dstY + 10 * (dstX - dstY)
}

return 14 * dstX + 10 * (dstY - dstX)



----------- Explane ------------
start        end
(0,0)  ->   (4,4)

- dstX = |x1 - y1| = |0 - 4| = 4
- dstY = |y1 - y2| = |0 - 4| = 4

10 -> orthogonal Move
14 -> diagonal Move

return 14 * dstX + 10 * (dstY - dstX)

14 * 4 + 10 * (4 - 4) = 14 * 4 + 10 * 0 


------ A* ----------

gCost -> cost from start node to candidate node
hCost -> cost from candidate to end node
fCost -> gCost + hCost

-> Create Open List     (OL) = []
-> Create Close List    (CL) = []
-> Start Node           (SN) = (0, 0)
-> End Node             (EN) = (4, 4)



-> Add SN to OL
OL = [(0,0)]

g(0, 0) = 0
h(0, 0) = (0, 0) -> (4, 4) = dstX = |0 - 4| = 4; dstY = |0 - 4| = 4

14 * dstX + 10 * (dstY - dstX)
h = 14 * 4 + 10 * (4 - 4) = 14 * 4 + 10 * 0  
f = 56


OL = []
CL = [(0, 0)]
**
* السطور الاتية تعنى باختصار اننى ابحث عن الطرق الاقرب للمسار وتحديد تكلفة كلا منها
* اذا كان الطريق بشكل افقى او رأسى تكون التكلفة 10
* اذا كان الطريق بشكل تقاطع تكون التكلفة 14
**

    Active Node Is (0,0)
--- (1, 0) -> Set Parent to (0,0)
g(1,0) = 10 
h(1,0) = dstX = 3; dstY = 4; h = 14 * 3 + 10 * 1 = 52
f(1,0) = 10 + 52 = 62

---(1,1) -> Set Parent to (0,0)
g(1,1) = 14
h(1,1) = dstX = 3; dstY = 3; h = 14 * 3 + 10 * 0 = 42
f(1,1) = 14 + 42 = 56

---(0,1) -> Set Parent to (0,0)
g(0,1) = 10
h(0,1) = dstX = 4; dstY = 3; 
// x larger than y return 14 * dstY + 10 * (dstX - dstY)
h(0,1) = 14 * 3 + 10 * 1 = 52
f(0,1) = 10 + 52 = 62

-> Add All To Neighbours to OL

OL = [(1, 0), (1, 1), (0, 1)]
CL = [(0, 0)]


-> Choose one with smallest F and move it to CL

OL = [(1, 0), (0, 1)]
CL = [(0, 0), (1, 1)]

------------------------------------------------------------------------------------

-> Choose (1,1) as Active Node

-- (1,2) -> Set Parent (1,1) end node it's (4,4)
g(1,2) = g(1,1) + Orthogonal Move = 14 + 10 = 24
h(1,2) = 14 * 2 + 10 * 1 = 38
f(1,2) = 24 + 38 = 62

-- (2,2) -> Set Parent (1,1) end node it's (4,4)
g(2,2) = g(1,1) + Diagonal Move = 14 + 14 = 28
h(2,2) = 28
f(2,2) = 56

-- (2,0) -> Set Parent (1,1)
g(2,0) = g(1,1) + Diagonal Move = 14 + 14 = 28
h(2,0) = 48
f(2,0) = 76



---- (0,1) -> Already In OL -> it's has Parent (0,0)

what about parinting? Set it to (1,1) ? NOT YET! First compute "new" or "tentative" G for (0,1), Because (0,1) has already gCost

tg(0,1) = g(1,1) + Orthogonal Move = 14 + 10 = 24;
 
Compare tg(0,1) with Existing g(0,1);  tg(0,1) = 24; g(0,1) = 10; Compare tg > g ->  24 > 10 ? NO Action Needed 









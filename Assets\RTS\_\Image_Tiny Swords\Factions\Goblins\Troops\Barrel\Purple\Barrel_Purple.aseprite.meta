fileFormatVersion: 2
guid: c15fc8de46dd12344af25c7a487ff01f
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 321
      width: 50
      height: 70
    spriteID: f921f33ffdb5fbf4d989d9a6a29c2704
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 321}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 399
      width: 50
      height: 70
    spriteID: 97d168d87854c564cb46afe9fcd540de
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 399}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 4
      width: 58
      height: 62
    spriteID: 03d14009f77a0d04d9264f6d7b1b10fd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 229
      width: 42
      height: 84
    spriteID: 05ae4b8668afd164daf8b552f8e06e58
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 229}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: -0.26881722}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 50
      height: 93
    spriteID: 59f0eed3cb516bb4b83a26a6ec6db519
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 4
      width: 54
      height: 67
    spriteID: c7d36d8f316801b44957febb3d74bd30
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: -0.32500002}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 181
      width: 50
      height: 80
    spriteID: 53bbbcaf129f179439f0997c5553b2d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 181}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 294
      width: 50
      height: 83
    spriteID: b1aeaf7ab4a0b034bbcfb67b313c09a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 294}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 385
      width: 50
      height: 83
    spriteID: 0b1a6d8c2cd8f9048a6d39be712cdda8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 385}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 79
      width: 54
      height: 67
    spriteID: 5a849e5b616e0034b9dbf784dd4fd2e3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 79}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: -0.28089887}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 105
      width: 50
      height: 89
    spriteID: 565557ce73444c8479a2d213461f43f8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 105}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: -0.2631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 269
      width: 42
      height: 95
    spriteID: e944e4d1e3bc0d04492e1d6c1f7d7ee1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 269}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 74
      width: 58
      height: 62
    spriteID: 45a10caef1c38dd448a0fa5d738ac564
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 74}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.51111114, y: -0.38157895}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 222
      width: 45
      height: 76
    spriteID: 222464e491cd7164282c2ff228cd7f31
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 222}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 144
      width: 50
      height: 70
    spriteID: aa44fb223181b694a998f4aa1313a567
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 144}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 154
      width: 54
      height: 67
    spriteID: 10e7c72fd59322e46bec8081638b2fd7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 154}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 202
      width: 50
      height: 84
    spriteID: 0f99ab3cf4b57ed4eba2c78a9d0337d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 202}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: -0.29069766}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 87
      width: 48
      height: 86
    spriteID: 675bbdc118cb955468e9b70f77fbd628
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 87}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.50877196, y: -0.3866667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 4
      width: 57
      height: 75
    spriteID: a8eb03e77c3d1a140a2e3fe995c49cbd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 4}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 127
      y: 4
      width: 46
      height: 79
    spriteID: 1dc0667e44ad340448a37e2e11fadded
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 127, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 372
      width: 50
      height: 79
    spriteID: 7878b5817a11dc94783152f34bad3aba
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 372}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 514203603
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Barrel_Purple
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: f921f33ffdb5fbf4d989d9a6a29c2704
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 97d168d87854c564cb46afe9fcd540de
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 03d14009f77a0d04d9264f6d7b1b10fd
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 84
      spriteId: 05ae4b8668afd164daf8b552f8e06e58
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 93
      spriteId: 59f0eed3cb516bb4b83a26a6ec6db519
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: c7d36d8f316801b44957febb3d74bd30
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 80
      spriteId: 53bbbcaf129f179439f0997c5553b2d3
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: b1aeaf7ab4a0b034bbcfb67b313c09a8
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: 0b1a6d8c2cd8f9048a6d39be712cdda8
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 5a849e5b616e0034b9dbf784dd4fd2e3
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 89
      spriteId: 565557ce73444c8479a2d213461f43f8
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 95
      spriteId: e944e4d1e3bc0d04492e1d6c1f7d7ee1
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 45a10caef1c38dd448a0fa5d738ac564
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 45
        height: 76
      spriteId: 222464e491cd7164282c2ff228cd7f31
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: aa44fb223181b694a998f4aa1313a567
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 10e7c72fd59322e46bec8081638b2fd7
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 84
      spriteId: 0f99ab3cf4b57ed4eba2c78a9d0337d2
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 25
        width: 48
        height: 86
      spriteId: 675bbdc118cb955468e9b70f77fbd628
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 57
        height: 75
      spriteId: a8eb03e77c3d1a140a2e3fe995c49cbd
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 46
        height: 79
      spriteId: 1dc0667e44ad340448a37e2e11fadded
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 79
      spriteId: 7878b5817a11dc94783152f34bad3aba
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 512, y: 512}

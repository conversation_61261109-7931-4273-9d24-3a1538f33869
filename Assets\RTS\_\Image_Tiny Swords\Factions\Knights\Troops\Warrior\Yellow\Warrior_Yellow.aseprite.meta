fileFormatVersion: 2
guid: 88cea1aeb82833f44b7780c39bd2b331
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 809
      y: 479
      width: 78
      height: 91
    spriteID: 70b2ffcd65648f244ba7bb8ca4a0e120
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 809, y: 479}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 895
      y: 479
      width: 78
      height: 91
    spriteID: 6d999dd7b0958a84382feeeafce47024
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 895, y: 479}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 241
      y: 590
      width: 78
      height: 91
    spriteID: 2a01f99dcb29b2c4198b30bb421ac0e6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 241, y: 590}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.42500004, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 721
      y: 479
      width: 80
      height: 89
    spriteID: 2dac73c1e0ee4f747aed8446739415d8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 721, y: 479}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.42500004, y: -0.6436781}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 413
      y: 590
      width: 80
      height: 87
    spriteID: 3442d79499d59fd4398a541e52ccd7ba
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 413, y: 590}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.42307693, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 501
      y: 590
      width: 78
      height: 88
    spriteID: fd4fa0f2828ca6649b7a6672ca3c3cbe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 501, y: 590}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 327
      y: 590
      width: 78
      height: 91
    spriteID: 14af30bdd2636324886221f4f45f4fde
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 327, y: 590}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.41666672, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 956
      y: 129
      width: 60
      height: 93
    spriteID: 3c1c932ca4853c240b7eb46b6a93a9f2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 956, y: 129}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.40579712, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 661
      y: 590
      width: 69
      height: 94
    spriteID: 240b1e3face37014f846ec0a27ead6dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 661, y: 590}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.41025642, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 825
      y: 590
      width: 78
      height: 80
    spriteID: f111608a0fd8dd046a22631d161059e6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 825, y: 590}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44210526, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 538
      y: 274
      width: 95
      height: 93
    spriteID: 98ab8432f1fd0b84cb18a61f16655c79
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 538, y: 274}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.43820223, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 641
      y: 274
      width: 89
      height: 94
    spriteID: 97bb938af5b94f34d84157ac9570a3c2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 641, y: 274}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.4177215, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 590
      width: 79
      height: 80
    spriteID: d960674c8a6819f42b58bf8d2fcbd029
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 590}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5352112, y: -0.5185185}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 590
      width: 71
      height: 108
    spriteID: c3d7938cf7d647543adb340dabbe5d0a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 590}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 590
      width: 71
      height: 112
    spriteID: a65ebd45ebb4a7e4b8ece730f93e527c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 590}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 83
      y: 590
      width: 71
      height: 112
    spriteID: e6898e0c837ea3f418c3716f4e79b0ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 83, y: 590}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.18691586, y: -0.49107146}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 858
      y: 4
      width: 107
      height: 112
    spriteID: 5799f99f96a40a94fa3c2846568aac2e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 858, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.20560749, y: -0.57608694}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 841
      y: 129
      width: 107
      height: 92
    spriteID: e501cd0d83619334e9b2ede2014a4ccc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 841, y: 129}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.28999996, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 430
      y: 274
      width: 100
      height: 89
    spriteID: ffde3f042b666e3478dcb0f7b478ba5f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 430, y: 274}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.75609756, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 631
      y: 479
      width: 82
      height: 89
    spriteID: be741f4918fd29a4e8f1d0392ba3fc09
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 631, y: 479}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 359
      y: 479
      width: 84
      height: 89
    spriteID: ef5de1506ab44904c9b522423a31a1ff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 359, y: 479}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 451
      y: 479
      width: 84
      height: 89
    spriteID: ad7907397ac488a4083e2fe8e002c0b2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 451, y: 479}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.30894306, y: -0.45999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 593
      y: 4
      width: 123
      height: 100
    spriteID: 538252306ba40d845ad94e2c098044f2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 593, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.29508197, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 508
      y: 129
      width: 122
      height: 94
    spriteID: ae691ab091e5c0c468bc01d59a6bf925
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 508, y: 129}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.40000004, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 916
      y: 274
      width: 85
      height: 88
    spriteID: 2639de61e2c6c774484b737573a973c2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 916, y: 274}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.4871795, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 186
      y: 479
      width: 78
      height: 101
    spriteID: dea9f7bf046d360499b4d5636fd4b8a6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 186, y: 479}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 479
      width: 83
      height: 103
    spriteID: 00e6761b39f78454dbc24f6b3366f412
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 479}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 95
      y: 479
      width: 83
      height: 103
    spriteID: 9cb8f92fef7c5704eb72f26e55c1e82f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 95, y: 479}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.42372882, y: -0.09489051}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 129
      width: 118
      height: 137
    spriteID: 971dbf0f4bc8d124bab56baed873bb86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 129}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.6179775, y: -0.20161289}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 638
      y: 129
      width: 89
      height: 124
    spriteID: c7586ee44b46a6944b37dc136b5ac003
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 638, y: 129}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.6363636, y: -0.5252526}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 587
      y: 590
      width: 66
      height: 99
    spriteID: 276ed91fe22ca1745815a2e574d41a29
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 587, y: 590}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.75, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 543
      y: 479
      width: 80
      height: 93
    spriteID: fe18362b979541f48a171026fc2761f6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 543, y: 479}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 274
      width: 81
      height: 94
    spriteID: 43f64c460e91def41870fc8f9abed204
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 274}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 827
      y: 274
      width: 81
      height: 94
    spriteID: d4dbd9a292eaf6e4fad3296b0c865c2b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 827, y: 274}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.39041096, y: -0.23931624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 146
      height: 117
    spriteID: 771d059a953739b4caaadf492727a801
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.24137934, y: -0.24786326}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 469
      y: 4
      width: 116
      height: 117
    spriteID: 1889f534bd1ba91489c3ae85df2d9b9d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 469, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.34343436, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 274
      width: 99
      height: 94
    spriteID: 08ed762d17ab19746bc7b8b6aba3ed76
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 274}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.65254235, y: -0.56565654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 382
      y: 129
      width: 118
      height: 99
    spriteID: e0dcc18bdba0da74e8a7bf570a372d44
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 382, y: 129}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 130
      y: 129
      width: 118
      height: 101
    spriteID: 583843765af001e4cab5bc3a08417b7b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 130, y: 129}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 256
      y: 129
      width: 118
      height: 101
    spriteID: 54b7aba4daac17e46a3d10f9e6c26321
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 256, y: 129}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.5037038, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 326
      y: 4
      width: 135
      height: 107
    spriteID: 10257cd64b2449c478a75eae4a3df200
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 326, y: 4}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.26530612, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 735
      y: 129
      width: 98
      height: 107
    spriteID: 6d02c85975be9854d9b182ea731ce4e5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 735, y: 129}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.25316453, y: -0.5833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 272
      y: 479
      width: 79
      height: 96
    spriteID: 9e1f85c906329e040b7567f5a5115a49
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 272, y: 479}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.20618555, y: -0.6086956}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 325
      y: 274
      width: 97
      height: 92
    spriteID: d6dd74fdf6c28bb429515d5c222efa86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 325, y: 274}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 274
      width: 99
      height: 91
    spriteID: 3891be1a44c01004e8161197d8fbddbc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 274}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 218
      y: 274
      width: 99
      height: 91
    spriteID: 92b0cd0b6572c56429c47b88ea156da9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 218, y: 274}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.54375005, y: -0.57}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 158
      y: 4
      width: 160
      height: 100
    spriteID: ab98a13838edd4643a5505583e806920
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 158, y: 4}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.72222227, y: -0.58762884}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 724
      y: 4
      width: 126
      height: 97
    spriteID: 7ceb71d4074496a4b9e414c3a4339a13
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 724, y: 4}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.59782606, y: -0.58947366}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 376
      width: 92
      height: 95
    spriteID: c2621b025a239434492f68ebf9c8c655
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 376}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3892026617
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Warrior_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 70b2ffcd65648f244ba7bb8ca4a0e120
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 6d999dd7b0958a84382feeeafce47024
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 2a01f99dcb29b2c4198b30bb421ac0e6
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 89
      spriteId: 2dac73c1e0ee4f747aed8446739415d8
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 87
      spriteId: 3442d79499d59fd4398a541e52ccd7ba
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 88
      spriteId: fd4fa0f2828ca6649b7a6672ca3c3cbe
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 14af30bdd2636324886221f4f45f4fde
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 71
        y: 56
        width: 60
        height: 93
      spriteId: 3c1c932ca4853c240b7eb46b6a93a9f2
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 56
        width: 69
        height: 94
      spriteId: 240b1e3face37014f846ec0a27ead6dc
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 56
        width: 78
        height: 80
      spriteId: f111608a0fd8dd046a22631d161059e6
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 56
        width: 95
        height: 93
      spriteId: 98ab8432f1fd0b84cb18a61f16655c79
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 89
        height: 94
      spriteId: 97bb938af5b94f34d84157ac9570a3c2
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 79
        height: 80
      spriteId: d960674c8a6819f42b58bf8d2fcbd029
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 71
        height: 108
      spriteId: c3d7938cf7d647543adb340dabbe5d0a
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: a65ebd45ebb4a7e4b8ece730f93e527c
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: e6898e0c837ea3f418c3716f4e79b0ed
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 55
        width: 107
        height: 112
      spriteId: 5799f99f96a40a94fa3c2846568aac2e
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 74
        y: 53
        width: 107
        height: 92
      spriteId: e501cd0d83619334e9b2ede2014a4ccc
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 56
        width: 100
        height: 89
      spriteId: ffde3f042b666e3478dcb0f7b478ba5f
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 34
        y: 56
        width: 82
        height: 89
      spriteId: be741f4918fd29a4e8f1d0392ba3fc09
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: ef5de1506ab44904c9b522423a31a1ff
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: ad7907397ac488a4083e2fe8e002c0b2
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 46
        width: 123
        height: 100
      spriteId: 538252306ba40d845ad94e2c098044f2
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 56
        width: 122
        height: 94
      spriteId: ae691ab091e5c0c468bc01d59a6bf925
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 85
        height: 88
      spriteId: 2639de61e2c6c774484b737573a973c2
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 78
        height: 101
      spriteId: dea9f7bf046d360499b4d5636fd4b8a6
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 00e6761b39f78454dbc24f6b3366f412
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 9cb8f92fef7c5704eb72f26e55c1e82f
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 13
        width: 118
        height: 137
      spriteId: 971dbf0f4bc8d124bab56baed873bb86
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 25
        width: 89
        height: 124
      spriteId: c7586ee44b46a6944b37dc136b5ac003
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 52
        width: 66
        height: 99
      spriteId: 276ed91fe22ca1745815a2e574d41a29
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 36
        y: 56
        width: 80
        height: 93
      spriteId: fe18362b979541f48a171026fc2761f6
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: 43f64c460e91def41870fc8f9abed204
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: d4dbd9a292eaf6e4fad3296b0c865c2b
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 28
        width: 146
        height: 117
      spriteId: 771d059a953739b4caaadf492727a801
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 29
        width: 116
        height: 117
      spriteId: 1889f534bd1ba91489c3ae85df2d9b9d
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 99
        height: 94
      spriteId: 08ed762d17ab19746bc7b8b6aba3ed76
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 56
        width: 118
        height: 99
      spriteId: e0dcc18bdba0da74e8a7bf570a372d44
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 583843765af001e4cab5bc3a08417b7b
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 54b7aba4daac17e46a3d10f9e6c26321
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 57
        width: 135
        height: 107
      spriteId: 10257cd64b2449c478a75eae4a3df200
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 57
        width: 98
        height: 107
      spriteId: 6d02c85975be9854d9b182ea731ce4e5
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 79
        height: 96
      spriteId: 9e1f85c906329e040b7567f5a5115a49
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 97
        height: 92
      spriteId: d6dd74fdf6c28bb429515d5c222efa86
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 3891be1a44c01004e8161197d8fbddbc
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 92b0cd0b6572c56429c47b88ea156da9
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 9
        y: 57
        width: 160
        height: 100
      spriteId: ab98a13838edd4643a5505583e806920
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 5
        y: 57
        width: 126
        height: 97
      spriteId: 7ceb71d4074496a4b9e414c3a4339a13
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 56
        width: 92
        height: 95
      spriteId: c2621b025a239434492f68ebf9c8c655
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 1024, y: 1024}

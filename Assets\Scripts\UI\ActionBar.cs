using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

/// <summary>
/// يدير هذا الكلاس شريط الإجراءات في واجهة المستخدم، حيث يمكن للاعب اختيار الإجراءات المتاحة. يتحكم في عرض وإخفاء الأزرار وتسجيل الإجراءات.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class ActionBar : MonoBehaviour
{
    [SerializeField] private Image m_BackgroundImage;
    [SerializeField] private ActionButton m_ActionButtonPrefab;

    private Color m_OriginalBackgroundColor;
    private List<ActionButton> m_ActionButtonsList = new();


    void Awake()
    {
        m_OriginalBackgroundColor = m_BackgroundImage.color;
    }

    public void RegisterAction(Sprite icon, UnityAction unityAction)
    {

        var actionButton = Instantiate(m_ActionButtonPrefab, transform);
        actionButton.Init(icon, unityAction);
        m_ActionButtonsList.Add(actionButton);
    }

    public void ClearActions()
    {
        for (int i = m_ActionButtonsList.Count - 1; i >= 0; i--)
        {
            Destroy(m_ActionButtonsList[i].gameObject);
            m_ActionButtonsList.RemoveAt(i);
        }
    }

    public void FocusAction(int index)
    {
        if (index < 0 || index >= m_ActionButtonsList.Count) return;

        foreach (var button in m_ActionButtonsList)
        {
            button.Unfocus();

        }
        m_ActionButtonsList[index].Focus();
    }

    public void Show()
    {
        m_BackgroundImage.color = m_OriginalBackgroundColor;
    }

    public void Hide()
    {
        m_BackgroundImage.color = new Color(0, 0, 0, 0);
    }





}//end class
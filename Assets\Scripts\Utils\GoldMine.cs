using System.Collections.Generic;
using System.Collections;
using UnityEngine;

public class GoldMine : MonoBehaviour
{
    [SerializeField] private Sprite m_ActiveSprite;
    [SerializeField] private Sprite m_DefaultSprite;
    [SerializeField] private SpriteRenderer m_RendererSprite;
    [SerializeField] private CapsuleCollider2D m_Collider;
    [SerializeField] private float m_EnterMineFrecuency = 2f;
    [SerializeField] private float m_MiningDeuration = 2f;

    private readonly int m_MaxAllowedMiners = 2;
    private readonly Queue<WorkerUnit> m_ActiveMinersQueue = new();
    private float m_NextPosibleEnterTime;
    // private bool m_Claimed = true;


    // public bool Claimed => m_Claimed;


    void Update()
    {
        if (m_ActiveMinersQueue.Count > 0)
        {
            m_RendererSprite.sprite = m_ActiveSprite;
        }
        else
        {
            m_RendererSprite.sprite = m_DefaultSprite;
        }
    }

    public bool TryToEnterMine(WorkerUnit worker)
    {
        if (m_ActiveMinersQueue.Count < m_MaxAllowedMiners && Time.time >= m_NextPosibleEnterTime)
        {
            worker.OnEnterMining();
            m_ActiveMinersQueue.Enqueue(worker);
            m_NextPosibleEnterTime = Time.time + m_EnterMineFrecuency;

            StartCoroutine(ReleaseWorkerAfterDelay(worker, m_MiningDeuration));

            return true;
        }
        Debug.Log("Cannot Enter yet !!");
        // m_Claimed = false;
        return false;
    }

    public Vector3 GetBottomPosition()
    {
        return m_Collider.bounds.min;
    }

    IEnumerator ReleaseWorkerAfterDelay(WorkerUnit worker, float delay)
    {
        yield return new WaitForSeconds(delay);

        if (m_ActiveMinersQueue.Contains(worker))
        {
            m_ActiveMinersQueue.Dequeue();
            worker.OnLeaveMining();
            // m_Claimed = true;
        }
    }



}

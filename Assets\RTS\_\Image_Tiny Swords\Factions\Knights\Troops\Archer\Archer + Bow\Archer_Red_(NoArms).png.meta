fileFormatVersion: 2
guid: 424d47caa96ba7d4faedef4c49a0a9e4
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -779530754703691295
    second: <PERSON><PERSON><PERSON>_(NoArms)_0
  - first:
      213: 1342457956399488464
    second: <PERSON><PERSON><PERSON>_(NoArms)_1
  - first:
      213: -760774686098207700
    second: <PERSON>_<PERSON>_(NoArms)_2
  - first:
      213: 2945705475700592968
    second: <PERSON><PERSON><PERSON>_(NoArms)_3
  - first:
      213: -5854490005169611917
    second: <PERSON>_<PERSON>_(NoArms)_4
  - first:
      213: -6278388339237390178
    second: <PERSON>_<PERSON>_(NoArms)_5
  - first:
      213: 7675357999706952606
    second: <PERSON>_<PERSON>_(NoArms)_6
  - first:
      213: 417398373251102774
    second: <PERSON><PERSON><PERSON>_(NoArms)_7
  - first:
      213: -857071193469251862
    second: <PERSON><PERSON><PERSON>_(NoArms)_8
  - first:
      213: -7949194503432481910
    second: <PERSON><PERSON><PERSON>_(NoArms)_9
  - first:
      213: 6922436141123105126
    second: <PERSON>_<PERSON>_(NoArms)_10
  - first:
      213: -4711082139558150911
    second: Archer_Red_(NoArms)_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_0
      rect:
        serializedVersion: 2
        x: 67
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e1f462a8ec8e25f0800000000000000
      internalID: -779530754703691295
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_1
      rect:
        serializedVersion: 2
        x: 259
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0dd9611166e51a210800000000000000
      internalID: 1342457956399488464
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_2
      rect:
        serializedVersion: 2
        x: 451
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2cc109247f2175f0800000000000000
      internalID: -760774686098207700
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_3
      rect:
        serializedVersion: 2
        x: 644
        y: 249
        width: 57
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8419e0ce58f31e820800000000000000
      internalID: 2945705475700592968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_4
      rect:
        serializedVersion: 2
        x: 836
        y: 249
        width: 57
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37b5ac4db5ca0cea0800000000000000
      internalID: -5854490005169611917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_5
      rect:
        serializedVersion: 2
        x: 1026
        y: 249
        width: 57
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e98fccdd11faed8a0800000000000000
      internalID: -6278388339237390178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_6
      rect:
        serializedVersion: 2
        x: 65
        y: 57
        width: 57
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e935c785f69548a60800000000000000
      internalID: 7675357999706952606
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_7
      rect:
        serializedVersion: 2
        x: 258
        y: 57
        width: 57
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6383577eb95eac500800000000000000
      internalID: 417398373251102774
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_8
      rect:
        serializedVersion: 2
        x: 451
        y: 57
        width: 57
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aee8b0e5a421b14f0800000000000000
      internalID: -857071193469251862
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_9
      rect:
        serializedVersion: 2
        x: 641
        y: 57
        width: 57
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a835b6c37b9cea190800000000000000
      internalID: -7949194503432481910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_10
      rect:
        serializedVersion: 2
        x: 834
        y: 57
        width: 57
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 661f7f36efe611060800000000000000
      internalID: 6922436141123105126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_(NoArms)_11
      rect:
        serializedVersion: 2
        x: 1027
        y: 57
        width: 57
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10df2f321cfde9eb0800000000000000
      internalID: -4711082139558150911
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Red_(NoArms)_0: -779530754703691295
      Archer_Red_(NoArms)_1: 1342457956399488464
      Archer_Red_(NoArms)_10: 6922436141123105126
      Archer_Red_(NoArms)_11: -4711082139558150911
      Archer_Red_(NoArms)_2: -760774686098207700
      Archer_Red_(NoArms)_3: 2945705475700592968
      Archer_Red_(NoArms)_4: -5854490005169611917
      Archer_Red_(NoArms)_5: -6278388339237390178
      Archer_Red_(NoArms)_6: 7675357999706952606
      Archer_Red_(NoArms)_7: 417398373251102774
      Archer_Red_(NoArms)_8: -857071193469251862
      Archer_Red_(NoArms)_9: -7949194503432481910
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

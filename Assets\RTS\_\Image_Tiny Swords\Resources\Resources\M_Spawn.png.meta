fileFormatVersion: 2
guid: 9d3c090c34906c04f9a24d32ea37a5bc
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 3498063503139651976
    second: M_Spawn_0
  - first:
      213: 6520460431258965811
    second: M_Spawn_1
  - first:
      213: -1122592425472363717
    second: M_Spawn_2
  - first:
      213: -4112147455562471226
    second: M_Spawn_3
  - first:
      213: -5147735771709780924
    second: M_Spawn_4
  - first:
      213: -9112186978602087139
    second: M_Spawn_5
  - first:
      213: 5925288531732966603
    second: M_Spawn_6
  - first:
      213: 3667812429124112445
    second: M_Spawn_7
  - first:
      213: 6933147786138553085
    second: M_Spawn_8
  - first:
      213: 7753593014952639919
    second: M_Spawn_9
  - first:
      213: -8366115955088691986
    second: M_Spawn_10
  - first:
      213: -8313749564130847164
    second: M_Spawn_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: M_Spawn_0
      rect:
        serializedVersion: 2
        x: 42
        y: 42
        width: 46
        height: 46
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 881c56af83e9b8030800000000000000
      internalID: 3498063503139651976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_1
      rect:
        serializedVersion: 2
        x: 164
        y: 28
        width: 58
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3375e2127345d7a50800000000000000
      internalID: 6520460431258965811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_2
      rect:
        serializedVersion: 2
        x: 295
        y: 79
        width: 27
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b33fc240320cb60f0800000000000000
      internalID: -1122592425472363717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_3
      rect:
        serializedVersion: 2
        x: 297
        y: 44
        width: 45
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c82c2f1da7bee6c0800000000000000
      internalID: -4112147455562471226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_4
      rect:
        serializedVersion: 2
        x: 335
        y: 54
        width: 17
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44ccaa9e6919f88b0800000000000000
      internalID: -5147735771709780924
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_5
      rect:
        serializedVersion: 2
        x: 423
        y: 45
        width: 54
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1d8b7da8320b8180800000000000000
      internalID: -9112186978602087139
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_6
      rect:
        serializedVersion: 2
        x: 290
        y: 44
        width: 11
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcca3ada68ada3250800000000000000
      internalID: 5925288531732966603
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_7
      rect:
        serializedVersion: 2
        x: 303
        y: 28
        width: 37
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3094bca8ffa6e230800000000000000
      internalID: 3667812429124112445
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_8
      rect:
        serializedVersion: 2
        x: 436
        y: 28
        width: 33
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfa03189d2d773060800000000000000
      internalID: 6933147786138553085
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_9
      rect:
        serializedVersion: 2
        x: 547
        y: 29
        width: 65
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fadb33d44cb4a9b60800000000000000
      internalID: 7753593014952639919
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_10
      rect:
        serializedVersion: 2
        x: 685
        y: 28
        width: 45
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eece53b4dd595eb80800000000000000
      internalID: -8366115955088691986
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: M_Spawn_11
      rect:
        serializedVersion: 2
        x: 809
        y: 27
        width: 53
        height: 43
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4461c6ec1d0af9c80800000000000000
      internalID: -8313749564130847164
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      M_Spawn_0: 3498063503139651976
      M_Spawn_1: 6520460431258965811
      M_Spawn_10: -8366115955088691986
      M_Spawn_11: -8313749564130847164
      M_Spawn_2: -1122592425472363717
      M_Spawn_3: -4112147455562471226
      M_Spawn_4: -5147735771709780924
      M_Spawn_5: -9112186978602087139
      M_Spawn_6: 5925288531732966603
      M_Spawn_7: 3667812429124112445
      M_Spawn_8: 6933147786138553085
      M_Spawn_9: 7753593014952639919
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

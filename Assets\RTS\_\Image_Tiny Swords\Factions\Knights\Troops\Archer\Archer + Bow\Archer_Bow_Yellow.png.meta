fileFormatVersion: 2
guid: 4c7fa02cdec886e42986c30ea19a9a43
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6718733118762791289
    second: <PERSON><PERSON><PERSON>_Yellow_0
  - first:
      213: -4349393927651703796
    second: <PERSON><PERSON><PERSON>_Yellow_1
  - first:
      213: -2904342112224456264
    second: <PERSON>_<PERSON>_Yellow_2
  - first:
      213: -6706752332118546780
    second: <PERSON>_<PERSON>_Yellow_3
  - first:
      213: -6038568990709319007
    second: <PERSON><PERSON><PERSON>_Yellow_4
  - first:
      213: -1726624554175693109
    second: <PERSON><PERSON><PERSON>_Yellow_5
  - first:
      213: -236583400459511096
    second: <PERSON>_<PERSON>_Yellow_6
  - first:
      213: 2651125302661294545
    second: <PERSON>_<PERSON>_Yellow_7
  - first:
      213: -4645790221744671976
    second: <PERSON>_<PERSON>_Yellow_8
  - first:
      213: -7471761208531681477
    second: <PERSON><PERSON><PERSON>_Yellow_9
  - first:
      213: 5206855670516815190
    second: <PERSON><PERSON><PERSON>_Yellow_10
  - first:
      213: 4685556051317077005
    second: <PERSON>_<PERSON>_Yellow_11
  - first:
      213: 2278029566634550615
    second: <PERSON>_Bow_Yellow_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Bow_Yellow_0
      rect:
        serializedVersion: 2
        x: 68
        y: 259
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97132849c2cbd3d50800000000000000
      internalID: 6718733118762791289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_1
      rect:
        serializedVersion: 2
        x: 112
        y: 251
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0444125449d3a3c0800000000000000
      internalID: -4349393927651703796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_2
      rect:
        serializedVersion: 2
        x: 46
        y: 62
        width: 48
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b157ae9c34b1b7d0800000000000000
      internalID: -2904342112224456264
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_3
      rect:
        serializedVersion: 2
        x: 96
        y: 63
        width: 20
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a62d8fa944dce2a0800000000000000
      internalID: -6706752332118546780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_4
      rect:
        serializedVersion: 2
        x: 256
        y: 62
        width: 55
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1aaf4152f71b23ca0800000000000000
      internalID: -6038568990709319007
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_5
      rect:
        serializedVersion: 2
        x: 475
        y: 62
        width: 67
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcef3e3bd1cc908e0800000000000000
      internalID: -1726624554175693109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_6
      rect:
        serializedVersion: 2
        x: 641
        y: 59
        width: 74
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ca5bd796ac77bcf0800000000000000
      internalID: -236583400459511096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_7
      rect:
        serializedVersion: 2
        x: 828
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1d92d20aa60bac420800000000000000
      internalID: 2651125302661294545
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_8
      rect:
        serializedVersion: 2
        x: 1020
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 813fce36866d68fb0800000000000000
      internalID: -4645790221744671976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_9
      rect:
        serializedVersion: 2
        x: 1202
        y: 73
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b3ffce694c8fe4890800000000000000
      internalID: -7471761208531681477
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_10
      rect:
        serializedVersion: 2
        x: 1265
        y: 59
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65512ed25a7724840800000000000000
      internalID: 5206855670516815190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_11
      rect:
        serializedVersion: 2
        x: 1409
        y: 67
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d04bfed4760760140800000000000000
      internalID: 4685556051317077005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Yellow_12
      rect:
        serializedVersion: 2
        x: 1454
        y: 57
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 755e61342df2d9f10800000000000000
      internalID: 2278029566634550615
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Bow_Yellow_0: 6718733118762791289
      Archer_Bow_Yellow_1: -4349393927651703796
      Archer_Bow_Yellow_10: 5206855670516815190
      Archer_Bow_Yellow_11: 4685556051317077005
      Archer_Bow_Yellow_12: 2278029566634550615
      Archer_Bow_Yellow_2: -2904342112224456264
      Archer_Bow_Yellow_3: -6706752332118546780
      Archer_Bow_Yellow_4: -6038568990709319007
      Archer_Bow_Yellow_5: -1726624554175693109
      Archer_Bow_Yellow_6: -236583400459511096
      Archer_Bow_Yellow_7: 2651125302661294545
      Archer_Bow_Yellow_8: -4645790221744671976
      Archer_Bow_Yellow_9: -7471761208531681477
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

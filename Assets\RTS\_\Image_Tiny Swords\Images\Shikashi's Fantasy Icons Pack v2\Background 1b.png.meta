fileFormatVersion: 2
guid: a549270b7e61dae42ad5f70a7fae2970
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5906131350403104941
    second: Background 1b_0
  - first:
      213: 3135307733736775626
    second: Background 1b_1
  - first:
      213: -4627916627190346735
    second: Background 1b_2
  - first:
      213: 7726177536480505408
    second: Background 1b_3
  - first:
      213: 2809103342127764416
    second: Background 1b_4
  - first:
      213: 5062696349429056118
    second: Background 1b_5
  - first:
      213: 5935344399417683858
    second: Background 1b_6
  - first:
      213: 9192827821051912604
    second: Background 1b_7
  - first:
      213: -2775435642356039265
    second: Background 1b_8
  - first:
      213: 7700017676508285661
    second: Background 1b_9
  - first:
      213: 3370752188979968875
    second: Background 1b_10
  - first:
      213: -9217203768585316163
    second: Background 1b_11
  - first:
      213: 4480130445376442595
    second: Background 1b_12
  - first:
      213: -3050413396881736261
    second: Background 1b_13
  - first:
      213: -2052684109504747570
    second: Background 1b_14
  - first:
      213: -8228775014873272904
    second: Background 1b_15
  - first:
      213: 7894936184584671346
    second: Background 1b_16
  - first:
      213: -8037003941297163526
    second: Background 1b_17
  - first:
      213: -619981938145707526
    second: Background 1b_18
  - first:
      213: -3485638287485345619
    second: Background 1b_19
  - first:
      213: -5524848858637164096
    second: Background 1b_20
  - first:
      213: 4260548471111037108
    second: Background 1b_21
  - first:
      213: -3350800951315006211
    second: Background 1b_22
  - first:
      213: -1227575065896625374
    second: Background 1b_23
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Background 1b_0
      rect:
        serializedVersion: 2
        x: 0
        y: 837
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da04391db2bc6f150800000000000000
      internalID: 5906131350403104941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_1
      rect:
        serializedVersion: 2
        x: 0
        y: 805
        width: 512
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acf5ae2b5c9d28b20800000000000000
      internalID: 3135307733736775626
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_2
      rect:
        serializedVersion: 2
        x: 0
        y: 773
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 11c023ae85656cfb0800000000000000
      internalID: -4627916627190346735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_3
      rect:
        serializedVersion: 2
        x: 0
        y: 741
        width: 512
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04ee58dc885e83b60800000000000000
      internalID: 7726177536480505408
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_4
      rect:
        serializedVersion: 2
        x: 0
        y: 709
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c353ec3b90fbf620800000000000000
      internalID: 2809103342127764416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_5
      rect:
        serializedVersion: 2
        x: 0
        y: 676
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 672c269a18f424640800000000000000
      internalID: 5062696349429056118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_6
      rect:
        serializedVersion: 2
        x: 0
        y: 645
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29f25e2d8449e5250800000000000000
      internalID: 5935344399417683858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_7
      rect:
        serializedVersion: 2
        x: 0
        y: 613
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c91a023923c739f70800000000000000
      internalID: 9192827821051912604
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_8
      rect:
        serializedVersion: 2
        x: 0
        y: 581
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f910968fcfbab79d0800000000000000
      internalID: -2775435642356039265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_9
      rect:
        serializedVersion: 2
        x: 0
        y: 549
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dda4184c745fbda60800000000000000
      internalID: 7700017676508285661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_10
      rect:
        serializedVersion: 2
        x: 0
        y: 517
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6b7502c14157ce20800000000000000
      internalID: 3370752188979968875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_11
      rect:
        serializedVersion: 2
        x: 0
        y: 485
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db8941fd10ae51080800000000000000
      internalID: -9217203768585316163
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_12
      rect:
        serializedVersion: 2
        x: 0
        y: 453
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e0531047ee9c2e30800000000000000
      internalID: 4480130445376442595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_13
      rect:
        serializedVersion: 2
        x: 0
        y: 420
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bbd75c46d21caa5d0800000000000000
      internalID: -3050413396881736261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_14
      rect:
        serializedVersion: 2
        x: 0
        y: 388
        width: 512
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec761e09da66383e0800000000000000
      internalID: -2052684109504747570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_15
      rect:
        serializedVersion: 2
        x: 0
        y: 356
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bd6eddc8b48dcd80800000000000000
      internalID: -8228775014873272904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_16
      rect:
        serializedVersion: 2
        x: 0
        y: 325
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2701c8671a2709d60800000000000000
      internalID: 7894936184584671346
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_17
      rect:
        serializedVersion: 2
        x: 0
        y: 293
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af2719c4f73d67090800000000000000
      internalID: -8037003941297163526
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_18
      rect:
        serializedVersion: 2
        x: 0
        y: 262
        width: 512
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af9baa946b16567f0800000000000000
      internalID: -619981938145707526
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_19
      rect:
        serializedVersion: 2
        x: 0
        y: 227
        width: 512
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da06feaf17680afc0800000000000000
      internalID: -3485638287485345619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_20
      rect:
        serializedVersion: 2
        x: 0
        y: 195
        width: 512
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c1a5372c3bc353b0800000000000000
      internalID: -5524848858637164096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_21
      rect:
        serializedVersion: 2
        x: 0
        y: 164
        width: 512
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b038e8d242802b30800000000000000
      internalID: 4260548471111037108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_22
      rect:
        serializedVersion: 2
        x: 0
        y: 136
        width: 512
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfcf3dfb9409f71d0800000000000000
      internalID: -3350800951315006211
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1b_23
      rect:
        serializedVersion: 2
        x: 0
        y: 104
        width: 512
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22bda164bf6c6fee0800000000000000
      internalID: -1227575065896625374
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable: {}
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

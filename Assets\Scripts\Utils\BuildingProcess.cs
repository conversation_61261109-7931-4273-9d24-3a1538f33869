
using UnityEngine;

/// <summary>
/// يدير عملية بناء الهياكل في اللعبة، بما في ذلك تتبع التقدم والعمال المشاركين. يتحكم في دورة حياة البناء من البداية حتى الاكتمال.
/// </summary>
/// <remarks>
/// يرث من: None
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class BuildingProcess
{
    private BuildActionSO m_BuildingAction;
    private WorkerUnit m_Worker;
    private StructureUnit m_Structure;
    private ParticleSystem m_ConstructionEffectPrefab;

    private float m_ProgressBuildingTimer;
    private bool m_IsFinishedBuilding;
    private bool InProgress => HasActiveWorker && m_Worker.CurrentState == UnitState.Building;


    public bool HasActiveWorker => m_Worker != null;


    public BuildingProcess(BuildActionSO buildAction, Vector3 placementPosition, WorkerUnit worker, ParticleSystem constructionEffectPrefab)
    {
        m_BuildingAction = buildAction;
        /// <summary>
        /// متغير من نوع var يستخدم في كلاس BuildingProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var effectOffset = new Vector3(0, -1f, 0);
        m_ConstructionEffectPrefab = Object.Instantiate(constructionEffectPrefab, placementPosition + effectOffset, Quaternion.identity);
        m_Structure = Object.Instantiate(buildAction.StructurePrefab);
        m_Structure.Renderer.sprite = m_BuildingAction.FoundationSprite;
        m_Structure.transform.position = placementPosition;
        m_Structure.RegisterProcess(this);

        worker.SendToBuild(m_Structure);
    }


    public void Update()
    {
        if (m_IsFinishedBuilding) return;

        if (InProgress)
        {
            m_ProgressBuildingTimer += Time.deltaTime;

            if (!m_ConstructionEffectPrefab.isPlaying)
            {
                m_ConstructionEffectPrefab.Play();
            }

            if (m_ProgressBuildingTimer >= m_BuildingAction.ConstructionTime)
            {
                m_IsFinishedBuilding = true;
                m_Structure.Renderer.sprite = m_BuildingAction.CompletionSprite;
                m_Worker.OnBuildingFinished();
                m_Structure.OnConstructionFinished();
            }
        }

    }

    public void AddWorker(WorkerUnit worker)
    {
        if (HasActiveWorker) return;
        m_Worker = worker;
    }

    public void RemoveWorker()
    {
        if (!HasActiveWorker) return;
        m_ConstructionEffectPrefab.Stop();
        m_Worker = null;
    }







}
fileFormatVersion: 2
guid: 42de441fbca56da4e84b4cd16e550304
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1657236513483380344
    second: Rocks_03_0
  - first:
      213: 8558894220588607466
    second: Rocks_03_1
  - first:
      213: -4827892071770254761
    second: Rocks_03_2
  - first:
      213: -3604461481311086551
    second: Rocks_03_3
  - first:
      213: 6861511599589026595
    second: Rocks_03_4
  - first:
      213: -2417548612333543299
    second: Rocks_03_5
  - first:
      213: 2648045286040330466
    second: Rocks_03_6
  - first:
      213: 2763439710183631591
    second: Rocks_03_7
  - first:
      213: -550042046018977558
    second: Rocks_03_8
  - first:
      213: -4303920536046383644
    second: Rocks_03_9
  - first:
      213: 631639723832495603
    second: Rocks_03_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Rocks_03_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8892401eb205009e0800000000000000
      internalID: -1657236513483380344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_1
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae7e35852fc47c670800000000000000
      internalID: 8558894220588607466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_2
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75295307db1effcb0800000000000000
      internalID: -4827892071770254761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_3
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 928dfd741616afdc0800000000000000
      internalID: -3604461481311086551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_4
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32be9bb227cf83f50800000000000000
      internalID: 6861511599589026595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_5
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d70ee968854237ed0800000000000000
      internalID: -2417548612333543299
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_7
      rect:
        serializedVersion: 2
        x: 896
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ea24bb66c5b95620800000000000000
      internalID: 2763439710183631591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_03_6
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2290866d7dc74544894d3d46c82a1483
      internalID: 2648045286040330466
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 4a9295da44abfba4a9bf988979884e39
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Rocks_03_0: -1657236513483380344
      Rocks_03_1: 8558894220588607466
      Rocks_03_2: -4827892071770254761
      Rocks_03_3: -3604461481311086551
      Rocks_03_4: 6861511599589026595
      Rocks_03_5: -2417548612333543299
      Rocks_03_6: 2648045286040330466
      Rocks_03_7: 2763439710183631591
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

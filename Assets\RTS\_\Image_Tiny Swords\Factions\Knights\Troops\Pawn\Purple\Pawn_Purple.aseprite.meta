fileFormatVersion: 2
guid: 47a7da33d8e349c479112880760412bd
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 295
      width: 58
      height: 59
    spriteID: ba542cad0767518438c1ebfa204f1ba5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 295}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 295
      width: 58
      height: 59
    spriteID: 15d58cdf5ca0b9e43961a112fe847670
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 295}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5172414, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 363
      width: 58
      height: 57
    spriteID: 9256a7baea6c16844b9e76a5698eba3a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 363}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5172414, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 363
      width: 58
      height: 56
    spriteID: ddaa8560feea9884d82ff773d7b4bc0f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 363}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 363
      width: 58
      height: 57
    spriteID: ea4b9ddf7c1272e49a6228faea32b77b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 363}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 363
      width: 58
      height: 58
    spriteID: 5aa2e2d87719f6a43b565b68c7bed1e3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 363}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 295
      width: 58
      height: 59
    spriteID: 7d1a43bac7c80c842abf94972a06d4cc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 295}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 141
      y: 227
      width: 58
      height: 60
    spriteID: 5f3b8328ad2735b46ac74f162b8aebd7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 141, y: 227}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 295
      width: 58
      height: 59
    spriteID: 8290a70458968b843b1fe75474e480b2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 295}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 78
      width: 58
      height: 55
    spriteID: 7b03b4028a4c2ee4fadd0e6272aeddfb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 78}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48387095, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 227
      width: 62
      height: 60
    spriteID: d57e4e8c0d875604aa54bf389cecea52
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 227}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.49152538, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 227
      width: 59
      height: 59
    spriteID: 0144e2aaf7bb28f4a80f8cd7092fb3ee
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 227}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 309
      y: 78
      width: 58
      height: 55
    spriteID: fe02e6b07e4e5d34fb44cf94576d3f36
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 309, y: 78}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.49230766, y: -0.85333335}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 150
      y: 142
      width: 65
      height: 75
    spriteID: ce93fd2837a44e34c8705b199b331037
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 150, y: 142}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 142
      width: 65
      height: 77
    spriteID: 1f8b12d0f36ba36439b4d5a867a5b518
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 142}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 77
      y: 142
      width: 65
      height: 77
    spriteID: 051688b2c36d9a14c8ba9b050dc5be45
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 77, y: 142}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.34782606, y: -0.969697}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 92
      height: 66
    spriteID: c3e0ab697531c484a812a403d83f6843
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.3483146, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 414
      y: 4
      width: 89
      height: 56
    spriteID: 42f3708a3c96c1d4ba81257ee3ec913d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 414, y: 4}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.3421052, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 389
      y: 142
      width: 76
      height: 58
    spriteID: 25302c95e66271542a7523128d1c513d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 389, y: 142}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.654321, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 223
      y: 142
      width: 81
      height: 60
    spriteID: d701a6bcd6bcc744381f4f0b8f3686f0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 223, y: 142}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 222
      y: 4
      width: 88
      height: 64
    spriteID: 167cdafba6be0ac4d8c0c66e08940054
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 222, y: 4}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 318
      y: 4
      width: 88
      height: 64
    spriteID: 13486e5ec102df445a884e96c43b6211
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 318, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.3909091, y: -1.1851852}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 104
      y: 4
      width: 110
      height: 54
    spriteID: b55a28d3196314d448746c589bcc511c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 104, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33333334, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 78
      width: 99
      height: 56
    spriteID: a37bb70f6a23638448b5028cf3076149
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 78}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.53623194, y: -0.8484849}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 312
      y: 142
      width: 69
      height: 66
    spriteID: 943251afba34dcf4e8c94cb514c61a52
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 312, y: 142}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.49999997, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 207
      y: 227
      width: 58
      height: 60
    spriteID: ab599277794be524cb76f9dd8a255ccd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 207, y: 227}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 295
      width: 58
      height: 59
    spriteID: a0717875c199c2a4782dc14f394f9038
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 295}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.5172414, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 363
      width: 58
      height: 58
    spriteID: 4b030bfcf5af062408e891a39985b2cb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 363}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 363
      width: 58
      height: 57
    spriteID: e3b9e310afbe0ed49af30eacb0ed8c80
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 363}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 363
      width: 58
      height: 58
    spriteID: 83c6a4c1f91c9b84a94614de3090ade8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 363}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 295
      width: 58
      height: 59
    spriteID: c72d3c16d12933e40a25dfb24d190ad6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 295}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 227
      width: 58
      height: 60
    spriteID: 93d060219b91af249a48e520da2f1094
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 227}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.5172414, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 339
      y: 227
      width: 58
      height: 60
    spriteID: e6cf06c236a33984d99108108edfe1cc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 339, y: 227}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 78
      width: 58
      height: 56
    spriteID: 1e3f7d26d3342bf499daaf102f2ef441
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 78}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.46551725, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 405
      y: 227
      width: 58
      height: 60
    spriteID: 8ad83e2c8ce9d2d44b7c8cfa7232b519
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 405, y: 227}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.48275867, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 295
      width: 58
      height: 60
    spriteID: a16a4b9575cf7eb48aa6de6812514695
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 295}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 177
      y: 78
      width: 58
      height: 56
    spriteID: 17c09f05c73e2f74982755af2633bf77
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 177, y: 78}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1553337329
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Pawn_Purple
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: ba542cad0767518438c1ebfa204f1ba5
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: 15d58cdf5ca0b9e43961a112fe847670
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 57
      spriteId: 9256a7baea6c16844b9e76a5698eba3a
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 56
      spriteId: ddaa8560feea9884d82ff773d7b4bc0f
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: ea4b9ddf7c1272e49a6228faea32b77b
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 5aa2e2d87719f6a43b565b68c7bed1e3
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: 7d1a43bac7c80c842abf94972a06d4cc
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 5f3b8328ad2735b46ac74f162b8aebd7
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: 8290a70458968b843b1fe75474e480b2
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: 7b03b4028a4c2ee4fadd0e6272aeddfb
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 62
        height: 60
      spriteId: d57e4e8c0d875604aa54bf389cecea52
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 59
        height: 59
      spriteId: 0144e2aaf7bb28f4a80f8cd7092fb3ee
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: fe02e6b07e4e5d34fb44cf94576d3f36
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 65
        height: 75
      spriteId: ce93fd2837a44e34c8705b199b331037
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 1f8b12d0f36ba36439b4d5a867a5b518
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 051688b2c36d9a14c8ba9b050dc5be45
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 92
        height: 66
      spriteId: c3e0ab697531c484a812a403d83f6843
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 89
        height: 56
      spriteId: 42f3708a3c96c1d4ba81257ee3ec913d
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 64
        width: 76
        height: 58
      spriteId: 25302c95e66271542a7523128d1c513d
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 64
        width: 81
        height: 60
      spriteId: d701a6bcd6bcc744381f4f0b8f3686f0
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: 167cdafba6be0ac4d8c0c66e08940054
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: 13486e5ec102df445a884e96c43b6211
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 64
        width: 110
        height: 54
      spriteId: b55a28d3196314d448746c589bcc511c
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 99
        height: 56
      spriteId: a37bb70f6a23638448b5028cf3076149
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 56
        width: 69
        height: 66
      spriteId: 943251afba34dcf4e8c94cb514c61a52
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 60
      spriteId: ab599277794be524cb76f9dd8a255ccd
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: a0717875c199c2a4782dc14f394f9038
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 58
      spriteId: 4b030bfcf5af062408e891a39985b2cb
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: e3b9e310afbe0ed49af30eacb0ed8c80
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 83c6a4c1f91c9b84a94614de3090ade8
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: c72d3c16d12933e40a25dfb24d190ad6
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 93d060219b91af249a48e520da2f1094
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 60
      spriteId: e6cf06c236a33984d99108108edfe1cc
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 1e3f7d26d3342bf499daaf102f2ef441
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 64
        width: 58
        height: 60
      spriteId: 8ad83e2c8ce9d2d44b7c8cfa7232b519
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 60
      spriteId: a16a4b9575cf7eb48aa6de6812514695
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 17c09f05c73e2f74982755af2633bf77
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

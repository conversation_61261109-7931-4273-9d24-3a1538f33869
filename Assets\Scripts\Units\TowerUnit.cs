using System.Collections;
using UnityEngine;

public class TowerUnit : StructureUnit
{
    [SerializeField] private Projectile m_ProjectilePrefab;


    public override bool IsPlayer => true;

    public override void OnConstructionFinished()
    {
        base.OnConstructionFinished();


    }

    protected override void AfterConstructionUpdate()
    {
        if (CurrentState == UnitState.Dead) return;

        if (HasTarget)
        {
            TryAttackCurrentTarget();
        }
        else
        {
            if (TryFindClosestTarget(out Unit target))
            {
                SetTarget(target);
            }
        }
    }



    protected override void OnAttakReady(Unit target)
    {
        var projectile = Instantiate(m_ProjectilePrefab, transform.position, Quaternion.identity);

        projectile.Initialize(this, target, m_AutoAttackDamage);
    }

    protected override void RunDeadEffect()
    {
        StartCoroutine(FadeOut());
    }

    IEnumerator FadeOut()
    {
        float alpha = 1;

        while (alpha > 0)
        {
            alpha -= Time.deltaTime;
            m_SpriteRenderer.color = new Color(1, 1, 1, alpha);
            yield return null;
        }
        Destroy(gameObject);
    }


}
using TMPro;
using UnityEngine;

/// <summary>
/// يعرض متطلبات الموارد للإجراءات المختلفة في واجهة المستخدم. يوضح للاعب الموارد المطلوبة لإتمام عملية معينة.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class ResourceRequirementDisplay : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI m_GoldText;
    [SerializeField] private TextMeshProUGUI m_WoodText;


    public void Show(int reqGold, int reqWood)
    {
        m_GoldText.text = reqGold.ToString();
        m_WoodText.text = reqWood.ToString();
        UpdateColorRequirements(reqGold, reqWood);
    }


    /// <summary>
    /// تنفذ دالة UpdateColorRequirements وظيفة محددة في كلاس ResourceRequirementDisplay.
    /// </summary>
    /// <param name="reqGold">معامل من نوع int يستخدم في الدالة لتحديد reqGold.</param>
    /// <param name="reqWood">معامل من نوع int يستخدم في الدالة لتحديد reqWood.</param>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    void UpdateColorRequirements(int reqGold, int reqWood)
    {
        /// <summary>
        /// متغير من نوع var يستخدم في كلاس ResourceRequirementDisplay لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var manager = GameManager.Get();

        m_GoldText.color = manager.Gold >= reqGold ? new Color(0, 1, 0, 1) : new Color(1, 0, 0, 1);
        m_WoodText.color = manager.Wood >= reqWood ? new Color(0, 1, 0, 1) : new Color(1, 0, 0, 1);
    }





}

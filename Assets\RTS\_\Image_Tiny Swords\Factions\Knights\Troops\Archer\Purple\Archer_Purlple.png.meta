fileFormatVersion: 2
guid: b03bcc1b518fb904682ce905f291ed7f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5688775359304816724
    second: <PERSON><PERSON><PERSON><PERSON>_0
  - first:
      213: -4126191856018484511
    second: <PERSON><PERSON>lple_1
  - first:
      213: 7635862362993226562
    second: <PERSON><PERSON>_2
  - first:
      213: 4285742831580318532
    second: <PERSON><PERSON><PERSON>_3
  - first:
      213: -6419811096059114000
    second: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_4
  - first:
      213: 4681215906259792246
    second: <PERSON><PERSON><PERSON>ur<PERSON><PERSON>_5
  - first:
      213: 2289214738956296848
    second: <PERSON><PERSON><PERSON><PERSON>_6
  - first:
      213: -8079309372791655890
    second: <PERSON><PERSON><PERSON><PERSON>ple_7
  - first:
      213: -97632971312326491
    second: <PERSON><PERSON><PERSON>ur<PERSON><PERSON>_8
  - first:
      213: -3326047085963779301
    second: <PERSON><PERSON><PERSON><PERSON>_9
  - first:
      213: -2380513396119624467
    second: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_10
  - first:
      213: 1378135010453093076
    second: <PERSON><PERSON><PERSON><PERSON><PERSON>_11
  - first:
      213: 5305386049251263896
    second: <PERSON>_Purlple_12
  - first:
      213: -5528259549667078488
    second: <PERSON>_Purlple_13
  - first:
      213: 5889591374933604790
    second: Archer_Purlple_14
  - first:
      213: 7857757495352532263
    second: Archer_Purlple_15
  - first:
      213: -1069347171516835842
    second: Archer_Purlple_16
  - first:
      213: 7988009266505996358
    second: Archer_Purlple_17
  - first:
      213: 1382753673542916525
    second: Archer_Purlple_18
  - first:
      213: -8526647764782197225
    second: Archer_Purlple_19
  - first:
      213: 3375597542107262487
    second: Archer_Purlple_20
  - first:
      213: -6069009247562384567
    second: Archer_Purlple_21
  - first:
      213: 1049677329631348412
    second: Archer_Purlple_22
  - first:
      213: -4399044106217734894
    second: Archer_Purlple_23
  - first:
      213: -1668775173932952733
    second: Archer_Purlple_24
  - first:
      213: -1297906120030014998
    second: Archer_Purlple_25
  - first:
      213: -6755259609889727851
    second: Archer_Purlple_26
  - first:
      213: 2466663834094571780
    second: Archer_Purlple_27
  - first:
      213: -3050249036192697206
    second: Archer_Purlple_28
  - first:
      213: -6788493807621397670
    second: Archer_Purlple_29
  - first:
      213: 9154110952024273765
    second: Archer_Purlple_30
  - first:
      213: -8222187579108431701
    second: Archer_Purlple_31
  - first:
      213: 2462092204365764089
    second: Archer_Purlple_32
  - first:
      213: 1818537269942280862
    second: Archer_Purlple_33
  - first:
      213: 5503498321124818014
    second: Archer_Purlple_34
  - first:
      213: -8099803274382362636
    second: Archer_Purlple_35
  - first:
      213: -6056181142320568098
    second: Archer_Purlple_36
  - first:
      213: -3253237886399024324
    second: Archer_Purlple_37
  - first:
      213: 5267590031568540090
    second: Archer_Purlple_38
  - first:
      213: 7500193760998665738
    second: Archer_Purlple_39
  - first:
      213: -1948966030038604017
    second: Archer_Purlple_40
  - first:
      213: -3911437878754029842
    second: Archer_Purlple_41
  - first:
      213: 6429566032034093503
    second: Archer_Purlple_42
  - first:
      213: 4954597708737794283
    second: Archer_Purlple_43
  - first:
      213: 8632851899821884424
    second: Archer_Purlple_44
  - first:
      213: 4576600839900134867
    second: Archer_Purlple_45
  - first:
      213: -4608041067789015930
    second: Archer_Purlple_46
  - first:
      213: -4619797782547340376
    second: Archer_Purlple_47
  - first:
      213: 7351702866095259968
    second: Archer_Purlple_48
  - first:
      213: 8042907193256643409
    second: Archer_Purlple_49
  - first:
      213: 4899993079730403289
    second: Archer_Purlple_50
  - first:
      213: 6797711020358646903
    second: Archer_Purlple_51
  - first:
      213: 2904012338507247522
    second: Archer_Purlple_52
  - first:
      213: 184416479932246283
    second: Archer_Purlple_53
  - first:
      213: 1958120123509032486
    second: Archer_Purlple_54
  - first:
      213: 7518186691194325941
    second: Archer_Purlple_55
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Purlple_0
      rect:
        serializedVersion: 2
        x: 67
        y: 1209
        width: 64
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 458db528c0792fe40800000000000000
      internalID: 5688775359304816724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_1
      rect:
        serializedVersion: 2
        x: 259
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e298e25e52dcb6c0800000000000000
      internalID: -4126191856018484511
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_2
      rect:
        serializedVersion: 2
        x: 451
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2437cf3dc5808f960800000000000000
      internalID: 7635862362993226562
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_3
      rect:
        serializedVersion: 2
        x: 641
        y: 1209
        width: 69
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44b850306640a7b30800000000000000
      internalID: 4285742831580318532
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_4
      rect:
        serializedVersion: 2
        x: 833
        y: 1209
        width: 67
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0fd094e52df38e6a0800000000000000
      internalID: -6419811096059114000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_5
      rect:
        serializedVersion: 2
        x: 1026
        y: 1209
        width: 64
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 671f589801507f040800000000000000
      internalID: 4681215906259792246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_6
      rect:
        serializedVersion: 2
        x: 65
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09224122dace4cf10800000000000000
      internalID: 2289214738956296848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_7
      rect:
        serializedVersion: 2
        x: 258
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e22f4447ee680ef80800000000000000
      internalID: -8079309372791655890
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_8
      rect:
        serializedVersion: 2
        x: 451
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a81092745325aef0800000000000000
      internalID: -97632971312326491
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_9
      rect:
        serializedVersion: 2
        x: 636
        y: 1035
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b13e7e62cc187d1d0800000000000000
      internalID: -3326047085963779301
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_10
      rect:
        serializedVersion: 2
        x: 641
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de42b31cda7b6fed0800000000000000
      internalID: -2380513396119624467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_11
      rect:
        serializedVersion: 2
        x: 830
        y: 1030
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4daf5968d7e102310800000000000000
      internalID: 1378135010453093076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_12
      rect:
        serializedVersion: 2
        x: 834
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8954feda08480a940800000000000000
      internalID: 5305386049251263896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_13
      rect:
        serializedVersion: 2
        x: 1027
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8aecee3ca3da743b0800000000000000
      internalID: -5528259549667078488
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_14
      rect:
        serializedVersion: 2
        x: 66
        y: 825
        width: 67
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b93f1fa6280cb150800000000000000
      internalID: 5889591374933604790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_15
      rect:
        serializedVersion: 2
        x: 258
        y: 825
        width: 77
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 72d96a15fcc5c0d60800000000000000
      internalID: 7857757495352532263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_16
      rect:
        serializedVersion: 2
        x: 450
        y: 825
        width: 64
        height: 116
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef79b24c96ae821f0800000000000000
      internalID: -1069347171516835842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_17
      rect:
        serializedVersion: 2
        x: 636
        y: 825
        width: 68
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6485e980b1c1bde60800000000000000
      internalID: 7988009266505996358
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_18
      rect:
        serializedVersion: 2
        x: 828
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da95463d327803310800000000000000
      internalID: 1382753673542916525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_19
      rect:
        serializedVersion: 2
        x: 1020
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7165747b8034ba980800000000000000
      internalID: -8526647764782197225
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_20
      rect:
        serializedVersion: 2
        x: 1210
        y: 825
        width: 62
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71e2cf1741888de20800000000000000
      internalID: 3375597542107262487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_21
      rect:
        serializedVersion: 2
        x: 1238
        y: 891
        width: 43
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 94bbb7c8e3c86cba0800000000000000
      internalID: -6069009247562384567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_22
      rect:
        serializedVersion: 2
        x: 1410
        y: 825
        width: 57
        height: 71
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb6057948f3319e00800000000000000
      internalID: 1049677329631348412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_23
      rect:
        serializedVersion: 2
        x: 1439
        y: 875
        width: 35
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21dbd9941b473f2c0800000000000000
      internalID: -4399044106217734894
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_24
      rect:
        serializedVersion: 2
        x: 55
        y: 633
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3635cb322d157d8e0800000000000000
      internalID: -1668775173932952733
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_25
      rect:
        serializedVersion: 2
        x: 256
        y: 633
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae13ecba249ecfde0800000000000000
      internalID: -1297906120030014998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_26
      rect:
        serializedVersion: 2
        x: 448
        y: 633
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59a9afafc2f7042a0800000000000000
      internalID: -6755259609889727851
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_27
      rect:
        serializedVersion: 2
        x: 634
        y: 633
        width: 74
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 401928b20b95b3220800000000000000
      internalID: 2466663834094571780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_28
      rect:
        serializedVersion: 2
        x: 826
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a84b14999a65ba5d0800000000000000
      internalID: -3050249036192697206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_29
      rect:
        serializedVersion: 2
        x: 1018
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a53a9c459dc6ac1a0800000000000000
      internalID: -6788493807621397670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_30
      rect:
        serializedVersion: 2
        x: 1207
        y: 633
        width: 80
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5637be9096fe90f70800000000000000
      internalID: 9154110952024273765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_31
      rect:
        serializedVersion: 2
        x: 1408
        y: 633
        width: 68
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba8ec96b5fbe4ed80800000000000000
      internalID: -8222187579108431701
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_32
      rect:
        serializedVersion: 2
        x: 55
        y: 441
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9f5da7da0db1b2220800000000000000
      internalID: 2462092204365764089
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_33
      rect:
        serializedVersion: 2
        x: 256
        y: 441
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e92e7b1130ebc3910800000000000000
      internalID: 1818537269942280862
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_34
      rect:
        serializedVersion: 2
        x: 448
        y: 441
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5c7a94709a506c40800000000000000
      internalID: 5503498321124818014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_35
      rect:
        serializedVersion: 2
        x: 634
        y: 441
        width: 81
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f7b4e726d7b79f80800000000000000
      internalID: -8099803274382362636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_36
      rect:
        serializedVersion: 2
        x: 826
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed8209c865f14fba0800000000000000
      internalID: -6056181142320568098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_37
      rect:
        serializedVersion: 2
        x: 1018
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c33f753f06d2ad2d0800000000000000
      internalID: -3253237886399024324
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_38
      rect:
        serializedVersion: 2
        x: 1202
        y: 441
        width: 82
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab1180e893d3a1940800000000000000
      internalID: 5267590031568540090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_39
      rect:
        serializedVersion: 2
        x: 1408
        y: 441
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0e2de6ec7a061860800000000000000
      internalID: 7500193760998665738
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_40
      rect:
        serializedVersion: 2
        x: 55
        y: 249
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f07ee34c8b1e3f4e0800000000000000
      internalID: -1948966030038604017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_41
      rect:
        serializedVersion: 2
        x: 256
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eeecbd319f7c7b9c0800000000000000
      internalID: -3911437878754029842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_42
      rect:
        serializedVersion: 2
        x: 448
        y: 249
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb95f47ad386a3950800000000000000
      internalID: 6429566032034093503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_43
      rect:
        serializedVersion: 2
        x: 639
        y: 242
        width: 63
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be0d7429d5442c440800000000000000
      internalID: 4954597708737794283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_44
      rect:
        serializedVersion: 2
        x: 827
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8048077601d0ec770800000000000000
      internalID: 8632851899821884424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_45
      rect:
        serializedVersion: 2
        x: 1019
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3dd700d473a538f30800000000000000
      internalID: 4576600839900134867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_46
      rect:
        serializedVersion: 2
        x: 1205
        y: 239
        width: 76
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68426043013fc00c0800000000000000
      internalID: -4608041067789015930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_47
      rect:
        serializedVersion: 2
        x: 1402
        y: 243
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a3e02db46e23efb0800000000000000
      internalID: -4619797782547340376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_48
      rect:
        serializedVersion: 2
        x: 55
        y: 57
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04144d34ece760660800000000000000
      internalID: 7351702866095259968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_49
      rect:
        serializedVersion: 2
        x: 256
        y: 57
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15b0f77aa752e9f60800000000000000
      internalID: 8042907193256643409
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_50
      rect:
        serializedVersion: 2
        x: 448
        y: 54
        width: 86
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d7f337aeb5400440800000000000000
      internalID: 4899993079730403289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_51
      rect:
        serializedVersion: 2
        x: 639
        y: 42
        width: 57
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77c3c3e3822565e50800000000000000
      internalID: 6797711020358646903
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_52
      rect:
        serializedVersion: 2
        x: 831
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2af6314f5df1d4820800000000000000
      internalID: 2904012338507247522
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_53
      rect:
        serializedVersion: 2
        x: 1023
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b09e619bfcd2f8200800000000000000
      internalID: 184416479932246283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_54
      rect:
        serializedVersion: 2
        x: 1212
        y: 47
        width: 61
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 626cc45b0e3ac2b10800000000000000
      internalID: 1958120123509032486
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Purlple_55
      rect:
        serializedVersion: 2
        x: 1402
        y: 51
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bb05bb66f6f55860800000000000000
      internalID: 7518186691194325941
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Purlple_0: 5688775359304816724
      Archer_Purlple_1: -4126191856018484511
      Archer_Purlple_10: -2380513396119624467
      Archer_Purlple_11: 1378135010453093076
      Archer_Purlple_12: 5305386049251263896
      Archer_Purlple_13: -5528259549667078488
      Archer_Purlple_14: 5889591374933604790
      Archer_Purlple_15: 7857757495352532263
      Archer_Purlple_16: -1069347171516835842
      Archer_Purlple_17: 7988009266505996358
      Archer_Purlple_18: 1382753673542916525
      Archer_Purlple_19: -8526647764782197225
      Archer_Purlple_2: 7635862362993226562
      Archer_Purlple_20: 3375597542107262487
      Archer_Purlple_21: -6069009247562384567
      Archer_Purlple_22: 1049677329631348412
      Archer_Purlple_23: -4399044106217734894
      Archer_Purlple_24: -1668775173932952733
      Archer_Purlple_25: -1297906120030014998
      Archer_Purlple_26: -6755259609889727851
      Archer_Purlple_27: 2466663834094571780
      Archer_Purlple_28: -3050249036192697206
      Archer_Purlple_29: -6788493807621397670
      Archer_Purlple_3: 4285742831580318532
      Archer_Purlple_30: 9154110952024273765
      Archer_Purlple_31: -8222187579108431701
      Archer_Purlple_32: 2462092204365764089
      Archer_Purlple_33: 1818537269942280862
      Archer_Purlple_34: 5503498321124818014
      Archer_Purlple_35: -8099803274382362636
      Archer_Purlple_36: -6056181142320568098
      Archer_Purlple_37: -3253237886399024324
      Archer_Purlple_38: 5267590031568540090
      Archer_Purlple_39: 7500193760998665738
      Archer_Purlple_4: -6419811096059114000
      Archer_Purlple_40: -1948966030038604017
      Archer_Purlple_41: -3911437878754029842
      Archer_Purlple_42: 6429566032034093503
      Archer_Purlple_43: 4954597708737794283
      Archer_Purlple_44: 8632851899821884424
      Archer_Purlple_45: 4576600839900134867
      Archer_Purlple_46: -4608041067789015930
      Archer_Purlple_47: -4619797782547340376
      Archer_Purlple_48: 7351702866095259968
      Archer_Purlple_49: 8042907193256643409
      Archer_Purlple_5: 4681215906259792246
      Archer_Purlple_50: 4899993079730403289
      Archer_Purlple_51: 6797711020358646903
      Archer_Purlple_52: 2904012338507247522
      Archer_Purlple_53: 184416479932246283
      Archer_Purlple_54: 1958120123509032486
      Archer_Purlple_55: 7518186691194325941
      Archer_Purlple_6: 2289214738956296848
      Archer_Purlple_7: -8079309372791655890
      Archer_Purlple_8: -97632971312326491
      Archer_Purlple_9: -3326047085963779301
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

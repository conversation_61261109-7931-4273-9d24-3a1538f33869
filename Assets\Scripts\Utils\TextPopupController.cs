
using UnityEngine;

/// <summary>
/// يدير إنشاء وعرض النصوص المنبثقة في اللعبة. يتحكم في إنشاء وتوزيع النصوص المنبثقة في أماكن مختلفة.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class TextPopupController : MonoBehaviour
{
    [SerializeField] private TextPopup m_TextPopupPrefab;


    public void Spawn(string text, Vector3 position, Color color)
    {
        /// <summary>
        /// متغير من نوع TextPopup يستخدم في كلاس TextPopupController لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        TextPopup textPopup = Instantiate(m_TextPopupPrefab);

        textPopup.transform.position = position;

        textPopup.SetText(text, color);

    }



}// end class
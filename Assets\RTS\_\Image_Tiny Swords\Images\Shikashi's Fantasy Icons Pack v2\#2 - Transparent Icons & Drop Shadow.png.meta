fileFormatVersion: 2
guid: a154f96169eda8e4b902ff604cde854a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7568524073924095226
    second: '#2 - Transparent Icons & Drop Shadow_0'
  - first:
      213: 2584609656251380707
    second: '#2 - Transparent Icons & Drop Shadow_1'
  - first:
      213: -7483596328251532873
    second: '#2 - Transparent Icons & Drop Shadow_2'
  - first:
      213: 7539286595509921347
    second: '#2 - Transparent Icons & Drop Shadow_3'
  - first:
      213: 6948773886327953995
    second: '#2 - Transparent Icons & Drop Shadow_4'
  - first:
      213: -4768308916089180935
    second: '#2 - Transparent Icons & Drop Shadow_5'
  - first:
      213: 1842380838397042475
    second: '#2 - Transparent Icons & Drop Shadow_6'
  - first:
      213: 7685470968617421020
    second: '#2 - Transparent Icons & Drop Shadow_7'
  - first:
      213: 804741048422990168
    second: '#2 - Transparent Icons & Drop Shadow_8'
  - first:
      213: -5628875207666852851
    second: '#2 - Transparent Icons & Drop Shadow_9'
  - first:
      213: -8216494571090356096
    second: '#2 - Transparent Icons & Drop Shadow_10'
  - first:
      213: 4558633621754773204
    second: '#2 - Transparent Icons & Drop Shadow_11'
  - first:
      213: 1422253230955754594
    second: '#2 - Transparent Icons & Drop Shadow_12'
  - first:
      213: -3390942904790010266
    second: '#2 - Transparent Icons & Drop Shadow_13'
  - first:
      213: 6602636038343897003
    second: '#2 - Transparent Icons & Drop Shadow_14'
  - first:
      213: 3806784070233699775
    second: '#2 - Transparent Icons & Drop Shadow_15'
  - first:
      213: -413687423355243995
    second: '#2 - Transparent Icons & Drop Shadow_16'
  - first:
      213: -1245755177353750832
    second: '#2 - Transparent Icons & Drop Shadow_17'
  - first:
      213: -3923099718294011341
    second: '#2 - Transparent Icons & Drop Shadow_18'
  - first:
      213: 1599180710894039856
    second: '#2 - Transparent Icons & Drop Shadow_19'
  - first:
      213: -772803406078260477
    second: '#2 - Transparent Icons & Drop Shadow_20'
  - first:
      213: -7108182838404684714
    second: '#2 - Transparent Icons & Drop Shadow_21'
  - first:
      213: 5241480379350098711
    second: '#2 - Transparent Icons & Drop Shadow_22'
  - first:
      213: -3488153121969934050
    second: '#2 - Transparent Icons & Drop Shadow_23'
  - first:
      213: 6397100653707125053
    second: '#2 - Transparent Icons & Drop Shadow_24'
  - first:
      213: 3435686699319217275
    second: '#2 - Transparent Icons & Drop Shadow_25'
  - first:
      213: 6233947190329606780
    second: '#2 - Transparent Icons & Drop Shadow_26'
  - first:
      213: 8296119791664463575
    second: '#2 - Transparent Icons & Drop Shadow_27'
  - first:
      213: 8758660628681154190
    second: '#2 - Transparent Icons & Drop Shadow_28'
  - first:
      213: 5942704498625079392
    second: '#2 - Transparent Icons & Drop Shadow_29'
  - first:
      213: -5501215998907543212
    second: '#2 - Transparent Icons & Drop Shadow_30'
  - first:
      213: -4370429751426449405
    second: '#2 - Transparent Icons & Drop Shadow_31'
  - first:
      213: 8573033428421157452
    second: '#2 - Transparent Icons & Drop Shadow_32'
  - first:
      213: 5540574519761578848
    second: '#2 - Transparent Icons & Drop Shadow_33'
  - first:
      213: 7183686000987961966
    second: '#2 - Transparent Icons & Drop Shadow_34'
  - first:
      213: -7516765442453985350
    second: '#2 - Transparent Icons & Drop Shadow_35'
  - first:
      213: 4697799021792961516
    second: '#2 - Transparent Icons & Drop Shadow_36'
  - first:
      213: 5864141120653579955
    second: '#2 - Transparent Icons & Drop Shadow_37'
  - first:
      213: 3378112070155288112
    second: '#2 - Transparent Icons & Drop Shadow_38'
  - first:
      213: 4884337891824352890
    second: '#2 - Transparent Icons & Drop Shadow_39'
  - first:
      213: 8579901613499352774
    second: '#2 - Transparent Icons & Drop Shadow_40'
  - first:
      213: -5692852124484312159
    second: '#2 - Transparent Icons & Drop Shadow_41'
  - first:
      213: 8469456256804645828
    second: '#2 - Transparent Icons & Drop Shadow_42'
  - first:
      213: 7788705131787259084
    second: '#2 - Transparent Icons & Drop Shadow_43'
  - first:
      213: 8244745962220302406
    second: '#2 - Transparent Icons & Drop Shadow_44'
  - first:
      213: 8013616204054527717
    second: '#2 - Transparent Icons & Drop Shadow_45'
  - first:
      213: -3259243744252596725
    second: '#2 - Transparent Icons & Drop Shadow_46'
  - first:
      213: -5164983277317961741
    second: '#2 - Transparent Icons & Drop Shadow_47'
  - first:
      213: 183573810413276680
    second: '#2 - Transparent Icons & Drop Shadow_48'
  - first:
      213: 1704530735959705138
    second: '#2 - Transparent Icons & Drop Shadow_49'
  - first:
      213: -7907440099980241561
    second: '#2 - Transparent Icons & Drop Shadow_50'
  - first:
      213: 969911064562707275
    second: '#2 - Transparent Icons & Drop Shadow_51'
  - first:
      213: 8921443712075806451
    second: '#2 - Transparent Icons & Drop Shadow_52'
  - first:
      213: 2105162914696504809
    second: '#2 - Transparent Icons & Drop Shadow_53'
  - first:
      213: 5191580535614204767
    second: '#2 - Transparent Icons & Drop Shadow_54'
  - first:
      213: -950096140469595123
    second: '#2 - Transparent Icons & Drop Shadow_55'
  - first:
      213: 3583167426799192178
    second: '#2 - Transparent Icons & Drop Shadow_56'
  - first:
      213: -7405286937500457954
    second: '#2 - Transparent Icons & Drop Shadow_57'
  - first:
      213: 821283588467877935
    second: '#2 - Transparent Icons & Drop Shadow_58'
  - first:
      213: -6451020461040997553
    second: '#2 - Transparent Icons & Drop Shadow_59'
  - first:
      213: -5443706011219761410
    second: '#2 - Transparent Icons & Drop Shadow_60'
  - first:
      213: 2770867968450680294
    second: '#2 - Transparent Icons & Drop Shadow_61'
  - first:
      213: -5606345008730570156
    second: '#2 - Transparent Icons & Drop Shadow_62'
  - first:
      213: -2027895187021310853
    second: '#2 - Transparent Icons & Drop Shadow_63'
  - first:
      213: -7822049742711148976
    second: '#2 - Transparent Icons & Drop Shadow_64'
  - first:
      213: -5813384638183593889
    second: '#2 - Transparent Icons & Drop Shadow_65'
  - first:
      213: 9158683398484645407
    second: '#2 - Transparent Icons & Drop Shadow_66'
  - first:
      213: -7656981068869977111
    second: '#2 - Transparent Icons & Drop Shadow_67'
  - first:
      213: 1056014759243546959
    second: '#2 - Transparent Icons & Drop Shadow_68'
  - first:
      213: 249412086356771944
    second: '#2 - Transparent Icons & Drop Shadow_69'
  - first:
      213: 3964465338351796908
    second: '#2 - Transparent Icons & Drop Shadow_70'
  - first:
      213: 4240453425555717549
    second: '#2 - Transparent Icons & Drop Shadow_71'
  - first:
      213: -7387093150480851852
    second: '#2 - Transparent Icons & Drop Shadow_72'
  - first:
      213: -11322859963825669
    second: '#2 - Transparent Icons & Drop Shadow_73'
  - first:
      213: 7526374455504009102
    second: '#2 - Transparent Icons & Drop Shadow_74'
  - first:
      213: -7399256461421256914
    second: '#2 - Transparent Icons & Drop Shadow_75'
  - first:
      213: -6184507285319492425
    second: '#2 - Transparent Icons & Drop Shadow_76'
  - first:
      213: -9147071376023271221
    second: '#2 - Transparent Icons & Drop Shadow_77'
  - first:
      213: 7819060303277530886
    second: '#2 - Transparent Icons & Drop Shadow_78'
  - first:
      213: 4620036233086308783
    second: '#2 - Transparent Icons & Drop Shadow_79'
  - first:
      213: -6312372248548970523
    second: '#2 - Transparent Icons & Drop Shadow_80'
  - first:
      213: -2581948243186363227
    second: '#2 - Transparent Icons & Drop Shadow_81'
  - first:
      213: 2831401197825969033
    second: '#2 - Transparent Icons & Drop Shadow_82'
  - first:
      213: 5688385721555123385
    second: '#2 - Transparent Icons & Drop Shadow_83'
  - first:
      213: 1484917075010711910
    second: '#2 - Transparent Icons & Drop Shadow_84'
  - first:
      213: 8734278466190218363
    second: '#2 - Transparent Icons & Drop Shadow_85'
  - first:
      213: 9153715404705889837
    second: '#2 - Transparent Icons & Drop Shadow_86'
  - first:
      213: 5970681058612394725
    second: '#2 - Transparent Icons & Drop Shadow_87'
  - first:
      213: 5576558967255421441
    second: '#2 - Transparent Icons & Drop Shadow_88'
  - first:
      213: 5862109787822352134
    second: '#2 - Transparent Icons & Drop Shadow_89'
  - first:
      213: -6608148472023216053
    second: '#2 - Transparent Icons & Drop Shadow_90'
  - first:
      213: -7492221114004634305
    second: '#2 - Transparent Icons & Drop Shadow_91'
  - first:
      213: -8659921585605857471
    second: '#2 - Transparent Icons & Drop Shadow_92'
  - first:
      213: -8072734333329823742
    second: '#2 - Transparent Icons & Drop Shadow_93'
  - first:
      213: 6351365340128835113
    second: '#2 - Transparent Icons & Drop Shadow_94'
  - first:
      213: -3611065304843812169
    second: '#2 - Transparent Icons & Drop Shadow_95'
  - first:
      213: -7911167414898439247
    second: '#2 - Transparent Icons & Drop Shadow_96'
  - first:
      213: 1248619470624631663
    second: '#2 - Transparent Icons & Drop Shadow_97'
  - first:
      213: -4959899092030057782
    second: '#2 - Transparent Icons & Drop Shadow_98'
  - first:
      213: 5290622554995841036
    second: '#2 - Transparent Icons & Drop Shadow_99'
  - first:
      213: -7324339567714289606
    second: '#2 - Transparent Icons & Drop Shadow_100'
  - first:
      213: 4106480638470415659
    second: '#2 - Transparent Icons & Drop Shadow_101'
  - first:
      213: -8354208325991750120
    second: '#2 - Transparent Icons & Drop Shadow_102'
  - first:
      213: -5708691651963701962
    second: '#2 - Transparent Icons & Drop Shadow_103'
  - first:
      213: -978527167860701757
    second: '#2 - Transparent Icons & Drop Shadow_104'
  - first:
      213: 3332671309235019512
    second: '#2 - Transparent Icons & Drop Shadow_105'
  - first:
      213: -8753393225063967389
    second: '#2 - Transparent Icons & Drop Shadow_106'
  - first:
      213: 1305000423023697474
    second: '#2 - Transparent Icons & Drop Shadow_107'
  - first:
      213: 2242127617640288418
    second: '#2 - Transparent Icons & Drop Shadow_108'
  - first:
      213: -8508503817635240546
    second: '#2 - Transparent Icons & Drop Shadow_109'
  - first:
      213: -8192483641660394284
    second: '#2 - Transparent Icons & Drop Shadow_110'
  - first:
      213: 2399578771559581788
    second: '#2 - Transparent Icons & Drop Shadow_111'
  - first:
      213: -1192392031901719504
    second: '#2 - Transparent Icons & Drop Shadow_112'
  - first:
      213: -5484451948897457147
    second: '#2 - Transparent Icons & Drop Shadow_113'
  - first:
      213: 429199960171848815
    second: '#2 - Transparent Icons & Drop Shadow_114'
  - first:
      213: 3653192047798930668
    second: '#2 - Transparent Icons & Drop Shadow_115'
  - first:
      213: 971504893046722317
    second: '#2 - Transparent Icons & Drop Shadow_116'
  - first:
      213: 7423710207338370629
    second: '#2 - Transparent Icons & Drop Shadow_117'
  - first:
      213: -143208548368856878
    second: '#2 - Transparent Icons & Drop Shadow_118'
  - first:
      213: 6812396857366647474
    second: '#2 - Transparent Icons & Drop Shadow_119'
  - first:
      213: 7281626079675469898
    second: '#2 - Transparent Icons & Drop Shadow_120'
  - first:
      213: 6229783472119169768
    second: '#2 - Transparent Icons & Drop Shadow_121'
  - first:
      213: 7723507555042334362
    second: '#2 - Transparent Icons & Drop Shadow_122'
  - first:
      213: 5895743733560592253
    second: '#2 - Transparent Icons & Drop Shadow_123'
  - first:
      213: -7304476087515427257
    second: '#2 - Transparent Icons & Drop Shadow_124'
  - first:
      213: 766568942560507546
    second: '#2 - Transparent Icons & Drop Shadow_125'
  - first:
      213: 5702301827914847618
    second: '#2 - Transparent Icons & Drop Shadow_126'
  - first:
      213: 7071413685948691855
    second: '#2 - Transparent Icons & Drop Shadow_127'
  - first:
      213: -4493119780215096621
    second: '#2 - Transparent Icons & Drop Shadow_128'
  - first:
      213: 3483340098289625841
    second: '#2 - Transparent Icons & Drop Shadow_129'
  - first:
      213: 7356012238514178944
    second: '#2 - Transparent Icons & Drop Shadow_130'
  - first:
      213: 9177197982001231785
    second: '#2 - Transparent Icons & Drop Shadow_131'
  - first:
      213: 7587327949842529059
    second: '#2 - Transparent Icons & Drop Shadow_132'
  - first:
      213: 830162279942836597
    second: '#2 - Transparent Icons & Drop Shadow_133'
  - first:
      213: 5180278899884751490
    second: '#2 - Transparent Icons & Drop Shadow_134'
  - first:
      213: 2093714518365425205
    second: '#2 - Transparent Icons & Drop Shadow_135'
  - first:
      213: -3122399997724876031
    second: '#2 - Transparent Icons & Drop Shadow_136'
  - first:
      213: -8888933011482793071
    second: '#2 - Transparent Icons & Drop Shadow_137'
  - first:
      213: -157310728770800790
    second: '#2 - Transparent Icons & Drop Shadow_138'
  - first:
      213: 7322225789008322440
    second: '#2 - Transparent Icons & Drop Shadow_139'
  - first:
      213: 6768465706591882896
    second: '#2 - Transparent Icons & Drop Shadow_140'
  - first:
      213: 4876574062683277506
    second: '#2 - Transparent Icons & Drop Shadow_141'
  - first:
      213: -2129252301932304945
    second: '#2 - Transparent Icons & Drop Shadow_142'
  - first:
      213: -4672503218770899442
    second: '#2 - Transparent Icons & Drop Shadow_143'
  - first:
      213: 9216980979689419991
    second: '#2 - Transparent Icons & Drop Shadow_144'
  - first:
      213: 8099792054545138375
    second: '#2 - Transparent Icons & Drop Shadow_145'
  - first:
      213: -6599546582249927157
    second: '#2 - Transparent Icons & Drop Shadow_146'
  - first:
      213: 4487214514996280067
    second: '#2 - Transparent Icons & Drop Shadow_147'
  - first:
      213: -1388334726044338524
    second: '#2 - Transparent Icons & Drop Shadow_148'
  - first:
      213: -1837074531912217946
    second: '#2 - Transparent Icons & Drop Shadow_149'
  - first:
      213: 3563044760079015136
    second: '#2 - Transparent Icons & Drop Shadow_150'
  - first:
      213: 6678823951699107959
    second: '#2 - Transparent Icons & Drop Shadow_151'
  - first:
      213: 5594105880233148557
    second: '#2 - Transparent Icons & Drop Shadow_152'
  - first:
      213: 8635983665342292221
    second: '#2 - Transparent Icons & Drop Shadow_153'
  - first:
      213: 8063304825762643347
    second: '#2 - Transparent Icons & Drop Shadow_154'
  - first:
      213: 7311480069754638794
    second: '#2 - Transparent Icons & Drop Shadow_155'
  - first:
      213: 7861745911137020964
    second: '#2 - Transparent Icons & Drop Shadow_156'
  - first:
      213: -8405252794140944200
    second: '#2 - Transparent Icons & Drop Shadow_157'
  - first:
      213: 5498310778425241025
    second: '#2 - Transparent Icons & Drop Shadow_158'
  - first:
      213: 2976515441447516915
    second: '#2 - Transparent Icons & Drop Shadow_159'
  - first:
      213: 3045905129727160601
    second: '#2 - Transparent Icons & Drop Shadow_160'
  - first:
      213: 6689822577633031747
    second: '#2 - Transparent Icons & Drop Shadow_161'
  - first:
      213: -6596681222949706149
    second: '#2 - Transparent Icons & Drop Shadow_162'
  - first:
      213: -7176768192816253104
    second: '#2 - Transparent Icons & Drop Shadow_163'
  - first:
      213: -3591317230858283200
    second: '#2 - Transparent Icons & Drop Shadow_164'
  - first:
      213: 1954068733071698231
    second: '#2 - Transparent Icons & Drop Shadow_165'
  - first:
      213: -8409008894314519048
    second: '#2 - Transparent Icons & Drop Shadow_166'
  - first:
      213: 6975746642326818321
    second: '#2 - Transparent Icons & Drop Shadow_167'
  - first:
      213: -9034327563270943197
    second: '#2 - Transparent Icons & Drop Shadow_168'
  - first:
      213: 3628495426549715520
    second: '#2 - Transparent Icons & Drop Shadow_169'
  - first:
      213: -3611524022231650080
    second: '#2 - Transparent Icons & Drop Shadow_170'
  - first:
      213: -3154850044925739375
    second: '#2 - Transparent Icons & Drop Shadow_171'
  - first:
      213: -2490587925212428839
    second: '#2 - Transparent Icons & Drop Shadow_172'
  - first:
      213: -3032231566403425396
    second: '#2 - Transparent Icons & Drop Shadow_173'
  - first:
      213: 745079209864166825
    second: '#2 - Transparent Icons & Drop Shadow_174'
  - first:
      213: -9202433882124039654
    second: '#2 - Transparent Icons & Drop Shadow_175'
  - first:
      213: -5141525515687405265
    second: '#2 - Transparent Icons & Drop Shadow_176'
  - first:
      213: -6412529325694471836
    second: '#2 - Transparent Icons & Drop Shadow_177'
  - first:
      213: -3046216337235638372
    second: '#2 - Transparent Icons & Drop Shadow_178'
  - first:
      213: -8678670238620706453
    second: '#2 - Transparent Icons & Drop Shadow_179'
  - first:
      213: -8936463708450247864
    second: '#2 - Transparent Icons & Drop Shadow_180'
  - first:
      213: 8421659737649855299
    second: '#2 - Transparent Icons & Drop Shadow_181'
  - first:
      213: 8100173508216559957
    second: '#2 - Transparent Icons & Drop Shadow_182'
  - first:
      213: -4830942296010914177
    second: '#2 - Transparent Icons & Drop Shadow_183'
  - first:
      213: 3144352708175808623
    second: '#2 - Transparent Icons & Drop Shadow_184'
  - first:
      213: 9051664168745105658
    second: '#2 - Transparent Icons & Drop Shadow_185'
  - first:
      213: -4300520847608141783
    second: '#2 - Transparent Icons & Drop Shadow_186'
  - first:
      213: -4675753704938881013
    second: '#2 - Transparent Icons & Drop Shadow_187'
  - first:
      213: -8860478429687436892
    second: '#2 - Transparent Icons & Drop Shadow_188'
  - first:
      213: 8504972468082110267
    second: '#2 - Transparent Icons & Drop Shadow_189'
  - first:
      213: -8331572242567495242
    second: '#2 - Transparent Icons & Drop Shadow_190'
  - first:
      213: -4495425090866840351
    second: '#2 - Transparent Icons & Drop Shadow_191'
  - first:
      213: -4012141969497314876
    second: '#2 - Transparent Icons & Drop Shadow_192'
  - first:
      213: 6010118646404486385
    second: '#2 - Transparent Icons & Drop Shadow_193'
  - first:
      213: -5335074340823809906
    second: '#2 - Transparent Icons & Drop Shadow_194'
  - first:
      213: -6037611042952805034
    second: '#2 - Transparent Icons & Drop Shadow_195'
  - first:
      213: -5462704634393959283
    second: '#2 - Transparent Icons & Drop Shadow_196'
  - first:
      213: 7231550048576880314
    second: '#2 - Transparent Icons & Drop Shadow_197'
  - first:
      213: 5428956538372255841
    second: '#2 - Transparent Icons & Drop Shadow_198'
  - first:
      213: 5162288378122022885
    second: '#2 - Transparent Icons & Drop Shadow_199'
  - first:
      213: 7026316078318186500
    second: '#2 - Transparent Icons & Drop Shadow_200'
  - first:
      213: 1374670549757452355
    second: '#2 - Transparent Icons & Drop Shadow_201'
  - first:
      213: -234197381693910589
    second: '#2 - Transparent Icons & Drop Shadow_202'
  - first:
      213: -8423117483235724136
    second: '#2 - Transparent Icons & Drop Shadow_203'
  - first:
      213: 1145404032394259606
    second: '#2 - Transparent Icons & Drop Shadow_204'
  - first:
      213: 1162753139007183908
    second: '#2 - Transparent Icons & Drop Shadow_205'
  - first:
      213: -4958458704909742478
    second: '#2 - Transparent Icons & Drop Shadow_206'
  - first:
      213: 466456548507416648
    second: '#2 - Transparent Icons & Drop Shadow_207'
  - first:
      213: -8951304189195089106
    second: '#2 - Transparent Icons & Drop Shadow_208'
  - first:
      213: 7964594000054679645
    second: '#2 - Transparent Icons & Drop Shadow_209'
  - first:
      213: 8757547565198951967
    second: '#2 - Transparent Icons & Drop Shadow_210'
  - first:
      213: -726719379730627744
    second: '#2 - Transparent Icons & Drop Shadow_211'
  - first:
      213: -7853998414096601524
    second: '#2 - Transparent Icons & Drop Shadow_212'
  - first:
      213: 7058349591738051420
    second: '#2 - Transparent Icons & Drop Shadow_213'
  - first:
      213: 4951166039389310982
    second: '#2 - Transparent Icons & Drop Shadow_214'
  - first:
      213: -5526351751305055198
    second: '#2 - Transparent Icons & Drop Shadow_215'
  - first:
      213: -1951230028792470334
    second: '#2 - Transparent Icons & Drop Shadow_216'
  - first:
      213: -6617779257180967827
    second: '#2 - Transparent Icons & Drop Shadow_217'
  - first:
      213: -8260647028948093834
    second: '#2 - Transparent Icons & Drop Shadow_218'
  - first:
      213: -7642161154661158119
    second: '#2 - Transparent Icons & Drop Shadow_219'
  - first:
      213: -5291536130655649182
    second: '#2 - Transparent Icons & Drop Shadow_220'
  - first:
      213: 864916451691004800
    second: '#2 - Transparent Icons & Drop Shadow_221'
  - first:
      213: 4918712904808858685
    second: '#2 - Transparent Icons & Drop Shadow_222'
  - first:
      213: -3513621829382666981
    second: '#2 - Transparent Icons & Drop Shadow_223'
  - first:
      213: -8877052817109994687
    second: '#2 - Transparent Icons & Drop Shadow_224'
  - first:
      213: -5258166664456349547
    second: '#2 - Transparent Icons & Drop Shadow_225'
  - first:
      213: -4913508092620749888
    second: '#2 - Transparent Icons & Drop Shadow_226'
  - first:
      213: 2638214239362370348
    second: '#2 - Transparent Icons & Drop Shadow_227'
  - first:
      213: -6765587114449210688
    second: '#2 - Transparent Icons & Drop Shadow_228'
  - first:
      213: 6142543257898026600
    second: '#2 - Transparent Icons & Drop Shadow_229'
  - first:
      213: -2184882346354886025
    second: '#2 - Transparent Icons & Drop Shadow_230'
  - first:
      213: 7774254553147042332
    second: '#2 - Transparent Icons & Drop Shadow_231'
  - first:
      213: -550771597486948442
    second: '#2 - Transparent Icons & Drop Shadow_232'
  - first:
      213: 5403132134260291811
    second: '#2 - Transparent Icons & Drop Shadow_233'
  - first:
      213: 4636447500729424692
    second: '#2 - Transparent Icons & Drop Shadow_234'
  - first:
      213: 193776288328875908
    second: '#2 - Transparent Icons & Drop Shadow_235'
  - first:
      213: -5205662494574892940
    second: '#2 - Transparent Icons & Drop Shadow_236'
  - first:
      213: 4553488662114205020
    second: '#2 - Transparent Icons & Drop Shadow_237'
  - first:
      213: 5460315946373779467
    second: '#2 - Transparent Icons & Drop Shadow_238'
  - first:
      213: -7957652584325686945
    second: '#2 - Transparent Icons & Drop Shadow_239'
  - first:
      213: 3745081088445577032
    second: '#2 - Transparent Icons & Drop Shadow_240'
  - first:
      213: 3406941685592084098
    second: '#2 - Transparent Icons & Drop Shadow_241'
  - first:
      213: -8459016738362980394
    second: '#2 - Transparent Icons & Drop Shadow_242'
  - first:
      213: 7239756022324072331
    second: '#2 - Transparent Icons & Drop Shadow_243'
  - first:
      213: 7375747531102694761
    second: '#2 - Transparent Icons & Drop Shadow_244'
  - first:
      213: 5005549167977470398
    second: '#2 - Transparent Icons & Drop Shadow_245'
  - first:
      213: 41946356324131235
    second: '#2 - Transparent Icons & Drop Shadow_246'
  - first:
      213: -1203358537988948490
    second: '#2 - Transparent Icons & Drop Shadow_247'
  - first:
      213: 4775819950423338855
    second: '#2 - Transparent Icons & Drop Shadow_248'
  - first:
      213: -1241399281213930050
    second: '#2 - Transparent Icons & Drop Shadow_249'
  - first:
      213: -1847224337267446819
    second: '#2 - Transparent Icons & Drop Shadow_250'
  - first:
      213: 4452902665718519870
    second: '#2 - Transparent Icons & Drop Shadow_251'
  - first:
      213: 2129610239855971139
    second: '#2 - Transparent Icons & Drop Shadow_252'
  - first:
      213: -1381023906944720389
    second: '#2 - Transparent Icons & Drop Shadow_253'
  - first:
      213: 6152015384126895060
    second: '#2 - Transparent Icons & Drop Shadow_254'
  - first:
      213: 3792681950590231387
    second: '#2 - Transparent Icons & Drop Shadow_255'
  - first:
      213: -6750408783741295316
    second: '#2 - Transparent Icons & Drop Shadow_256'
  - first:
      213: 352957040730354879
    second: '#2 - Transparent Icons & Drop Shadow_257'
  - first:
      213: -3549148016663579148
    second: '#2 - Transparent Icons & Drop Shadow_258'
  - first:
      213: -5523013176390652018
    second: '#2 - Transparent Icons & Drop Shadow_259'
  - first:
      213: 1849968632212408060
    second: '#2 - Transparent Icons & Drop Shadow_260'
  - first:
      213: 1210564344755463477
    second: '#2 - Transparent Icons & Drop Shadow_261'
  - first:
      213: -9155644283013557474
    second: '#2 - Transparent Icons & Drop Shadow_262'
  - first:
      213: -3466711498889415249
    second: '#2 - Transparent Icons & Drop Shadow_263'
  - first:
      213: 5466908159382051022
    second: '#2 - Transparent Icons & Drop Shadow_264'
  - first:
      213: -5992891883500385013
    second: '#2 - Transparent Icons & Drop Shadow_265'
  - first:
      213: -8573575788960047104
    second: '#2 - Transparent Icons & Drop Shadow_266'
  - first:
      213: -8712343438922201611
    second: '#2 - Transparent Icons & Drop Shadow_267'
  - first:
      213: 715997935203308138
    second: '#2 - Transparent Icons & Drop Shadow_268'
  - first:
      213: -2796071702963426276
    second: '#2 - Transparent Icons & Drop Shadow_269'
  - first:
      213: -5977185819954782074
    second: '#2 - Transparent Icons & Drop Shadow_270'
  - first:
      213: 7969046993835574059
    second: '#2 - Transparent Icons & Drop Shadow_271'
  - first:
      213: -5191351630652530774
    second: '#2 - Transparent Icons & Drop Shadow_272'
  - first:
      213: 1435083807972979427
    second: '#2 - Transparent Icons & Drop Shadow_273'
  - first:
      213: -6685496933419510379
    second: '#2 - Transparent Icons & Drop Shadow_274'
  - first:
      213: 5189726397073159175
    second: '#2 - Transparent Icons & Drop Shadow_275'
  - first:
      213: 4093176322471666036
    second: '#2 - Transparent Icons & Drop Shadow_276'
  - first:
      213: 5333913983957367281
    second: '#2 - Transparent Icons & Drop Shadow_277'
  - first:
      213: 6145633931487486579
    second: '#2 - Transparent Icons & Drop Shadow_278'
  - first:
      213: -3384462780974178560
    second: '#2 - Transparent Icons & Drop Shadow_279'
  - first:
      213: -5221447207604669579
    second: '#2 - Transparent Icons & Drop Shadow_280'
  - first:
      213: -1045323024871736742
    second: '#2 - Transparent Icons & Drop Shadow_281'
  - first:
      213: -7947354301065827530
    second: '#2 - Transparent Icons & Drop Shadow_282'
  - first:
      213: 8966060540651279181
    second: '#2 - Transparent Icons & Drop Shadow_283'
  - first:
      213: -2915997874020038260
    second: '#2 - Transparent Icons & Drop Shadow_284'
  - first:
      213: 684824644300870239
    second: '#2 - Transparent Icons & Drop Shadow_285'
  - first:
      213: -3694360758804291836
    second: '#2 - Transparent Icons & Drop Shadow_286'
  - first:
      213: 2313763699520662351
    second: '#2 - Transparent Icons & Drop Shadow_287'
  - first:
      213: -7899408503067056276
    second: '#2 - Transparent Icons & Drop Shadow_288'
  - first:
      213: 1292018081667124281
    second: '#2 - Transparent Icons & Drop Shadow_289'
  - first:
      213: -217254596849507150
    second: '#2 - Transparent Icons & Drop Shadow_290'
  - first:
      213: -8648913370475640610
    second: '#2 - Transparent Icons & Drop Shadow_291'
  - first:
      213: 1908208174507688777
    second: '#2 - Transparent Icons & Drop Shadow_292'
  - first:
      213: -7038974878340436006
    second: '#2 - Transparent Icons & Drop Shadow_293'
  - first:
      213: 2106597909263342591
    second: '#2 - Transparent Icons & Drop Shadow_294'
  - first:
      213: -1043816623829228471
    second: '#2 - Transparent Icons & Drop Shadow_295'
  - first:
      213: 8162344005663537131
    second: '#2 - Transparent Icons & Drop Shadow_296'
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_0'
      rect:
        serializedVersion: 2
        x: 0
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60313cb547337f690800000000000000
      internalID: -7568524073924095226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_1'
      rect:
        serializedVersion: 2
        x: 32
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e378ebd9c06ed320800000000000000
      internalID: 2584609656251380707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_2'
      rect:
        serializedVersion: 2
        x: 64
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b9562c0acce42890800000000000000
      internalID: -7483596328251532873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_3'
      rect:
        serializedVersion: 2
        x: 96
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 346ab39b63de0a860800000000000000
      internalID: 7539286595509921347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_4'
      rect:
        serializedVersion: 2
        x: 128
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4a961f88010f6060800000000000000
      internalID: 6948773886327953995
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_5'
      rect:
        serializedVersion: 2
        x: 160
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fc2d2ded4093ddb0800000000000000
      internalID: -4768308916089180935
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_6'
      rect:
        serializedVersion: 2
        x: 192
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2f54a57c93719910800000000000000
      internalID: 1842380838397042475
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_7'
      rect:
        serializedVersion: 2
        x: 224
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd8c408602748aa60800000000000000
      internalID: 7685470968617421020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_8'
      rect:
        serializedVersion: 2
        x: 256
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8518d6748b30b2b00800000000000000
      internalID: 804741048422990168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_9'
      rect:
        serializedVersion: 2
        x: 288
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0c930a52d732e1b0800000000000000
      internalID: -5628875207666852851
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_10'
      rect:
        serializedVersion: 2
        x: 320
        y: 835
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 088db3f68b529fd80800000000000000
      internalID: -8216494571090356096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_11'
      rect:
        serializedVersion: 2
        x: 0
        y: 803
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4de78d55025834f30800000000000000
      internalID: 4558633621754773204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_12'
      rect:
        serializedVersion: 2
        x: 32
        y: 803
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 264f4c337cbdcb310800000000000000
      internalID: 1422253230955754594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_13'
      rect:
        serializedVersion: 2
        x: 64
        y: 803
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6663c52e463f0f0d0800000000000000
      internalID: -3390942904790010266
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_14'
      rect:
        serializedVersion: 2
        x: 96
        y: 803
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: baf2eae9e7641ab50800000000000000
      internalID: 6602636038343897003
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_15'
      rect:
        serializedVersion: 2
        x: 128
        y: 803
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb535e251f964d430800000000000000
      internalID: 3806784070233699775
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_16'
      rect:
        serializedVersion: 2
        x: 0
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5223dd2fa79424af0800000000000000
      internalID: -413687423355243995
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_17'
      rect:
        serializedVersion: 2
        x: 32
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d6d9b8344036bee0800000000000000
      internalID: -1245755177353750832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_18'
      rect:
        serializedVersion: 2
        x: 64
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33286a877995e89c0800000000000000
      internalID: -3923099718294011341
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_19'
      rect:
        serializedVersion: 2
        x: 96
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 033fe7c326e613610800000000000000
      internalID: 1599180710894039856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_20'
      rect:
        serializedVersion: 2
        x: 128
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30b4e7b85637645f0800000000000000
      internalID: -772803406078260477
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_21'
      rect:
        serializedVersion: 2
        x: 160
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6546c568e59aa5d90800000000000000
      internalID: -7108182838404684714
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_22'
      rect:
        serializedVersion: 2
        x: 192
        y: 771
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 713a75c62aa7db840800000000000000
      internalID: 5241480379350098711
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_23'
      rect:
        serializedVersion: 2
        x: 0
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e11ffc27737979fc0800000000000000
      internalID: -3488153121969934050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_24'
      rect:
        serializedVersion: 2
        x: 32
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d31e1a8b62117c850800000000000000
      internalID: 6397100653707125053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_25'
      rect:
        serializedVersion: 2
        x: 64
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7cc47899d20eaf20800000000000000
      internalID: 3435686699319217275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_26'
      rect:
        serializedVersion: 2
        x: 96
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c720424d0fd638650800000000000000
      internalID: 6233947190329606780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_27'
      rect:
        serializedVersion: 2
        x: 128
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7de7f3ceefcb12370800000000000000
      internalID: 8296119791664463575
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_28'
      rect:
        serializedVersion: 2
        x: 160
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8e5db8bf630d8970800000000000000
      internalID: 8758660628681154190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_29'
      rect:
        serializedVersion: 2
        x: 192
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 068e9a5714ab87250800000000000000
      internalID: 5942704498625079392
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_30'
      rect:
        serializedVersion: 2
        x: 224
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4519b681231c7a3b0800000000000000
      internalID: -5501215998907543212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_31'
      rect:
        serializedVersion: 2
        x: 256
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3008d6eeb4d1953c0800000000000000
      internalID: -4370429751426449405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_32'
      rect:
        serializedVersion: 2
        x: 288
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c42a7943b7889f670800000000000000
      internalID: 8573033428421157452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_33'
      rect:
        serializedVersion: 2
        x: 320
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06b28f5ab2314ec40800000000000000
      internalID: 5540574519761578848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_34'
      rect:
        serializedVersion: 2
        x: 352
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6a75da6b5491b360800000000000000
      internalID: 7183686000987961966
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_35'
      rect:
        serializedVersion: 2
        x: 384
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab72996d7a51fa790800000000000000
      internalID: -7516765442453985350
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_36'
      rect:
        serializedVersion: 2
        x: 416
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce3a520025fe13140800000000000000
      internalID: 4697799021792961516
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_37'
      rect:
        serializedVersion: 2
        x: 448
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3b65159974d916150800000000000000
      internalID: 5864141120653579955
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_38'
      rect:
        serializedVersion: 2
        x: 480
        y: 739
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03eb011a70771ee20800000000000000
      internalID: 3378112070155288112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_39'
      rect:
        serializedVersion: 2
        x: 0
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7ebb2d2f67a8c340800000000000000
      internalID: 4884337891824352890
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_40'
      rect:
        serializedVersion: 2
        x: 32
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c60e682f0fe11770800000000000000
      internalID: 8579901613499352774
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_41'
      rect:
        serializedVersion: 2
        x: 64
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a3e841a72deef0b0800000000000000
      internalID: -5692852124484312159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_42'
      rect:
        serializedVersion: 2
        x: 96
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cbec8ba79d898570800000000000000
      internalID: 8469456256804645828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_43'
      rect:
        serializedVersion: 2
        x: 128
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ccc01701d0a071c60800000000000000
      internalID: 7788705131787259084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_44'
      rect:
        serializedVersion: 2
        x: 160
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6403a7525c83b6270800000000000000
      internalID: 8244745962220302406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_45'
      rect:
        serializedVersion: 2
        x: 192
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5eefbf8ca75163f60800000000000000
      internalID: 8013616204054527717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_46'
      rect:
        serializedVersion: 2
        x: 224
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0ee8633517d4c2d0800000000000000
      internalID: -3259243744252596725
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_47'
      rect:
        serializedVersion: 2
        x: 256
        y: 707
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f741a4131b4258b0800000000000000
      internalID: -5164983277317961741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_48'
      rect:
        serializedVersion: 2
        x: 0
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 80e8fbf686f2c8200800000000000000
      internalID: 183573810413276680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_49'
      rect:
        serializedVersion: 2
        x: 32
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2360d916ca5b7a710800000000000000
      internalID: 1704530735959705138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_50'
      rect:
        serializedVersion: 2
        x: 64
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7659ffddf11234290800000000000000
      internalID: -7907440099980241561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_51'
      rect:
        serializedVersion: 2
        x: 96
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4b2bf279f0d57d00800000000000000
      internalID: 969911064562707275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_52'
      rect:
        serializedVersion: 2
        x: 128
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f6bbedc9c55fcb70800000000000000
      internalID: 8921443712075806451
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_53'
      rect:
        serializedVersion: 2
        x: 160
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e5afcc648a073d10800000000000000
      internalID: 2105162914696504809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_54'
      rect:
        serializedVersion: 2
        x: 192
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5bed10bdf23c0840800000000000000
      internalID: 5191580535614204767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_55'
      rect:
        serializedVersion: 2
        x: 224
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0417d4189490d2f0800000000000000
      internalID: -950096140469595123
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_56'
      rect:
        serializedVersion: 2
        x: 256
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 278cd1d4ac7f9b130800000000000000
      internalID: 3583167426799192178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_57'
      rect:
        serializedVersion: 2
        x: 288
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e14e176e3c22b3990800000000000000
      internalID: -7405286937500457954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_58'
      rect:
        serializedVersion: 2
        x: 320
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f286c768219c56b00800000000000000
      internalID: 821283588467877935
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_59'
      rect:
        serializedVersion: 2
        x: 352
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4befd4d11f5976a0800000000000000
      internalID: -6451020461040997553
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_60'
      rect:
        serializedVersion: 2
        x: 384
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef6669b89321474b0800000000000000
      internalID: -5443706011219761410
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_61'
      rect:
        serializedVersion: 2
        x: 416
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e5d2039cb9147620800000000000000
      internalID: 2770867968450680294
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_62'
      rect:
        serializedVersion: 2
        x: 448
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45284521be24232b0800000000000000
      internalID: -5606345008730570156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_63'
      rect:
        serializedVersion: 2
        x: 480
        y: 675
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b74ded752187bd3e0800000000000000
      internalID: -2027895187021310853
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_64'
      rect:
        serializedVersion: 2
        x: 0
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05e2446b33f727390800000000000000
      internalID: -7822049742711148976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_65'
      rect:
        serializedVersion: 2
        x: 32
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f58aa52e875b25fa0800000000000000
      internalID: -5813384638183593889
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_66'
      rect:
        serializedVersion: 2
        x: 64
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f16d1f0b60e2a1f70800000000000000
      internalID: 9158683398484645407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_67'
      rect:
        serializedVersion: 2
        x: 96
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9eb3d1a4940fcb590800000000000000
      internalID: -7656981068869977111
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_68'
      rect:
        serializedVersion: 2
        x: 128
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f41084a14d7b7ae00800000000000000
      internalID: 1056014759243546959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_69'
      rect:
        serializedVersion: 2
        x: 160
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8640cf998f6167300800000000000000
      internalID: 249412086356771944
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_70'
      rect:
        serializedVersion: 2
        x: 192
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca2cec8783c940730800000000000000
      internalID: 3964465338351796908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_71'
      rect:
        serializedVersion: 2
        x: 224
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dad36d47ced19da30800000000000000
      internalID: 4240453425555717549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_72'
      rect:
        serializedVersion: 2
        x: 256
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47c1eda0be5cb7990800000000000000
      internalID: -7387093150480851852
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_73'
      rect:
        serializedVersion: 2
        x: 288
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf1c30a3be5c7dff0800000000000000
      internalID: -11322859963825669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_74'
      rect:
        serializedVersion: 2
        x: 320
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e87f3ead0bd037860800000000000000
      internalID: 7526374455504009102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_75'
      rect:
        serializedVersion: 2
        x: 352
        y: 643
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2fd8f6837f805990800000000000000
      internalID: -7399256461421256914
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_76'
      rect:
        serializedVersion: 2
        x: 0
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7bc57b314673c2aa0800000000000000
      internalID: -6184507285319492425
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_77'
      rect:
        serializedVersion: 2
        x: 32
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bc84df8fb031f0180800000000000000
      internalID: -9147071376023271221
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_78'
      rect:
        serializedVersion: 2
        x: 64
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60f173e2be1e28c60800000000000000
      internalID: 7819060303277530886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_79'
      rect:
        serializedVersion: 2
        x: 96
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa11b8ad97aad1040800000000000000
      internalID: 4620036233086308783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_80'
      rect:
        serializedVersion: 2
        x: 128
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ef643362e2f568a0800000000000000
      internalID: -6312372248548970523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_81'
      rect:
        serializedVersion: 2
        x: 160
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a4de68a0c31b2cd0800000000000000
      internalID: -2581948243186363227
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_82'
      rect:
        serializedVersion: 2
        x: 192
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98b480553682b4720800000000000000
      internalID: 2831401197825969033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_83'
      rect:
        serializedVersion: 2
        x: 224
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9bc72b6eca431fe40800000000000000
      internalID: 5688385721555123385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_84'
      rect:
        serializedVersion: 2
        x: 256
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 661fce8563c7b9410800000000000000
      internalID: 1484917075010711910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_85'
      rect:
        serializedVersion: 2
        x: 288
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b749c922df3663970800000000000000
      internalID: 8734278466190218363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_86'
      rect:
        serializedVersion: 2
        x: 320
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2ef38089a7880f70800000000000000
      internalID: 9153715404705889837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_87'
      rect:
        serializedVersion: 2
        x: 352
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5eabebde9ce1cd250800000000000000
      internalID: 5970681058612394725
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_88'
      rect:
        serializedVersion: 2
        x: 384
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1025748b5dae36d40800000000000000
      internalID: 5576558967255421441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_89'
      rect:
        serializedVersion: 2
        x: 416
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 607ed711bc56a5150800000000000000
      internalID: 5862109787822352134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_90'
      rect:
        serializedVersion: 2
        x: 448
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b401406e9f32b44a0800000000000000
      internalID: -6608148472023216053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_91'
      rect:
        serializedVersion: 2
        x: 480
        y: 611
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f358211a798460890800000000000000
      internalID: -7492221114004634305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_92'
      rect:
        serializedVersion: 2
        x: 0
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14fa93b3337c1d780800000000000000
      internalID: -8659921585605857471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_93'
      rect:
        serializedVersion: 2
        x: 32
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 208a6cf15e2e7ff80800000000000000
      internalID: -8072734333329823742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_94'
      rect:
        serializedVersion: 2
        x: 64
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9267b193025942850800000000000000
      internalID: 6351365340128835113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_95'
      rect:
        serializedVersion: 2
        x: 96
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b20c0ccc3be2edc0800000000000000
      internalID: -3611065304843812169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_96'
      rect:
        serializedVersion: 2
        x: 128
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1bb455cb623e53290800000000000000
      internalID: -7911167414898439247
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_97'
      rect:
        serializedVersion: 2
        x: 160
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6ba7360bccf35110800000000000000
      internalID: 1248619470624631663
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_98'
      rect:
        serializedVersion: 2
        x: 192
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac214c1fd06ea2bb0800000000000000
      internalID: -4959899092030057782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_99'
      rect:
        serializedVersion: 2
        x: 224
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c08523fce211c6940800000000000000
      internalID: 5290622554995841036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_100'
      rect:
        serializedVersion: 2
        x: 256
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a383d0c18f7ba5a90800000000000000
      internalID: -7324339567714289606
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_101'
      rect:
        serializedVersion: 2
        x: 288
        y: 579
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b256c0532662df830800000000000000
      internalID: 4106480638470415659
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_102'
      rect:
        serializedVersion: 2
        x: 0
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 816a6ad3ac3ef0c80800000000000000
      internalID: -8354208325991750120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_103'
      rect:
        serializedVersion: 2
        x: 32
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63537704037a6c0b0800000000000000
      internalID: -5708691651963701962
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_104'
      rect:
        serializedVersion: 2
        x: 64
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c5089ca9b29b62f0800000000000000
      internalID: -978527167860701757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_105'
      rect:
        serializedVersion: 2
        x: 96
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8fed61406e6004e20800000000000000
      internalID: 3332671309235019512
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_106'
      rect:
        serializedVersion: 2
        x: 128
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36550c04d33b58680800000000000000
      internalID: -8753393225063967389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_107'
      rect:
        serializedVersion: 2
        x: 160
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24edf0678fa4c1210800000000000000
      internalID: 1305000423023697474
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_108'
      rect:
        serializedVersion: 2
        x: 192
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a0da8e5033ad1f10800000000000000
      internalID: 2242127617640288418
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_109'
      rect:
        serializedVersion: 2
        x: 224
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e95ebcb9bd8bbe980800000000000000
      internalID: -8508503817635240546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_110'
      rect:
        serializedVersion: 2
        x: 256
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d432a698837e4e80800000000000000
      internalID: -8192483641660394284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_111'
      rect:
        serializedVersion: 2
        x: 288
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c588061ed240d4120800000000000000
      internalID: 2399578771559581788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_112'
      rect:
        serializedVersion: 2
        x: 320
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 034ed79b3c5c37fe0800000000000000
      internalID: -1192392031901719504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_113'
      rect:
        serializedVersion: 2
        x: 352
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50ca446a20053e3b0800000000000000
      internalID: -5484451948897457147
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_114'
      rect:
        serializedVersion: 2
        x: 384
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f64a7abf613d4f500800000000000000
      internalID: 429199960171848815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_115'
      rect:
        serializedVersion: 2
        x: 416
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cec4651efceb2b230800000000000000
      internalID: 3653192047798930668
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_116'
      rect:
        serializedVersion: 2
        x: 448
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0f8a9f8d8a7b7d00800000000000000
      internalID: 971504893046722317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_117'
      rect:
        serializedVersion: 2
        x: 480
        y: 547
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 54658dfd911560760800000000000000
      internalID: 7423710207338370629
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_118'
      rect:
        serializedVersion: 2
        x: 0
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d8cca18598330ef0800000000000000
      internalID: -143208548368856878
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_119'
      rect:
        serializedVersion: 2
        x: 32
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b2935029de7a8e50800000000000000
      internalID: 6812396857366647474
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_120'
      rect:
        serializedVersion: 2
        x: 64
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a40d64af6588d0560800000000000000
      internalID: 7281626079675469898
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_121'
      rect:
        serializedVersion: 2
        x: 96
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e24343af03a47650800000000000000
      internalID: 6229783472119169768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_122'
      rect:
        serializedVersion: 2
        x: 128
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a96185e43396f2b60800000000000000
      internalID: 7723507555042334362
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_123'
      rect:
        serializedVersion: 2
        x: 160
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d774d9d40b3e1d150800000000000000
      internalID: 5895743733560592253
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_124'
      rect:
        serializedVersion: 2
        x: 192
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74ece4df2b941aa90800000000000000
      internalID: -7304476087515427257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_125'
      rect:
        serializedVersion: 2
        x: 224
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a967a2d446663aa00800000000000000
      internalID: 766568942560507546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_126'
      rect:
        serializedVersion: 2
        x: 256
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 285dce6ec45a22f40800000000000000
      internalID: 5702301827914847618
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_127'
      rect:
        serializedVersion: 2
        x: 288
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f85beca0845b22260800000000000000
      internalID: 7071413685948691855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_128'
      rect:
        serializedVersion: 2
        x: 320
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3da7ffe8d5b35a1c0800000000000000
      internalID: -4493119780215096621
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_129'
      rect:
        serializedVersion: 2
        x: 352
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1fa6d662d5f475030800000000000000
      internalID: 3483340098289625841
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_130'
      rect:
        serializedVersion: 2
        x: 384
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08b4018382ec51660800000000000000
      internalID: 7356012238514178944
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_131'
      rect:
        serializedVersion: 2
        x: 416
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a77d9611f4fb5f70800000000000000
      internalID: 9177197982001231785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_132'
      rect:
        serializedVersion: 2
        x: 448
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32bb1cc229a9b4960800000000000000
      internalID: 7587327949842529059
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_133'
      rect:
        serializedVersion: 2
        x: 480
        y: 515
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 571dbe9f134558b00800000000000000
      internalID: 830162279942836597
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_134'
      rect:
        serializedVersion: 2
        x: 0
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28af411963c04e740800000000000000
      internalID: 5180278899884751490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_135'
      rect:
        serializedVersion: 2
        x: 32
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 53ecf30f24e5e0d10800000000000000
      internalID: 2093714518365425205
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_136'
      rect:
        serializedVersion: 2
        x: 64
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 103ade8ceb10ba4d0800000000000000
      internalID: -3122399997724876031
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_137'
      rect:
        serializedVersion: 2
        x: 96
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19b235a858a24a480800000000000000
      internalID: -8888933011482793071
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_138'
      rect:
        serializedVersion: 2
        x: 128
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a633244c9be11ddf0800000000000000
      internalID: -157310728770800790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_139'
      rect:
        serializedVersion: 2
        x: 160
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88f9b0f6f85cd9560800000000000000
      internalID: 7322225789008322440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_140'
      rect:
        serializedVersion: 2
        x: 192
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09e82d303bb6eed50800000000000000
      internalID: 6768465706591882896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_141'
      rect:
        serializedVersion: 2
        x: 224
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c8ce17d5421da340800000000000000
      internalID: 4876574062683277506
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_142'
      rect:
        serializedVersion: 2
        x: 256
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc5642a00506372e0800000000000000
      internalID: -2129252301932304945
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_143'
      rect:
        serializedVersion: 2
        x: 288
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0e8a49141fe72fb0800000000000000
      internalID: -4672503218770899442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_144'
      rect:
        serializedVersion: 2
        x: 320
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d47beb0e5b49ef70800000000000000
      internalID: 9216980979689419991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_145'
      rect:
        serializedVersion: 2
        x: 352
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c2c1b585fd386070800000000000000
      internalID: 8099792054545138375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_146'
      rect:
        serializedVersion: 2
        x: 384
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0e895e6953b964a0800000000000000
      internalID: -6599546582249927157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_147'
      rect:
        serializedVersion: 2
        x: 416
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30b745db3d9c54e30800000000000000
      internalID: 4487214514996280067
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_148'
      rect:
        serializedVersion: 2
        x: 448
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a6b9c02ce4abbce0800000000000000
      internalID: -1388334726044338524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_149'
      rect:
        serializedVersion: 2
        x: 480
        y: 483
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6aa847b42766186e0800000000000000
      internalID: -1837074531912217946
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_150'
      rect:
        serializedVersion: 2
        x: 0
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ec935bd45a727130800000000000000
      internalID: 3563044760079015136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_151'
      rect:
        serializedVersion: 2
        x: 32
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77065668ff2ffac50800000000000000
      internalID: 6678823951699107959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_152'
      rect:
        serializedVersion: 2
        x: 64
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d808833c8a142ad40800000000000000
      internalID: 5594105880233148557
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_153'
      rect:
        serializedVersion: 2
        x: 96
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df0adc2636d29d770800000000000000
      internalID: 8635983665342292221
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_154'
      rect:
        serializedVersion: 2
        x: 128
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 39d764f840d96ef60800000000000000
      internalID: 8063304825762643347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_155'
      rect:
        serializedVersion: 2
        x: 160
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac1d214b268977560800000000000000
      internalID: 7311480069754638794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_156'
      rect:
        serializedVersion: 2
        x: 192
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42ca3bbb0488a1d60800000000000000
      internalID: 7861745911137020964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_157'
      rect:
        serializedVersion: 2
        x: 224
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b89edddd1b8a5b80800000000000000
      internalID: -8405252794140944200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_158'
      rect:
        serializedVersion: 2
        x: 256
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1cdecbc858ced4c40800000000000000
      internalID: 5498310778425241025
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_159'
      rect:
        serializedVersion: 2
        x: 288
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fab1f31605be4920800000000000000
      internalID: 2976515441447516915
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_160'
      rect:
        serializedVersion: 2
        x: 320
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9117decd39a354a20800000000000000
      internalID: 3045905129727160601
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_161'
      rect:
        serializedVersion: 2
        x: 352
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 346743ea03607dc50800000000000000
      internalID: 6689822577633031747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_162'
      rect:
        serializedVersion: 2
        x: 384
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b56e791e061e374a0800000000000000
      internalID: -6596681222949706149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_163'
      rect:
        serializedVersion: 2
        x: 416
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0577ddf4a5ff66c90800000000000000
      internalID: -7176768192816253104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_164'
      rect:
        serializedVersion: 2
        x: 448
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 047d3f29104192ec0800000000000000
      internalID: -3591317230858283200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_165'
      rect:
        serializedVersion: 2
        x: 480
        y: 451
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 739af6dd82f3e1b10800000000000000
      internalID: 1954068733071698231
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_166'
      rect:
        serializedVersion: 2
        x: 0
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f56845a6f23d4b80800000000000000
      internalID: -8409008894314519048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_167'
      rect:
        serializedVersion: 2
        x: 32
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 11e8303bc94dec060800000000000000
      internalID: 6975746642326818321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_168'
      rect:
        serializedVersion: 2
        x: 64
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32e07b672fe9f9280800000000000000
      internalID: -9034327563270943197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_169'
      rect:
        serializedVersion: 2
        x: 96
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04210a8ad510b5230800000000000000
      internalID: 3628495426549715520
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_170'
      rect:
        serializedVersion: 2
        x: 128
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0e0b715590a41edc0800000000000000
      internalID: -3611524022231650080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_171'
      rect:
        serializedVersion: 2
        x: 160
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19e73986998b734d0800000000000000
      internalID: -3154850044925739375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_172'
      rect:
        serializedVersion: 2
        x: 192
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d506db4a77af6dd0800000000000000
      internalID: -2490587925212428839
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_173'
      rect:
        serializedVersion: 2
        x: 224
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8b2ccfa4795be5d0800000000000000
      internalID: -3032231566403425396
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_174'
      rect:
        serializedVersion: 2
        x: 256
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ad7b71089d075a00800000000000000
      internalID: 745079209864166825
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_175'
      rect:
        serializedVersion: 2
        x: 288
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1a4afa04236a4080800000000000000
      internalID: -9202433882124039654
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_176'
      rect:
        serializedVersion: 2
        x: 320
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f2958d1d8c1a5a8b0800000000000000
      internalID: -5141525515687405265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_177'
      rect:
        serializedVersion: 2
        x: 352
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 461b436ad8e1207a0800000000000000
      internalID: -6412529325694471836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_178'
      rect:
        serializedVersion: 2
        x: 384
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c97041e716aa9b5d0800000000000000
      internalID: -3046216337235638372
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_179'
      rect:
        serializedVersion: 2
        x: 416
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b691248466b2f8780800000000000000
      internalID: -8678670238620706453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_180'
      rect:
        serializedVersion: 2
        x: 448
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84b27a5ca9d4bf380800000000000000
      internalID: -8936463708450247864
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_181'
      rect:
        serializedVersion: 2
        x: 480
        y: 419
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34b34d959eebfd470800000000000000
      internalID: 8421659737649855299
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_182'
      rect:
        serializedVersion: 2
        x: 0
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5591740a3e8996070800000000000000
      internalID: 8100173508216559957
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_183'
      rect:
        serializedVersion: 2
        x: 32
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f760cd3c39b05fcb0800000000000000
      internalID: -4830942296010914177
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_184'
      rect:
        serializedVersion: 2
        x: 64
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f684b5be02cf2ab20800000000000000
      internalID: 3144352708175808623
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_185'
      rect:
        serializedVersion: 2
        x: 96
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af8fe2c8a98fd9d70800000000000000
      internalID: 9051664168745105658
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_186'
      rect:
        serializedVersion: 2
        x: 128
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 928a721031b7154c0800000000000000
      internalID: -4300520847608141783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_187'
      rect:
        serializedVersion: 2
        x: 160
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b00c6ef47c26c1fb0800000000000000
      internalID: -4675753704938881013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_188'
      rect:
        serializedVersion: 2
        x: 192
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a9287220d1490580800000000000000
      internalID: -8860478429687436892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_189'
      rect:
        serializedVersion: 2
        x: 224
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b3fe280066bb70670800000000000000
      internalID: 8504972468082110267
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_190'
      rect:
        serializedVersion: 2
        x: 256
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b1824d103f406c80800000000000000
      internalID: -8331572242567495242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_191'
      rect:
        serializedVersion: 2
        x: 288
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ecf08892ba0d91c0800000000000000
      internalID: -4495425090866840351
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_192'
      rect:
        serializedVersion: 2
        x: 320
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c92dafc2220258c0800000000000000
      internalID: -4012141969497314876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_193'
      rect:
        serializedVersion: 2
        x: 352
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f414e0e01b386350800000000000000
      internalID: 6010118646404486385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_194'
      rect:
        serializedVersion: 2
        x: 384
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8045e8472206f5b0800000000000000
      internalID: -5335074340823809906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_195'
      rect:
        serializedVersion: 2
        x: 416
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65582dcbeb8163ca0800000000000000
      internalID: -6037611042952805034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_196'
      rect:
        serializedVersion: 2
        x: 448
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d846112e3139034b0800000000000000
      internalID: -5462704634393959283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_197'
      rect:
        serializedVersion: 2
        x: 480
        y: 387
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: abe32ec6470ab5460800000000000000
      internalID: 7231550048576880314
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_198'
      rect:
        serializedVersion: 2
        x: 0
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16076b23537875b40800000000000000
      internalID: 5428956538372255841
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_199'
      rect:
        serializedVersion: 2
        x: 32
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ef271ecde124a740800000000000000
      internalID: 5162288378122022885
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_200'
      rect:
        serializedVersion: 2
        x: 64
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40075c70f3d728160800000000000000
      internalID: 7026316078318186500
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_201'
      rect:
        serializedVersion: 2
        x: 96
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 340eb79e49fc31310800000000000000
      internalID: 1374670549757452355
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_202'
      rect:
        serializedVersion: 2
        x: 128
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c1dec0e8b6ffbcf0800000000000000
      internalID: -234197381693910589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_203'
      rect:
        serializedVersion: 2
        x: 160
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8943a5ec6431b1b80800000000000000
      internalID: -8423117483235724136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_204'
      rect:
        serializedVersion: 2
        x: 192
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69ce82b16ea45ef00800000000000000
      internalID: 1145404032394259606
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_205'
      rect:
        serializedVersion: 2
        x: 224
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42c5ef3c1dde22010800000000000000
      internalID: 1162753139007183908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_206'
      rect:
        serializedVersion: 2
        x: 256
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27e49643414003bb0800000000000000
      internalID: -4958458704909742478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_207'
      rect:
        serializedVersion: 2
        x: 288
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84870fc62cf297600800000000000000
      internalID: 466456548507416648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_208'
      rect:
        serializedVersion: 2
        x: 320
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2b8f86144496c380800000000000000
      internalID: -8951304189195089106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_209'
      rect:
        serializedVersion: 2
        x: 352
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d5877797b0ce78e60800000000000000
      internalID: 7964594000054679645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_210'
      rect:
        serializedVersion: 2
        x: 384
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f16454f6c1f098970800000000000000
      internalID: 8757547565198951967
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_211'
      rect:
        serializedVersion: 2
        x: 416
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06383cf039c2ae5f0800000000000000
      internalID: -726719379730627744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_212'
      rect:
        serializedVersion: 2
        x: 448
        y: 355
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4e6a4dfd0ef00390800000000000000
      internalID: -7853998414096601524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_213'
      rect:
        serializedVersion: 2
        x: 0
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5be1149e8b44f160800000000000000
      internalID: 7058349591738051420
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_214'
      rect:
        serializedVersion: 2
        x: 32
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60017c9c74316b440800000000000000
      internalID: 4951166039389310982
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_215'
      rect:
        serializedVersion: 2
        x: 64
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 224ba9fac547e43b0800000000000000
      internalID: -5526351751305055198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_216'
      rect:
        serializedVersion: 2
        x: 96
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c4efba70a6dbe4e0800000000000000
      internalID: -1951230028792470334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_217'
      rect:
        serializedVersion: 2
        x: 128
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6ca7cdf3dce824a0800000000000000
      internalID: -6617779257180967827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_218'
      rect:
        serializedVersion: 2
        x: 160
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 674b9a04b494c5d80800000000000000
      internalID: -8260647028948093834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_219'
      rect:
        serializedVersion: 2
        x: 192
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 913f1247be691f590800000000000000
      internalID: -7642161154661158119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_220'
      rect:
        serializedVersion: 2
        x: 224
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2620028ccefa096b0800000000000000
      internalID: -5291536130655649182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_221'
      rect:
        serializedVersion: 2
        x: 256
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0830f672eecc00c00800000000000000
      internalID: 864916451691004800
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_222'
      rect:
        serializedVersion: 2
        x: 288
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3403439357c24440800000000000000
      internalID: 4918712904808858685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_223'
      rect:
        serializedVersion: 2
        x: 320
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b158698ef8b1d3fc0800000000000000
      internalID: -3513621829382666981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_224'
      rect:
        serializedVersion: 2
        x: 352
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14337c7de7f5ec480800000000000000
      internalID: -8877052817109994687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_225'
      rect:
        serializedVersion: 2
        x: 384
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5985743174d3707b0800000000000000
      internalID: -5258166664456349547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_226'
      rect:
        serializedVersion: 2
        x: 416
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c345523c66bfcbb0800000000000000
      internalID: -4913508092620749888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_227'
      rect:
        serializedVersion: 2
        x: 448
        y: 323
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2b42927fd1dc9420800000000000000
      internalID: 2638214239362370348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_228'
      rect:
        serializedVersion: 2
        x: 0
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c27b427d5ecb12a0800000000000000
      internalID: -6765587114449210688
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_229'
      rect:
        serializedVersion: 2
        x: 32
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8668b786c82be3550800000000000000
      internalID: 6142543257898026600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_230'
      rect:
        serializedVersion: 2
        x: 64
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77abe7df41dbda1e0800000000000000
      internalID: -2184882346354886025
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_231'
      rect:
        serializedVersion: 2
        x: 96
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c1aeac98353b3eb60800000000000000
      internalID: 7774254553147042332
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_232'
      rect:
        serializedVersion: 2
        x: 128
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6af7a6376244b58f0800000000000000
      internalID: -550771597486948442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_233'
      rect:
        serializedVersion: 2
        x: 160
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e835119c08cbfa40800000000000000
      internalID: 5403132134260291811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_234'
      rect:
        serializedVersion: 2
        x: 192
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43b99bcdf68f75040800000000000000
      internalID: 4636447500729424692
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_235'
      rect:
        serializedVersion: 2
        x: 224
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48f7cbfe18e60b200800000000000000
      internalID: 193776288328875908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_236'
      rect:
        serializedVersion: 2
        x: 256
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4782a4eba85c1c7b0800000000000000
      internalID: -5205662494574892940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_237'
      rect:
        serializedVersion: 2
        x: 288
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5df51210dd313f30800000000000000
      internalID: 4553488662114205020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_238'
      rect:
        serializedVersion: 2
        x: 320
        y: 291
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0092095c60f6cb40800000000000000
      internalID: 5460315946373779467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_239'
      rect:
        serializedVersion: 2
        x: 0
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f595c41d22db09190800000000000000
      internalID: -7957652584325686945
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_240'
      rect:
        serializedVersion: 2
        x: 32
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 847f4db586339f330800000000000000
      internalID: 3745081088445577032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_241'
      rect:
        serializedVersion: 2
        x: 64
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28ecf2c8963e74f20800000000000000
      internalID: 3406941685592084098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_242'
      rect:
        serializedVersion: 2
        x: 96
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d78ac028198b9a80800000000000000
      internalID: -8459016738362980394
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_243'
      rect:
        serializedVersion: 2
        x: 128
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8f0f139eb7c87460800000000000000
      internalID: 7239756022324072331
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_244'
      rect:
        serializedVersion: 2
        x: 160
        y: 259
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96d27581d4beb5660800000000000000
      internalID: 7375747531102694761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_245'
      rect:
        serializedVersion: 2
        x: 0
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb99c0f9278477540800000000000000
      internalID: 5005549167977470398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_246'
      rect:
        serializedVersion: 2
        x: 32
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a162542df5059000800000000000000
      internalID: 41946356324131235
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_247'
      rect:
        serializedVersion: 2
        x: 64
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6fd3cbd09cfcc4fe0800000000000000
      internalID: -1203358537988948490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_248'
      rect:
        serializedVersion: 2
        x: 96
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76fe43801fe174240800000000000000
      internalID: 4775819950423338855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_249'
      rect:
        serializedVersion: 2
        x: 128
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb581d25ee9a5cee0800000000000000
      internalID: -1241399281213930050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_250'
      rect:
        serializedVersion: 2
        x: 160
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ddba7b490475d56e0800000000000000
      internalID: -1847224337267446819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_251'
      rect:
        serializedVersion: 2
        x: 192
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e38da89c163ebcd30800000000000000
      internalID: 4452902665718519870
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_252'
      rect:
        serializedVersion: 2
        x: 224
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34b46a0ea35ed8d10800000000000000
      internalID: 2129610239855971139
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_253'
      rect:
        serializedVersion: 2
        x: 256
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf1ec38d21e95dce0800000000000000
      internalID: -1381023906944720389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_254'
      rect:
        serializedVersion: 2
        x: 288
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d33f97a569506550800000000000000
      internalID: 6152015384126895060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_255'
      rect:
        serializedVersion: 2
        x: 320
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5367fab32052a430800000000000000
      internalID: 3792681950590231387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_256'
      rect:
        serializedVersion: 2
        x: 352
        y: 227
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c256c64f9fab152a0800000000000000
      internalID: -6750408783741295316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_257'
      rect:
        serializedVersion: 2
        x: 0
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb4b4b9fe84f5e400800000000000000
      internalID: 352957040730354879
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_258'
      rect:
        serializedVersion: 2
        x: 32
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f53c7bdea4eebec0800000000000000
      internalID: -3549148016663579148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_259'
      rect:
        serializedVersion: 2
        x: 64
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8fee6b37c05a53b0800000000000000
      internalID: -5523013176390652018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_260'
      rect:
        serializedVersion: 2
        x: 96
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf6c2a06ba86ca910800000000000000
      internalID: 1849968632212408060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_261'
      rect:
        serializedVersion: 2
        x: 128
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 53527e29bd9ccc010800000000000000
      internalID: 1210564344755463477
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_262'
      rect:
        serializedVersion: 2
        x: 160
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e131425880e90f080800000000000000
      internalID: -9155644283013557474
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_263'
      rect:
        serializedVersion: 2
        x: 192
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa53fe95244c3efc0800000000000000
      internalID: -3466711498889415249
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_264'
      rect:
        serializedVersion: 2
        x: 224
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec8d6ab810c5edb40800000000000000
      internalID: 5466908159382051022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_265'
      rect:
        serializedVersion: 2
        x: 256
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0d43aa6598f4dca0800000000000000
      internalID: -5992891883500385013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_266'
      rect:
        serializedVersion: 2
        x: 288
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 000a524ae3a840980800000000000000
      internalID: -8573575788960047104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_267'
      rect:
        serializedVersion: 2
        x: 320
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f546d16dc9871780800000000000000
      internalID: -8712343438922201611
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_268'
      rect:
        serializedVersion: 2
        x: 352
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a62d4bb145cbfe900800000000000000
      internalID: 715997935203308138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_269'
      rect:
        serializedVersion: 2
        x: 384
        y: 195
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c1ce752b99b5239d0800000000000000
      internalID: -2796071702963426276
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_270'
      rect:
        serializedVersion: 2
        x: 0
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 680e71d4a25cc0da0800000000000000
      internalID: -5977185819954782074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_271'
      rect:
        serializedVersion: 2
        x: 32
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b23c6f1e40eb79e60800000000000000
      internalID: 7969046993835574059
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_272'
      rect:
        serializedVersion: 2
        x: 64
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aaf9fc6623d94f7b0800000000000000
      internalID: -5191351630652530774
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_273'
      rect:
        serializedVersion: 2
        x: 96
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e2f425be117ae310800000000000000
      internalID: 1435083807972979427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_274'
      rect:
        serializedVersion: 2
        x: 128
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5951198d5f75833a0800000000000000
      internalID: -6685496933419510379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_275'
      rect:
        serializedVersion: 2
        x: 160
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 704730b69ac950840800000000000000
      internalID: 5189726397073159175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_276'
      rect:
        serializedVersion: 2
        x: 192
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 471a6ffbd22edc830800000000000000
      internalID: 4093176322471666036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_277'
      rect:
        serializedVersion: 2
        x: 224
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f97444128ed50a40800000000000000
      internalID: 5333913983957367281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_278'
      rect:
        serializedVersion: 2
        x: 256
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37226edef7da94550800000000000000
      internalID: 6145633931487486579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_279'
      rect:
        serializedVersion: 2
        x: 288
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00fd0884809f701d0800000000000000
      internalID: -3384462780974178560
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_280'
      rect:
        serializedVersion: 2
        x: 320
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 573b449dd61b987b0800000000000000
      internalID: -5221447207604669579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_281'
      rect:
        serializedVersion: 2
        x: 352
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a5abbfa4f344e71f0800000000000000
      internalID: -1045323024871736742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_282'
      rect:
        serializedVersion: 2
        x: 384
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63b6cccbe5355b190800000000000000
      internalID: -7947354301065827530
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#2 - Transparent Icons & Drop Shadow_283'
      rect:
        serializedVersion: 2
        x: 416
        y: 163
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d439a28be88dd6c70800000000000000
      internalID: 8966060540651279181
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 9ce66e2a054960941ac680c62c33ec60
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      '#2 - Transparent Icons & Drop Shadow_0': -7568524073924095226
      '#2 - Transparent Icons & Drop Shadow_1': 2584609656251380707
      '#2 - Transparent Icons & Drop Shadow_10': -8216494571090356096
      '#2 - Transparent Icons & Drop Shadow_100': -7324339567714289606
      '#2 - Transparent Icons & Drop Shadow_101': 4106480638470415659
      '#2 - Transparent Icons & Drop Shadow_102': -8354208325991750120
      '#2 - Transparent Icons & Drop Shadow_103': -5708691651963701962
      '#2 - Transparent Icons & Drop Shadow_104': -978527167860701757
      '#2 - Transparent Icons & Drop Shadow_105': 3332671309235019512
      '#2 - Transparent Icons & Drop Shadow_106': -8753393225063967389
      '#2 - Transparent Icons & Drop Shadow_107': 1305000423023697474
      '#2 - Transparent Icons & Drop Shadow_108': 2242127617640288418
      '#2 - Transparent Icons & Drop Shadow_109': -8508503817635240546
      '#2 - Transparent Icons & Drop Shadow_11': 4558633621754773204
      '#2 - Transparent Icons & Drop Shadow_110': -8192483641660394284
      '#2 - Transparent Icons & Drop Shadow_111': 2399578771559581788
      '#2 - Transparent Icons & Drop Shadow_112': -1192392031901719504
      '#2 - Transparent Icons & Drop Shadow_113': -5484451948897457147
      '#2 - Transparent Icons & Drop Shadow_114': 429199960171848815
      '#2 - Transparent Icons & Drop Shadow_115': 3653192047798930668
      '#2 - Transparent Icons & Drop Shadow_116': 971504893046722317
      '#2 - Transparent Icons & Drop Shadow_117': 7423710207338370629
      '#2 - Transparent Icons & Drop Shadow_118': -143208548368856878
      '#2 - Transparent Icons & Drop Shadow_119': 6812396857366647474
      '#2 - Transparent Icons & Drop Shadow_12': 1422253230955754594
      '#2 - Transparent Icons & Drop Shadow_120': 7281626079675469898
      '#2 - Transparent Icons & Drop Shadow_121': 6229783472119169768
      '#2 - Transparent Icons & Drop Shadow_122': 7723507555042334362
      '#2 - Transparent Icons & Drop Shadow_123': 5895743733560592253
      '#2 - Transparent Icons & Drop Shadow_124': -7304476087515427257
      '#2 - Transparent Icons & Drop Shadow_125': 766568942560507546
      '#2 - Transparent Icons & Drop Shadow_126': 5702301827914847618
      '#2 - Transparent Icons & Drop Shadow_127': 7071413685948691855
      '#2 - Transparent Icons & Drop Shadow_128': -4493119780215096621
      '#2 - Transparent Icons & Drop Shadow_129': 3483340098289625841
      '#2 - Transparent Icons & Drop Shadow_13': -3390942904790010266
      '#2 - Transparent Icons & Drop Shadow_130': 7356012238514178944
      '#2 - Transparent Icons & Drop Shadow_131': 9177197982001231785
      '#2 - Transparent Icons & Drop Shadow_132': 7587327949842529059
      '#2 - Transparent Icons & Drop Shadow_133': 830162279942836597
      '#2 - Transparent Icons & Drop Shadow_134': 5180278899884751490
      '#2 - Transparent Icons & Drop Shadow_135': 2093714518365425205
      '#2 - Transparent Icons & Drop Shadow_136': -3122399997724876031
      '#2 - Transparent Icons & Drop Shadow_137': -8888933011482793071
      '#2 - Transparent Icons & Drop Shadow_138': -157310728770800790
      '#2 - Transparent Icons & Drop Shadow_139': 7322225789008322440
      '#2 - Transparent Icons & Drop Shadow_14': 6602636038343897003
      '#2 - Transparent Icons & Drop Shadow_140': 6768465706591882896
      '#2 - Transparent Icons & Drop Shadow_141': 4876574062683277506
      '#2 - Transparent Icons & Drop Shadow_142': -2129252301932304945
      '#2 - Transparent Icons & Drop Shadow_143': -4672503218770899442
      '#2 - Transparent Icons & Drop Shadow_144': 9216980979689419991
      '#2 - Transparent Icons & Drop Shadow_145': 8099792054545138375
      '#2 - Transparent Icons & Drop Shadow_146': -6599546582249927157
      '#2 - Transparent Icons & Drop Shadow_147': 4487214514996280067
      '#2 - Transparent Icons & Drop Shadow_148': -1388334726044338524
      '#2 - Transparent Icons & Drop Shadow_149': -1837074531912217946
      '#2 - Transparent Icons & Drop Shadow_15': 3806784070233699775
      '#2 - Transparent Icons & Drop Shadow_150': 3563044760079015136
      '#2 - Transparent Icons & Drop Shadow_151': 6678823951699107959
      '#2 - Transparent Icons & Drop Shadow_152': 5594105880233148557
      '#2 - Transparent Icons & Drop Shadow_153': 8635983665342292221
      '#2 - Transparent Icons & Drop Shadow_154': 8063304825762643347
      '#2 - Transparent Icons & Drop Shadow_155': 7311480069754638794
      '#2 - Transparent Icons & Drop Shadow_156': 7861745911137020964
      '#2 - Transparent Icons & Drop Shadow_157': -8405252794140944200
      '#2 - Transparent Icons & Drop Shadow_158': 5498310778425241025
      '#2 - Transparent Icons & Drop Shadow_159': 2976515441447516915
      '#2 - Transparent Icons & Drop Shadow_16': -413687423355243995
      '#2 - Transparent Icons & Drop Shadow_160': 3045905129727160601
      '#2 - Transparent Icons & Drop Shadow_161': 6689822577633031747
      '#2 - Transparent Icons & Drop Shadow_162': -6596681222949706149
      '#2 - Transparent Icons & Drop Shadow_163': -7176768192816253104
      '#2 - Transparent Icons & Drop Shadow_164': -3591317230858283200
      '#2 - Transparent Icons & Drop Shadow_165': 1954068733071698231
      '#2 - Transparent Icons & Drop Shadow_166': -8409008894314519048
      '#2 - Transparent Icons & Drop Shadow_167': 6975746642326818321
      '#2 - Transparent Icons & Drop Shadow_168': -9034327563270943197
      '#2 - Transparent Icons & Drop Shadow_169': 3628495426549715520
      '#2 - Transparent Icons & Drop Shadow_17': -1245755177353750832
      '#2 - Transparent Icons & Drop Shadow_170': -3611524022231650080
      '#2 - Transparent Icons & Drop Shadow_171': -3154850044925739375
      '#2 - Transparent Icons & Drop Shadow_172': -2490587925212428839
      '#2 - Transparent Icons & Drop Shadow_173': -3032231566403425396
      '#2 - Transparent Icons & Drop Shadow_174': 745079209864166825
      '#2 - Transparent Icons & Drop Shadow_175': -9202433882124039654
      '#2 - Transparent Icons & Drop Shadow_176': -5141525515687405265
      '#2 - Transparent Icons & Drop Shadow_177': -6412529325694471836
      '#2 - Transparent Icons & Drop Shadow_178': -3046216337235638372
      '#2 - Transparent Icons & Drop Shadow_179': -8678670238620706453
      '#2 - Transparent Icons & Drop Shadow_18': -3923099718294011341
      '#2 - Transparent Icons & Drop Shadow_180': -8936463708450247864
      '#2 - Transparent Icons & Drop Shadow_181': 8421659737649855299
      '#2 - Transparent Icons & Drop Shadow_182': 8100173508216559957
      '#2 - Transparent Icons & Drop Shadow_183': -4830942296010914177
      '#2 - Transparent Icons & Drop Shadow_184': 3144352708175808623
      '#2 - Transparent Icons & Drop Shadow_185': 9051664168745105658
      '#2 - Transparent Icons & Drop Shadow_186': -4300520847608141783
      '#2 - Transparent Icons & Drop Shadow_187': -4675753704938881013
      '#2 - Transparent Icons & Drop Shadow_188': -8860478429687436892
      '#2 - Transparent Icons & Drop Shadow_189': 8504972468082110267
      '#2 - Transparent Icons & Drop Shadow_19': 1599180710894039856
      '#2 - Transparent Icons & Drop Shadow_190': -8331572242567495242
      '#2 - Transparent Icons & Drop Shadow_191': -4495425090866840351
      '#2 - Transparent Icons & Drop Shadow_192': -4012141969497314876
      '#2 - Transparent Icons & Drop Shadow_193': 6010118646404486385
      '#2 - Transparent Icons & Drop Shadow_194': -5335074340823809906
      '#2 - Transparent Icons & Drop Shadow_195': -6037611042952805034
      '#2 - Transparent Icons & Drop Shadow_196': -5462704634393959283
      '#2 - Transparent Icons & Drop Shadow_197': 7231550048576880314
      '#2 - Transparent Icons & Drop Shadow_198': 5428956538372255841
      '#2 - Transparent Icons & Drop Shadow_199': 5162288378122022885
      '#2 - Transparent Icons & Drop Shadow_2': -7483596328251532873
      '#2 - Transparent Icons & Drop Shadow_20': -772803406078260477
      '#2 - Transparent Icons & Drop Shadow_200': 7026316078318186500
      '#2 - Transparent Icons & Drop Shadow_201': 1374670549757452355
      '#2 - Transparent Icons & Drop Shadow_202': -234197381693910589
      '#2 - Transparent Icons & Drop Shadow_203': -8423117483235724136
      '#2 - Transparent Icons & Drop Shadow_204': 1145404032394259606
      '#2 - Transparent Icons & Drop Shadow_205': 1162753139007183908
      '#2 - Transparent Icons & Drop Shadow_206': -4958458704909742478
      '#2 - Transparent Icons & Drop Shadow_207': 466456548507416648
      '#2 - Transparent Icons & Drop Shadow_208': -8951304189195089106
      '#2 - Transparent Icons & Drop Shadow_209': 7964594000054679645
      '#2 - Transparent Icons & Drop Shadow_21': -7108182838404684714
      '#2 - Transparent Icons & Drop Shadow_210': 8757547565198951967
      '#2 - Transparent Icons & Drop Shadow_211': -726719379730627744
      '#2 - Transparent Icons & Drop Shadow_212': -7853998414096601524
      '#2 - Transparent Icons & Drop Shadow_213': 7058349591738051420
      '#2 - Transparent Icons & Drop Shadow_214': 4951166039389310982
      '#2 - Transparent Icons & Drop Shadow_215': -5526351751305055198
      '#2 - Transparent Icons & Drop Shadow_216': -1951230028792470334
      '#2 - Transparent Icons & Drop Shadow_217': -6617779257180967827
      '#2 - Transparent Icons & Drop Shadow_218': -8260647028948093834
      '#2 - Transparent Icons & Drop Shadow_219': -7642161154661158119
      '#2 - Transparent Icons & Drop Shadow_22': 5241480379350098711
      '#2 - Transparent Icons & Drop Shadow_220': -5291536130655649182
      '#2 - Transparent Icons & Drop Shadow_221': 864916451691004800
      '#2 - Transparent Icons & Drop Shadow_222': 4918712904808858685
      '#2 - Transparent Icons & Drop Shadow_223': -3513621829382666981
      '#2 - Transparent Icons & Drop Shadow_224': -8877052817109994687
      '#2 - Transparent Icons & Drop Shadow_225': -5258166664456349547
      '#2 - Transparent Icons & Drop Shadow_226': -4913508092620749888
      '#2 - Transparent Icons & Drop Shadow_227': 2638214239362370348
      '#2 - Transparent Icons & Drop Shadow_228': -6765587114449210688
      '#2 - Transparent Icons & Drop Shadow_229': 6142543257898026600
      '#2 - Transparent Icons & Drop Shadow_23': -3488153121969934050
      '#2 - Transparent Icons & Drop Shadow_230': -2184882346354886025
      '#2 - Transparent Icons & Drop Shadow_231': 7774254553147042332
      '#2 - Transparent Icons & Drop Shadow_232': -550771597486948442
      '#2 - Transparent Icons & Drop Shadow_233': 5403132134260291811
      '#2 - Transparent Icons & Drop Shadow_234': 4636447500729424692
      '#2 - Transparent Icons & Drop Shadow_235': 193776288328875908
      '#2 - Transparent Icons & Drop Shadow_236': -5205662494574892940
      '#2 - Transparent Icons & Drop Shadow_237': 4553488662114205020
      '#2 - Transparent Icons & Drop Shadow_238': 5460315946373779467
      '#2 - Transparent Icons & Drop Shadow_239': -7957652584325686945
      '#2 - Transparent Icons & Drop Shadow_24': 6397100653707125053
      '#2 - Transparent Icons & Drop Shadow_240': 3745081088445577032
      '#2 - Transparent Icons & Drop Shadow_241': 3406941685592084098
      '#2 - Transparent Icons & Drop Shadow_242': -8459016738362980394
      '#2 - Transparent Icons & Drop Shadow_243': 7239756022324072331
      '#2 - Transparent Icons & Drop Shadow_244': 7375747531102694761
      '#2 - Transparent Icons & Drop Shadow_245': 5005549167977470398
      '#2 - Transparent Icons & Drop Shadow_246': 41946356324131235
      '#2 - Transparent Icons & Drop Shadow_247': -1203358537988948490
      '#2 - Transparent Icons & Drop Shadow_248': 4775819950423338855
      '#2 - Transparent Icons & Drop Shadow_249': -1241399281213930050
      '#2 - Transparent Icons & Drop Shadow_25': 3435686699319217275
      '#2 - Transparent Icons & Drop Shadow_250': -1847224337267446819
      '#2 - Transparent Icons & Drop Shadow_251': 4452902665718519870
      '#2 - Transparent Icons & Drop Shadow_252': 2129610239855971139
      '#2 - Transparent Icons & Drop Shadow_253': -1381023906944720389
      '#2 - Transparent Icons & Drop Shadow_254': 6152015384126895060
      '#2 - Transparent Icons & Drop Shadow_255': 3792681950590231387
      '#2 - Transparent Icons & Drop Shadow_256': -6750408783741295316
      '#2 - Transparent Icons & Drop Shadow_257': 352957040730354879
      '#2 - Transparent Icons & Drop Shadow_258': -3549148016663579148
      '#2 - Transparent Icons & Drop Shadow_259': -5523013176390652018
      '#2 - Transparent Icons & Drop Shadow_26': 6233947190329606780
      '#2 - Transparent Icons & Drop Shadow_260': 1849968632212408060
      '#2 - Transparent Icons & Drop Shadow_261': 1210564344755463477
      '#2 - Transparent Icons & Drop Shadow_262': -9155644283013557474
      '#2 - Transparent Icons & Drop Shadow_263': -3466711498889415249
      '#2 - Transparent Icons & Drop Shadow_264': 5466908159382051022
      '#2 - Transparent Icons & Drop Shadow_265': -5992891883500385013
      '#2 - Transparent Icons & Drop Shadow_266': -8573575788960047104
      '#2 - Transparent Icons & Drop Shadow_267': -8712343438922201611
      '#2 - Transparent Icons & Drop Shadow_268': 715997935203308138
      '#2 - Transparent Icons & Drop Shadow_269': -2796071702963426276
      '#2 - Transparent Icons & Drop Shadow_27': 8296119791664463575
      '#2 - Transparent Icons & Drop Shadow_270': -5977185819954782074
      '#2 - Transparent Icons & Drop Shadow_271': 7969046993835574059
      '#2 - Transparent Icons & Drop Shadow_272': -5191351630652530774
      '#2 - Transparent Icons & Drop Shadow_273': 1435083807972979427
      '#2 - Transparent Icons & Drop Shadow_274': -6685496933419510379
      '#2 - Transparent Icons & Drop Shadow_275': 5189726397073159175
      '#2 - Transparent Icons & Drop Shadow_276': 4093176322471666036
      '#2 - Transparent Icons & Drop Shadow_277': 5333913983957367281
      '#2 - Transparent Icons & Drop Shadow_278': 6145633931487486579
      '#2 - Transparent Icons & Drop Shadow_279': -3384462780974178560
      '#2 - Transparent Icons & Drop Shadow_28': 8758660628681154190
      '#2 - Transparent Icons & Drop Shadow_280': -5221447207604669579
      '#2 - Transparent Icons & Drop Shadow_281': -1045323024871736742
      '#2 - Transparent Icons & Drop Shadow_282': -7947354301065827530
      '#2 - Transparent Icons & Drop Shadow_283': 8966060540651279181
      '#2 - Transparent Icons & Drop Shadow_29': 5942704498625079392
      '#2 - Transparent Icons & Drop Shadow_3': 7539286595509921347
      '#2 - Transparent Icons & Drop Shadow_30': -5501215998907543212
      '#2 - Transparent Icons & Drop Shadow_31': -4370429751426449405
      '#2 - Transparent Icons & Drop Shadow_32': 8573033428421157452
      '#2 - Transparent Icons & Drop Shadow_33': 5540574519761578848
      '#2 - Transparent Icons & Drop Shadow_34': 7183686000987961966
      '#2 - Transparent Icons & Drop Shadow_35': -7516765442453985350
      '#2 - Transparent Icons & Drop Shadow_36': 4697799021792961516
      '#2 - Transparent Icons & Drop Shadow_37': 5864141120653579955
      '#2 - Transparent Icons & Drop Shadow_38': 3378112070155288112
      '#2 - Transparent Icons & Drop Shadow_39': 4884337891824352890
      '#2 - Transparent Icons & Drop Shadow_4': 6948773886327953995
      '#2 - Transparent Icons & Drop Shadow_40': 8579901613499352774
      '#2 - Transparent Icons & Drop Shadow_41': -5692852124484312159
      '#2 - Transparent Icons & Drop Shadow_42': 8469456256804645828
      '#2 - Transparent Icons & Drop Shadow_43': 7788705131787259084
      '#2 - Transparent Icons & Drop Shadow_44': 8244745962220302406
      '#2 - Transparent Icons & Drop Shadow_45': 8013616204054527717
      '#2 - Transparent Icons & Drop Shadow_46': -3259243744252596725
      '#2 - Transparent Icons & Drop Shadow_47': -5164983277317961741
      '#2 - Transparent Icons & Drop Shadow_48': 183573810413276680
      '#2 - Transparent Icons & Drop Shadow_49': 1704530735959705138
      '#2 - Transparent Icons & Drop Shadow_5': -4768308916089180935
      '#2 - Transparent Icons & Drop Shadow_50': -7907440099980241561
      '#2 - Transparent Icons & Drop Shadow_51': 969911064562707275
      '#2 - Transparent Icons & Drop Shadow_52': 8921443712075806451
      '#2 - Transparent Icons & Drop Shadow_53': 2105162914696504809
      '#2 - Transparent Icons & Drop Shadow_54': 5191580535614204767
      '#2 - Transparent Icons & Drop Shadow_55': -950096140469595123
      '#2 - Transparent Icons & Drop Shadow_56': 3583167426799192178
      '#2 - Transparent Icons & Drop Shadow_57': -7405286937500457954
      '#2 - Transparent Icons & Drop Shadow_58': 821283588467877935
      '#2 - Transparent Icons & Drop Shadow_59': -6451020461040997553
      '#2 - Transparent Icons & Drop Shadow_6': 1842380838397042475
      '#2 - Transparent Icons & Drop Shadow_60': -5443706011219761410
      '#2 - Transparent Icons & Drop Shadow_61': 2770867968450680294
      '#2 - Transparent Icons & Drop Shadow_62': -5606345008730570156
      '#2 - Transparent Icons & Drop Shadow_63': -2027895187021310853
      '#2 - Transparent Icons & Drop Shadow_64': -7822049742711148976
      '#2 - Transparent Icons & Drop Shadow_65': -5813384638183593889
      '#2 - Transparent Icons & Drop Shadow_66': 9158683398484645407
      '#2 - Transparent Icons & Drop Shadow_67': -7656981068869977111
      '#2 - Transparent Icons & Drop Shadow_68': 1056014759243546959
      '#2 - Transparent Icons & Drop Shadow_69': 249412086356771944
      '#2 - Transparent Icons & Drop Shadow_7': 7685470968617421020
      '#2 - Transparent Icons & Drop Shadow_70': 3964465338351796908
      '#2 - Transparent Icons & Drop Shadow_71': 4240453425555717549
      '#2 - Transparent Icons & Drop Shadow_72': -7387093150480851852
      '#2 - Transparent Icons & Drop Shadow_73': -11322859963825669
      '#2 - Transparent Icons & Drop Shadow_74': 7526374455504009102
      '#2 - Transparent Icons & Drop Shadow_75': -7399256461421256914
      '#2 - Transparent Icons & Drop Shadow_76': -6184507285319492425
      '#2 - Transparent Icons & Drop Shadow_77': -9147071376023271221
      '#2 - Transparent Icons & Drop Shadow_78': 7819060303277530886
      '#2 - Transparent Icons & Drop Shadow_79': 4620036233086308783
      '#2 - Transparent Icons & Drop Shadow_8': 804741048422990168
      '#2 - Transparent Icons & Drop Shadow_80': -6312372248548970523
      '#2 - Transparent Icons & Drop Shadow_81': -2581948243186363227
      '#2 - Transparent Icons & Drop Shadow_82': 2831401197825969033
      '#2 - Transparent Icons & Drop Shadow_83': 5688385721555123385
      '#2 - Transparent Icons & Drop Shadow_84': 1484917075010711910
      '#2 - Transparent Icons & Drop Shadow_85': 8734278466190218363
      '#2 - Transparent Icons & Drop Shadow_86': 9153715404705889837
      '#2 - Transparent Icons & Drop Shadow_87': 5970681058612394725
      '#2 - Transparent Icons & Drop Shadow_88': 5576558967255421441
      '#2 - Transparent Icons & Drop Shadow_89': 5862109787822352134
      '#2 - Transparent Icons & Drop Shadow_9': -5628875207666852851
      '#2 - Transparent Icons & Drop Shadow_90': -6608148472023216053
      '#2 - Transparent Icons & Drop Shadow_91': -7492221114004634305
      '#2 - Transparent Icons & Drop Shadow_92': -8659921585605857471
      '#2 - Transparent Icons & Drop Shadow_93': -8072734333329823742
      '#2 - Transparent Icons & Drop Shadow_94': 6351365340128835113
      '#2 - Transparent Icons & Drop Shadow_95': -3611065304843812169
      '#2 - Transparent Icons & Drop Shadow_96': -7911167414898439247
      '#2 - Transparent Icons & Drop Shadow_97': 1248619470624631663
      '#2 - Transparent Icons & Drop Shadow_98': -4959899092030057782
      '#2 - Transparent Icons & Drop Shadow_99': 5290622554995841036
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

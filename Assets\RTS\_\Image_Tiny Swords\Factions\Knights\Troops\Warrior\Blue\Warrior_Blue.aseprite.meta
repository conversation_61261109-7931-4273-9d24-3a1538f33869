fileFormatVersion: 2
guid: f7f8a62679657c5459e910ecd2b230f5
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 809
      y: 479
      width: 78
      height: 91
    spriteID: cb2b515e9043bc142aac16c9d2668c59
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 809, y: 479}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 895
      y: 479
      width: 78
      height: 91
    spriteID: 049dcb42f18869a48b07ee10f1d0930d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 895, y: 479}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 241
      y: 590
      width: 78
      height: 91
    spriteID: 131447744453cd54f84594188bd2ff2e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 241, y: 590}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.42500004, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 721
      y: 479
      width: 80
      height: 89
    spriteID: a26d89bd8856f8348809f5b97fbee718
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 721, y: 479}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.42500004, y: -0.6436781}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 413
      y: 590
      width: 80
      height: 87
    spriteID: d30040eeb2ab5e74983bd7609bd8ea74
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 413, y: 590}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.42307693, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 501
      y: 590
      width: 78
      height: 88
    spriteID: ae0e5a1f159156f4c88d1e8512d0d758
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 501, y: 590}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 327
      y: 590
      width: 78
      height: 91
    spriteID: 529920bb8af6cc543ab163d3cb2d17cb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 327, y: 590}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.41666672, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 956
      y: 129
      width: 60
      height: 93
    spriteID: 74348c30c0133fa4fa1749d2acee0340
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 956, y: 129}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.40579712, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 661
      y: 590
      width: 69
      height: 94
    spriteID: 0dd4b1dc57567824aac38abc141bed7e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 661, y: 590}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.41025642, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 825
      y: 590
      width: 78
      height: 80
    spriteID: fe3a6deb8cdc1644086f1c031ddd8edb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 825, y: 590}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44210526, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 538
      y: 274
      width: 95
      height: 93
    spriteID: f6425633956c9f749887b409e257a888
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 538, y: 274}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.43820223, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 641
      y: 274
      width: 89
      height: 94
    spriteID: ff17617505cccbd47a6980d5f8bedc78
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 641, y: 274}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.4177215, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 590
      width: 79
      height: 80
    spriteID: 7988b17688bd925488f0295cf367490c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 590}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5352112, y: -0.5185185}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 590
      width: 71
      height: 108
    spriteID: 2ba313dabd7f9914385ea251899eaa70
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 590}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 590
      width: 71
      height: 112
    spriteID: d5df94e0f0258244a9aa63601eecede4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 590}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 83
      y: 590
      width: 71
      height: 112
    spriteID: 8367f03af4e757841ab46135d93d28d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 83, y: 590}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.18691586, y: -0.49107146}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 858
      y: 4
      width: 107
      height: 112
    spriteID: 1d3b0888bb9cf54499d2996a33cfad91
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 858, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.20560749, y: -0.57608694}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 841
      y: 129
      width: 107
      height: 92
    spriteID: 1b5c8d7327d60eb4599a093124461594
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 841, y: 129}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.28999996, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 430
      y: 274
      width: 100
      height: 89
    spriteID: ef38ef25b0d0e0b499aa5361351c5e89
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 430, y: 274}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.75609756, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 631
      y: 479
      width: 82
      height: 89
    spriteID: c345f95cf679a2a4987d62b7b138de90
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 631, y: 479}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 359
      y: 479
      width: 84
      height: 89
    spriteID: be9e0afb71097b3489c15e6dadf1d071
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 359, y: 479}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 451
      y: 479
      width: 84
      height: 89
    spriteID: 5341819d0d27eed4299bc50b4c19b2c0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 451, y: 479}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.30894306, y: -0.45999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 593
      y: 4
      width: 123
      height: 100
    spriteID: 2d01c8ffa9ce5a34290c1162a6008680
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 593, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.29508197, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 508
      y: 129
      width: 122
      height: 94
    spriteID: 0020f79e6e8660c48a09392fd36420e0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 508, y: 129}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.40000004, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 916
      y: 274
      width: 85
      height: 88
    spriteID: e6045a8ea41ff1f4ea8147b4c636731d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 916, y: 274}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.4871795, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 186
      y: 479
      width: 78
      height: 101
    spriteID: fc72626b0130e5b40a8bd085b9f818ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 186, y: 479}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 479
      width: 83
      height: 103
    spriteID: c0f32b8adca3cc949bab0d89a67dc673
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 479}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 95
      y: 479
      width: 83
      height: 103
    spriteID: a89880a524229c14aba4dc3612a84658
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 95, y: 479}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.42372882, y: -0.09489051}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 129
      width: 118
      height: 137
    spriteID: a4c96bb6853737d4b9f0e84d4b2f25aa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 129}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.6179775, y: -0.20161289}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 638
      y: 129
      width: 89
      height: 124
    spriteID: cfe571ae91e092e41baf8d42be186fa1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 638, y: 129}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.6363636, y: -0.5252526}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 587
      y: 590
      width: 66
      height: 99
    spriteID: dca98c9ed82fbb34aa8bc95c337758f3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 587, y: 590}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.75, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 543
      y: 479
      width: 80
      height: 93
    spriteID: 73ab6b127287c1a46b53f45b9304352f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 543, y: 479}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 274
      width: 81
      height: 94
    spriteID: 53887c3ab181577419818cf34ec83ade
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 274}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 827
      y: 274
      width: 81
      height: 94
    spriteID: b26c9e042b81c9c40907c6b776ae8ce9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 827, y: 274}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.39041096, y: -0.23931624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 146
      height: 117
    spriteID: d3c5aa53f1a158d42b2aa6d2410fafbd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.24137934, y: -0.24786326}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 469
      y: 4
      width: 116
      height: 117
    spriteID: 154aaf24fbd6d4641b82145a9d2d63fd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 469, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.34343436, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 274
      width: 99
      height: 94
    spriteID: d8de30dabce278a4e9d8dacf3eeaec50
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 274}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.65254235, y: -0.56565654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 382
      y: 129
      width: 118
      height: 99
    spriteID: 9c049b3ca9fef444090e282bac6bd394
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 382, y: 129}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 130
      y: 129
      width: 118
      height: 101
    spriteID: 7985a17f30f8ec945b03aabb17dde28a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 130, y: 129}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 256
      y: 129
      width: 118
      height: 101
    spriteID: 0207d6c713ee5c7488dae31624c1ca71
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 256, y: 129}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.5037038, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 326
      y: 4
      width: 135
      height: 107
    spriteID: 9df4ecad809ffbe4382624dcf4c1aa91
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 326, y: 4}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.26530612, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 735
      y: 129
      width: 98
      height: 107
    spriteID: e0fd87e157bbc794ab2620afb0deb545
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 735, y: 129}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.25316453, y: -0.5833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 272
      y: 479
      width: 79
      height: 96
    spriteID: 2cc8039a2b50ae1489f22694b1361aa6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 272, y: 479}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.20618555, y: -0.6086956}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 325
      y: 274
      width: 97
      height: 92
    spriteID: b32ab321af4cdd74fae8031ddd776d9a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 325, y: 274}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 274
      width: 99
      height: 91
    spriteID: 95cafc48185f3c44fbfd79aef6de2828
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 274}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 218
      y: 274
      width: 99
      height: 91
    spriteID: 8df5fa329c98e6249b1453a337e45231
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 218, y: 274}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.54375005, y: -0.57}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 158
      y: 4
      width: 160
      height: 100
    spriteID: 96b82dfb3feddf74a8c660c4ffae95a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 158, y: 4}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.72222227, y: -0.58762884}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 724
      y: 4
      width: 126
      height: 97
    spriteID: bbc370f704e2b354fb325d408b1e6ff0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 724, y: 4}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.59782606, y: -0.58947366}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 376
      width: 92
      height: 95
    spriteID: 888c755b676484740a4c820d8e61833c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 376}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1486572119
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Warrior_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: cb2b515e9043bc142aac16c9d2668c59
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 049dcb42f18869a48b07ee10f1d0930d
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 131447744453cd54f84594188bd2ff2e
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 89
      spriteId: a26d89bd8856f8348809f5b97fbee718
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 87
      spriteId: d30040eeb2ab5e74983bd7609bd8ea74
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 88
      spriteId: ae0e5a1f159156f4c88d1e8512d0d758
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 529920bb8af6cc543ab163d3cb2d17cb
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 71
        y: 56
        width: 60
        height: 93
      spriteId: 74348c30c0133fa4fa1749d2acee0340
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 56
        width: 69
        height: 94
      spriteId: 0dd4b1dc57567824aac38abc141bed7e
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 56
        width: 78
        height: 80
      spriteId: fe3a6deb8cdc1644086f1c031ddd8edb
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 56
        width: 95
        height: 93
      spriteId: f6425633956c9f749887b409e257a888
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 89
        height: 94
      spriteId: ff17617505cccbd47a6980d5f8bedc78
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 79
        height: 80
      spriteId: 7988b17688bd925488f0295cf367490c
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 71
        height: 108
      spriteId: 2ba313dabd7f9914385ea251899eaa70
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: d5df94e0f0258244a9aa63601eecede4
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: 8367f03af4e757841ab46135d93d28d2
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 55
        width: 107
        height: 112
      spriteId: 1d3b0888bb9cf54499d2996a33cfad91
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 74
        y: 53
        width: 107
        height: 92
      spriteId: 1b5c8d7327d60eb4599a093124461594
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 56
        width: 100
        height: 89
      spriteId: ef38ef25b0d0e0b499aa5361351c5e89
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 34
        y: 56
        width: 82
        height: 89
      spriteId: c345f95cf679a2a4987d62b7b138de90
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: be9e0afb71097b3489c15e6dadf1d071
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: 5341819d0d27eed4299bc50b4c19b2c0
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 46
        width: 123
        height: 100
      spriteId: 2d01c8ffa9ce5a34290c1162a6008680
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 56
        width: 122
        height: 94
      spriteId: 0020f79e6e8660c48a09392fd36420e0
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 85
        height: 88
      spriteId: e6045a8ea41ff1f4ea8147b4c636731d
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 78
        height: 101
      spriteId: fc72626b0130e5b40a8bd085b9f818ca
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: c0f32b8adca3cc949bab0d89a67dc673
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: a89880a524229c14aba4dc3612a84658
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 13
        width: 118
        height: 137
      spriteId: a4c96bb6853737d4b9f0e84d4b2f25aa
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 25
        width: 89
        height: 124
      spriteId: cfe571ae91e092e41baf8d42be186fa1
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 52
        width: 66
        height: 99
      spriteId: dca98c9ed82fbb34aa8bc95c337758f3
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 36
        y: 56
        width: 80
        height: 93
      spriteId: 73ab6b127287c1a46b53f45b9304352f
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: 53887c3ab181577419818cf34ec83ade
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: b26c9e042b81c9c40907c6b776ae8ce9
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 28
        width: 146
        height: 117
      spriteId: d3c5aa53f1a158d42b2aa6d2410fafbd
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 29
        width: 116
        height: 117
      spriteId: 154aaf24fbd6d4641b82145a9d2d63fd
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 99
        height: 94
      spriteId: d8de30dabce278a4e9d8dacf3eeaec50
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 56
        width: 118
        height: 99
      spriteId: 9c049b3ca9fef444090e282bac6bd394
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 7985a17f30f8ec945b03aabb17dde28a
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 0207d6c713ee5c7488dae31624c1ca71
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 57
        width: 135
        height: 107
      spriteId: 9df4ecad809ffbe4382624dcf4c1aa91
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 57
        width: 98
        height: 107
      spriteId: e0fd87e157bbc794ab2620afb0deb545
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 79
        height: 96
      spriteId: 2cc8039a2b50ae1489f22694b1361aa6
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 97
        height: 92
      spriteId: b32ab321af4cdd74fae8031ddd776d9a
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 95cafc48185f3c44fbfd79aef6de2828
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 8df5fa329c98e6249b1453a337e45231
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 9
        y: 57
        width: 160
        height: 100
      spriteId: 96b82dfb3feddf74a8c660c4ffae95a8
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 5
        y: 57
        width: 126
        height: 97
      spriteId: bbc370f704e2b354fb325d408b1e6ff0
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 56
        width: 92
        height: 95
      spriteId: 888c755b676484740a4c820d8e61833c
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 1024, y: 1024}

fileFormatVersion: 2
guid: 16a295e680af9a94ca773316d8028fc9
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44705883, y: -0.826087}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 85
      height: 69
    spriteID: 60ace337fadacda4bb3a9822a6942aaf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.44186047, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 4
      width: 86
      height: 68
    spriteID: d1cfdfc58f3b91a4b8110ef3ae964cd2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 4}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44578314, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 380
      y: 4
      width: 83
      height: 68
    spriteID: cb5266a2d3895444b874302f1b5ffc27
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 380, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.43373492, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 88
      y: 81
      width: 83
      height: 68
    spriteID: e8e88b967d388fe41bb3888ae24b54a6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 88, y: 81}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.4302326, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 286
      y: 4
      width: 86
      height: 66
    spriteID: 7360762fb10e9014bb6b543b01c166fc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 286, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.4534884, y: -0.8769231}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 179
      y: 81
      width: 86
      height: 65
    spriteID: 543eb70b35de52a458a9927943d4cd2a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 179, y: 81}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.44827586, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 191
      y: 4
      width: 87
      height: 66
    spriteID: 418c092a502e3f142b72d5cd644e7407
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 191, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.45000002, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 164
      width: 80
      height: 64
    spriteID: cd3c2b14e0da7c44996fc31de9af3638
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 164}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.4375, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 236
      width: 80
      height: 67
    spriteID: 0e42e0916c6e64244ae4e15781f61da3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 236}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.43373492, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 185
      y: 164
      width: 83
      height: 60
    spriteID: 7c5befb1925d7cc4692a98f6bbc52c19
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 185, y: 164}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44705883, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 85
      height: 64
    spriteID: e081ed34873bbc8478ddac46a17ef25b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47560975, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 358
      y: 81
      width: 82
      height: 67
    spriteID: a2efbf4e8fd46fe48b609fca7fdccbd4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 358, y: 81}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.475, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 276
      y: 164
      width: 80
      height: 60
    spriteID: 70943de8d4d2a654d8c39f0fcd63744a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 276, y: 164}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5324675, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 81
      width: 77
      height: 72
    spriteID: 0050dc5c25b205c488cddc49ad16891e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 81}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.59210527, y: -0.76}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 81
      width: 76
      height: 75
    spriteID: c3a16f59dd19a1c499ef1a297094cd5b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 81}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.53333336, y: -0.95}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 231
      y: 311
      width: 75
      height: 60
    spriteID: f6f08fead33566e40aa16fc7c67f469e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 231, y: 311}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.51351345, y: -0.9047619}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 364
      y: 164
      width: 74
      height: 63
    spriteID: 78368e2caaebf124f901c64b315e7ce4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 364, y: 164}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.53424656, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 76
      y: 311
      width: 73
      height: 68
    spriteID: ad4a6cf1b06a31343926bbb38b0f6603
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 76, y: 311}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.640625, y: -0.7037037}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 311
      width: 64
      height: 81
    spriteID: 97027ff977c8960408b5299924317b7e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 311}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5606061, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 157
      y: 311
      width: 66
      height: 72
    spriteID: 34ed1071781002b49900e6f8f0a04add
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 157, y: 311}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 481
      y: 164
      width: 27
      height: 51
    spriteID: a33ee74650e4cee4a995740c7cfc6901
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 481, y: 164}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.44444445, y: -1.4423077}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 446
      y: 164
      width: 27
      height: 52
    spriteID: 77f038e757061e742839112a16da594d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 446, y: 164}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.46153846, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 314
      y: 311
      width: 26
      height: 51
    spriteID: 788e514ef4182794d9840848d99c074a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 314, y: 311}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.48, y: -1.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 483
      y: 81
      width: 25
      height: 50
    spriteID: b73c8e9721fdad14bb072053b0b9e4b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 483, y: 81}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 448
      y: 81
      width: 27
      height: 51
    spriteID: d35e010fdca92994daaa9bccc247c3c2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 448, y: 81}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.42857143, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 471
      y: 4
      width: 28
      height: 51
    spriteID: 6563670c2f0f39f4dbf84b26fe46f9c2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 471, y: 4}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3793803016
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: TNT_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 85
        height: 69
      spriteId: 60ace337fadacda4bb3a9822a6942aaf
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 86
        height: 68
      spriteId: d1cfdfc58f3b91a4b8110ef3ae964cd2
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 83
        height: 68
      spriteId: cb5266a2d3895444b874302f1b5ffc27
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 57
        width: 83
        height: 68
      spriteId: e8e88b967d388fe41bb3888ae24b54a6
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 86
        height: 66
      spriteId: 7360762fb10e9014bb6b543b01c166fc
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 86
        height: 65
      spriteId: 543eb70b35de52a458a9927943d4cd2a
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 87
        height: 66
      spriteId: 418c092a502e3f142b72d5cd644e7407
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 80
        height: 64
      spriteId: cd3c2b14e0da7c44996fc31de9af3638
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 59
        width: 80
        height: 67
      spriteId: 0e42e0916c6e64244ae4e15781f61da3
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 83
        height: 60
      spriteId: 7c5befb1925d7cc4692a98f6bbc52c19
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 85
        height: 64
      spriteId: e081ed34873bbc8478ddac46a17ef25b
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 59
        width: 82
        height: 67
      spriteId: a2efbf4e8fd46fe48b609fca7fdccbd4
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 80
        height: 60
      spriteId: 70943de8d4d2a654d8c39f0fcd63744a
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 77
        height: 72
      spriteId: 0050dc5c25b205c488cddc49ad16891e
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 57
        width: 76
        height: 75
      spriteId: c3a16f59dd19a1c499ef1a297094cd5b
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 57
        width: 75
        height: 60
      spriteId: f6f08fead33566e40aa16fc7c67f469e
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 74
        height: 63
      spriteId: 78368e2caaebf124f901c64b315e7ce4
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 73
        height: 68
      spriteId: ad4a6cf1b06a31343926bbb38b0f6603
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 64
        height: 81
      spriteId: 97027ff977c8960408b5299924317b7e
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 66
        height: 72
      spriteId: 34ed1071781002b49900e6f8f0a04add
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: a33ee74650e4cee4a995740c7cfc6901
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 52
      spriteId: 77f038e757061e742839112a16da594d
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 26
        height: 51
      spriteId: 788e514ef4182794d9840848d99c074a
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 25
        height: 50
      spriteId: b73c8e9721fdad14bb072053b0b9e4b7
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: d35e010fdca92994daaa9bccc247c3c2
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 28
        height: 51
      spriteId: 6563670c2f0f39f4dbf84b26fe46f9c2
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

fileFormatVersion: 2
guid: 321c17d682ace124f87dd0931561895b
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 374509577666808378
    second: <PERSON><PERSON><PERSON><PERSON>_0
  - first:
      213: -2501148103626578215
    second: <PERSON><PERSON>_<PERSON>_1
  - first:
      213: 7792214154831053348
    second: <PERSON><PERSON>_<PERSON>_2
  - first:
      213: 4793238650062290934
    second: <PERSON><PERSON>_<PERSON>_3
  - first:
      213: 1098690865857626312
    second: <PERSON><PERSON>_<PERSON>_4
  - first:
      213: -5321037528313582103
    second: <PERSON><PERSON>_<PERSON>_5
  - first:
      213: -8106885055767974897
    second: <PERSON><PERSON>_<PERSON>_6
  - first:
      213: 2123760179546117352
    second: <PERSON><PERSON>_<PERSON>_7
  - first:
      213: 5720705526620791537
    second: <PERSON><PERSON>_<PERSON>_8
  - first:
      213: -4971662062786198404
    second: <PERSON><PERSON><PERSON><PERSON>_9
  - first:
      213: -7296614750721971803
    second: <PERSON><PERSON>_<PERSON>_10
  - first:
      213: 6059372588945510605
    second: <PERSON><PERSON>_<PERSON>_11
  - first:
      213: 2716483723853378319
    second: <PERSON><PERSON>_<PERSON>_12
  - first:
      213: -4392794721805224771
    second: Barr<PERSON>_Purple_13
  - first:
      213: 5933670487553760919
    second: <PERSON><PERSON>_Purple_14
  - first:
      213: 8087102604193543662
    second: <PERSON><PERSON>_Purple_15
  - first:
      213: -2422706781576550745
    second: Barr<PERSON>_<PERSON>_16
  - first:
      213: -8521054238117765900
    second: <PERSON>el_Purple_17
  - first:
      213: -4966138846449464091
    second: Barrel_Purple_18
  - first:
      213: 213141599897976603
    second: Barrel_Purple_19
  - first:
      213: 6537648321963130873
    second: Barrel_Purple_20
  - first:
      213: -2693380528472036840
    second: Barrel_Purple_21
  - first:
      213: 3868027003054179029
    second: Barrel_Purple_22
  - first:
      213: 2355610125792969761
    second: Barrel_Purple_23
  - first:
      213: -6159135084754394775
    second: Barrel_Purple_24
  - first:
      213: -3520691947671439618
    second: Barrel_Purple_25
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Barrel_Purple_0
      rect:
        serializedVersion: 2
        x: 38
        y: 668
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3acfc50a76823500800000000000000
      internalID: 374509577666808378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_1
      rect:
        serializedVersion: 2
        x: 34
        y: 540
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d622b82d032a4dd0800000000000000
      internalID: -2501148103626578215
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_2
      rect:
        serializedVersion: 2
        x: 170
        y: 536
        width: 44
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 422c9782d71832c60800000000000000
      internalID: 7792214154831053348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_3
      rect:
        serializedVersion: 2
        x: 294
        y: 536
        width: 52
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ffb2a41821058240800000000000000
      internalID: 4793238650062290934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_4
      rect:
        serializedVersion: 2
        x: 420
        y: 537
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c0096975855f3f00800000000000000
      internalID: 1098690865857626312
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_5
      rect:
        serializedVersion: 2
        x: 550
        y: 537
        width: 52
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9eda8506f80e726b0800000000000000
      internalID: -5321037528313582103
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_6
      rect:
        serializedVersion: 2
        x: 678
        y: 537
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0ce6007efe8e7f80800000000000000
      internalID: -8106885055767974897
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_7
      rect:
        serializedVersion: 2
        x: 38
        y: 409
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e457f191ac197d10800000000000000
      internalID: 2123760179546117352
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_8
      rect:
        serializedVersion: 2
        x: 36
        y: 281
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f2a8daed57046f40800000000000000
      internalID: 5720705526620791537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_9
      rect:
        serializedVersion: 2
        x: 166
        y: 294
        width: 52
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c74c754e1bb110bb0800000000000000
      internalID: -4971662062786198404
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_10
      rect:
        serializedVersion: 2
        x: 173
        y: 280
        width: 35
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a967581b873dba90800000000000000
      internalID: -7296614750721971803
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_11
      rect:
        serializedVersion: 2
        x: 298
        y: 280
        width: 44
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc016d50447371450800000000000000
      internalID: 6059372588945510605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_12
      rect:
        serializedVersion: 2
        x: 418
        y: 284
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f033966ed83e2b520800000000000000
      internalID: 2716483723853378319
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_13
      rect:
        serializedVersion: 2
        x: 552
        y: 284
        width: 47
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db4b69b7978a903c0800000000000000
      internalID: -4392794721805224771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_14
      rect:
        serializedVersion: 2
        x: 678
        y: 284
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79e5cd8ded1a85250800000000000000
      internalID: 5933670487553760919
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_15
      rect:
        serializedVersion: 2
        x: 36
        y: 153
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee5b711a8f82b3070800000000000000
      internalID: 8087102604193543662
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_16
      rect:
        serializedVersion: 2
        x: 166
        y: 152
        width: 52
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a62339a401d06ed0800000000000000
      internalID: -2422706781576550745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_17
      rect:
        serializedVersion: 2
        x: 295
        y: 152
        width: 50
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4fc572121522fb980800000000000000
      internalID: -8521054238117765900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_18
      rect:
        serializedVersion: 2
        x: 56
        y: 87
        width: 10
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ec8c18e70bb41bb0800000000000000
      internalID: -4966138846449464091
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_19
      rect:
        serializedVersion: 2
        x: 168
        y: 28
        width: 48
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1ff6c4e72b35f200800000000000000
      internalID: 213141599897976603
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_20
      rect:
        serializedVersion: 2
        x: 184
        y: 103
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ff7a09c2846aba50800000000000000
      internalID: 6537648321963130873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_21
      rect:
        serializedVersion: 2
        x: 294
        y: 28
        width: 52
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 81a6873fda03f9ad0800000000000000
      internalID: -2693380528472036840
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_22
      rect:
        serializedVersion: 2
        x: 52
        y: 85
        width: 8
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d2c9aac01efda530800000000000000
      internalID: 3868027003054179029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_23
      rect:
        serializedVersion: 2
        x: 70
        y: 80
        width: 14
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1240439cdeec0b020800000000000000
      internalID: 2355610125792969761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_24
      rect:
        serializedVersion: 2
        x: 332
        y: 96
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 961a19dd54b568aa0800000000000000
      internalID: -6159135084754394775
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Purple_25
      rect:
        serializedVersion: 2
        x: 34
        y: 28
        width: 59
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef63777b35df32fc0800000000000000
      internalID: -3520691947671439618
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Barrel_Purple_0: 374509577666808378
      Barrel_Purple_1: -2501148103626578215
      Barrel_Purple_10: -7296614750721971803
      Barrel_Purple_11: 6059372588945510605
      Barrel_Purple_12: 2716483723853378319
      Barrel_Purple_13: -4392794721805224771
      Barrel_Purple_14: 5933670487553760919
      Barrel_Purple_15: 8087102604193543662
      Barrel_Purple_16: -2422706781576550745
      Barrel_Purple_17: -8521054238117765900
      Barrel_Purple_18: -4966138846449464091
      Barrel_Purple_19: 213141599897976603
      Barrel_Purple_2: 7792214154831053348
      Barrel_Purple_20: 6537648321963130873
      Barrel_Purple_21: -2693380528472036840
      Barrel_Purple_22: 3868027003054179029
      Barrel_Purple_23: 2355610125792969761
      Barrel_Purple_24: -6159135084754394775
      Barrel_Purple_25: -3520691947671439618
      Barrel_Purple_3: 4793238650062290934
      Barrel_Purple_4: 1098690865857626312
      Barrel_Purple_5: -5321037528313582103
      Barrel_Purple_6: -8106885055767974897
      Barrel_Purple_7: 2123760179546117352
      Barrel_Purple_8: 5720705526620791537
      Barrel_Purple_9: -4971662062786198404
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

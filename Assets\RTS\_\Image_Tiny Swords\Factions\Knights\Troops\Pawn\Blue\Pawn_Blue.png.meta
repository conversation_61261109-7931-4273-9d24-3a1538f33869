fileFormatVersion: 2
guid: 82c50a664887af9428423d437f932d05
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2526443156231494169
    second: Pawn_Blue_0
  - first:
      213: 85516132271839533
    second: Pawn_Blue_1
  - first:
      213: 2298971153527949317
    second: Pawn_Blue_2
  - first:
      213: -6279286161730005596
    second: Pawn_Blue_3
  - first:
      213: 3098459446524620889
    second: Pawn_Blue_4
  - first:
      213: 7598609044430795391
    second: Pawn_Blue_5
  - first:
      213: -3780493671843434630
    second: Pawn_Blue_6
  - first:
      213: -6496901974590534408
    second: Pawn_Blue_7
  - first:
      213: -5659882513805570622
    second: Pawn_Blue_8
  - first:
      213: -7810840403246613998
    second: Pawn_Blue_9
  - first:
      213: 418046305020467345
    second: Pawn_Blue_10
  - first:
      213: 4595005759171413359
    second: Pawn_Blue_11
  - first:
      213: 997159254126777594
    second: Pawn_Blue_12
  - first:
      213: -8916734054627887817
    second: Pawn_Blue_13
  - first:
      213: 6250710392093768849
    second: Pawn_Blue_14
  - first:
      213: 4853680120448552626
    second: Pawn_Blue_15
  - first:
      213: 2955165806944396838
    second: Pawn_Blue_16
  - first:
      213: -6152626301178634141
    second: Pawn_Blue_17
  - first:
      213: -5537870070541023844
    second: Pawn_Blue_18
  - first:
      213: 7042123909930582926
    second: Pawn_Blue_19
  - first:
      213: -5431640271881036569
    second: Pawn_Blue_20
  - first:
      213: -3948862315358351415
    second: Pawn_Blue_21
  - first:
      213: -8928817820482097097
    second: Pawn_Blue_22
  - first:
      213: 1911732741585851992
    second: Pawn_Blue_23
  - first:
      213: 6447538333027347605
    second: Pawn_Blue_24
  - first:
      213: -3013262475566291833
    second: Pawn_Blue_25
  - first:
      213: -4950327178770515442
    second: Pawn_Blue_26
  - first:
      213: -1740134095675873224
    second: Pawn_Blue_27
  - first:
      213: 8191108255482568448
    second: Pawn_Blue_28
  - first:
      213: -4011588955890851760
    second: Pawn_Blue_29
  - first:
      213: 6053010292413162328
    second: Pawn_Blue_30
  - first:
      213: -108865413175359257
    second: Pawn_Blue_31
  - first:
      213: -2876444873680751900
    second: Pawn_Blue_32
  - first:
      213: 4077430709402110876
    second: Pawn_Blue_33
  - first:
      213: -7077120319603751635
    second: Pawn_Blue_34
  - first:
      213: -2152789970493319388
    second: Pawn_Blue_35
  - first:
      213: 3805660289756520036
    second: Pawn_Blue_36
  - first:
      213: 5694982940656555304
    second: Pawn_Blue_37
  - first:
      213: 2052364809803933342
    second: Pawn_Blue_38
  - first:
      213: -387492165264437447
    second: Pawn_Blue_39
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Pawn_Blue_0
      rect:
        serializedVersion: 2
        x: 0
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e5ea97c55540fcd0800000000000000
      internalID: -2526443156231494169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_1
      rect:
        serializedVersion: 2
        x: 192
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d29d0ae1870df2100800000000000000
      internalID: 85516132271839533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_2
      rect:
        serializedVersion: 2
        x: 384
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50847ea651697ef10800000000000000
      internalID: 2298971153527949317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_3
      rect:
        serializedVersion: 2
        x: 576
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a59e07418e7bd8a0800000000000000
      internalID: -6279286161730005596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_4
      rect:
        serializedVersion: 2
        x: 768
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 950bd054370fffa20800000000000000
      internalID: 3098459446524620889
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_5
      rect:
        serializedVersion: 2
        x: 960
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f723e50baaea37960800000000000000
      internalID: 7598609044430795391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_6
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7fe82e090df88bc0800000000000000
      internalID: -3780493671843434630
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_7
      rect:
        serializedVersion: 2
        x: 192
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8fc4fff731e56d5a0800000000000000
      internalID: -6496901974590534408
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_8
      rect:
        serializedVersion: 2
        x: 384
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2cd8c2e47de0471b0800000000000000
      internalID: -5659882513805570622
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_9
      rect:
        serializedVersion: 2
        x: 576
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21a773f69025a9390800000000000000
      internalID: -7810840403246613998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_10
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 198d86646e23dc500800000000000000
      internalID: 418046305020467345
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_11
      rect:
        serializedVersion: 2
        x: 960
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f654940846db4cf30800000000000000
      internalID: 4595005759171413359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_12
      rect:
        serializedVersion: 2
        x: 0
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af458b6fe0f96dd00800000000000000
      internalID: 997159254126777594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_13
      rect:
        serializedVersion: 2
        x: 192
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 735b645ce95614480800000000000000
      internalID: -8916734054627887817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_14
      rect:
        serializedVersion: 2
        x: 384
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19c7792ebfbfeb650800000000000000
      internalID: 6250710392093768849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_15
      rect:
        serializedVersion: 2
        x: 576
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2be06b88a5cbb5340800000000000000
      internalID: 4853680120448552626
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_16
      rect:
        serializedVersion: 2
        x: 768
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6262d4fe4abd20920800000000000000
      internalID: 2955165806944396838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_17
      rect:
        serializedVersion: 2
        x: 960
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 360a5822afa7d9aa0800000000000000
      internalID: -6152626301178634141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_18
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c95822103888523b0800000000000000
      internalID: -5537870070541023844
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_19
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8382b3a266aab160800000000000000
      internalID: 7042123909930582926
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_20
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ec13d663ffee94b0800000000000000
      internalID: -5431640271881036569
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_21
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9cf832a65a2d239c0800000000000000
      internalID: -3948862315358351415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_22
      rect:
        serializedVersion: 2
        x: 768
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 730bcf9cf77761480800000000000000
      internalID: -8928817820482097097
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_23
      rect:
        serializedVersion: 2
        x: 960
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85655b7bcc6d78a10800000000000000
      internalID: 1911732741585851992
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_24
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59c2fcf04f14a7950800000000000000
      internalID: 6447538333027347605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_25
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7846bd05ebdbe26d0800000000000000
      internalID: -3013262475566291833
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_26
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0afb6ba8a7ec4bb0800000000000000
      internalID: -4950327178770515442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_27
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 838a1b5724dc9d7e0800000000000000
      internalID: -1740134095675873224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_28
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00fdecc5f89aca170800000000000000
      internalID: 8191108255482568448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_29
      rect:
        serializedVersion: 2
        x: 960
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 050ee335919f358c0800000000000000
      internalID: -4011588955890851760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_30
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 853e11d6acc900450800000000000000
      internalID: 6053010292413162328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_31
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e84b57cb7b3d7ef0800000000000000
      internalID: -108865413175359257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_32
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e2f340a0a0d418d0800000000000000
      internalID: -2876444873680751900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_33
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9b8e2680a1f59830800000000000000
      internalID: 4077430709402110876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_34
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d25c155c09409cd90800000000000000
      internalID: -7077120319603751635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Blue_35
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 427d8b48de0cf12e0800000000000000
      internalID: -2152789970493319388
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 77c7d35876467864b8adb2bcafe32b2d
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Pawn_Blue_0: -2526443156231494169
      Pawn_Blue_1: 85516132271839533
      Pawn_Blue_10: 418046305020467345
      Pawn_Blue_11: 4595005759171413359
      Pawn_Blue_12: 997159254126777594
      Pawn_Blue_13: -8916734054627887817
      Pawn_Blue_14: 6250710392093768849
      Pawn_Blue_15: 4853680120448552626
      Pawn_Blue_16: 2955165806944396838
      Pawn_Blue_17: -6152626301178634141
      Pawn_Blue_18: -5537870070541023844
      Pawn_Blue_19: 7042123909930582926
      Pawn_Blue_2: 2298971153527949317
      Pawn_Blue_20: -5431640271881036569
      Pawn_Blue_21: -3948862315358351415
      Pawn_Blue_22: -8928817820482097097
      Pawn_Blue_23: 1911732741585851992
      Pawn_Blue_24: 6447538333027347605
      Pawn_Blue_25: -3013262475566291833
      Pawn_Blue_26: -4950327178770515442
      Pawn_Blue_27: -1740134095675873224
      Pawn_Blue_28: 8191108255482568448
      Pawn_Blue_29: -4011588955890851760
      Pawn_Blue_3: -6279286161730005596
      Pawn_Blue_30: 6053010292413162328
      Pawn_Blue_31: -108865413175359257
      Pawn_Blue_32: -2876444873680751900
      Pawn_Blue_33: 4077430709402110876
      Pawn_Blue_34: -7077120319603751635
      Pawn_Blue_35: -2152789970493319388
      Pawn_Blue_4: 3098459446524620889
      Pawn_Blue_5: 7598609044430795391
      Pawn_Blue_6: -3780493671843434630
      Pawn_Blue_7: -6496901974590534408
      Pawn_Blue_8: -5659882513805570622
      Pawn_Blue_9: -7810840403246613998
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

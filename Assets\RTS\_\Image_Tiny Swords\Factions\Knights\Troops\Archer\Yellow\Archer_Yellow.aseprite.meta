fileFormatVersion: 2
guid: 53e10518122f7d949be3b06efc2c5bc6
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 420
      width: 63
      height: 75
    spriteID: 70c5190ac8fa8ea4abff6e1b3689266d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.45161292, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 752
      width: 62
      height: 75
    spriteID: e11c35b1eaaa19548beea605e446e696
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 752}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 503
      width: 63
      height: 75
    spriteID: f3e12867e83454d4cb9b08c7db419814
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 503}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 586
      width: 63
      height: 75
    spriteID: 11544377c32e9cd43acdb5d5d1bbe9d7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 586}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.44776118, y: -0.7837838}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 568
      width: 67
      height: 74
    spriteID: 7243130e4cdeafd4f908e5a46406a0b4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 568}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.46153846, y: -0.81690145}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 835
      width: 65
      height: 71
    spriteID: 63afc6787625d5043970558ff5225765
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 835}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.46774188, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 914
      width: 62
      height: 73
    spriteID: fd569d054c5ed4741ac4f95e78986677
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 914}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.44776118, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 483
      width: 67
      height: 77
    spriteID: a66e046e2b0caf045893ef8d42b0ebe4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 483}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.44615382, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 733
      width: 65
      height: 76
    spriteID: 8fe429760c6ebb5499c298e9bea78363
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 733}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 126
      width: 62
      height: 70
    spriteID: 0ec5d177fce4e7c4bb34627f22625590
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 126}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.4861111, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 510
      width: 72
      height: 77
    spriteID: 3a7390d9e8b9add4fb5a2e3988f0ff88
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 510}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47826087, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 4
      width: 69
      height: 76
    spriteID: e0ce16c64f1c453498eac90f02584585
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 62
      height: 70
    spriteID: 0a455b224178ee64082944f2cde43d1f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.44615382, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 293
      width: 65
      height: 80
    spriteID: d52324fadf4ac28488cad66c72548804
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 293}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.38666663, y: -0.70731705}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 644
      width: 75
      height: 82
    spriteID: a7585aebe5ab84a499c596bddf0ef4b2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 644}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.46774188, y: -0.50877196}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 62
      height: 114
    spriteID: cea5ab94bdd17624ea1e38c2ef59dbbf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.530303, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 538
      width: 66
      height: 98
    spriteID: 8a04ce9f80a8d544da8cc28662a5089c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 538}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 734
      width: 66
      height: 92
    spriteID: e3e89197b6c3d7841b81f6e0b7c47d60
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 734}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 834
      width: 66
      height: 92
    spriteID: 8240fa5cb23a5e04b9434b9d3ec3b301
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 834}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.53623194, y: -0.6170213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 436
      width: 69
      height: 94
    spriteID: c62a23f5d9a54a94cb8161242e808988
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 436}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.46774188, y: -0.65909094}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 595
      width: 62
      height: 88
    spriteID: 65f0bb52b364f774ea4a4b2e8c7faab7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 595}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 88
      width: 64
      height: 75
    spriteID: 27369a6d9b123084d9427b352fce38ec
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 88}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 282
      width: 55
      height: 75
    spriteID: d9d08344b827a5245b656ba7441a56db
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 282}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 4
      width: 92
      height: 75
    spriteID: b0f87db543276614ab4fc30a7265e9d6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 4}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.51388896, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 853
      width: 72
      height: 73
    spriteID: 1e1069bf0d3c7a04a805de11244d42ab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 853}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 344
      width: 74
      height: 75
    spriteID: 1d86949df646b7942afab5bb418505d5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 344}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 427
      width: 74
      height: 75
    spriteID: a53225cfc91c41947a99886c78814cea
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 427}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.51282054, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 934
      width: 78
      height: 76
    spriteID: e01f7b4528907dc4091848ca0eb2c416
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 934}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.469697, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 650
      width: 66
      height: 75
    spriteID: 3d075a5454777704480b459970b92e0a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 650}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 171
      width: 64
      height: 75
    spriteID: 70ea2fe4c891c8549bb530a1b8765e10
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 171}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 365
      width: 55
      height: 75
    spriteID: 1284e24cb854eb24788157e5319190dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 365}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 87
      width: 92
      height: 75
    spriteID: d44d4b640ed1f4f47abf019987756614
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 87}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.46835443, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 85
      width: 79
      height: 73
    spriteID: 7dbe9ca6fb2906546b8398141ff06a58
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 85}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 691
      width: 73
      height: 73
    spriteID: b5b7bebb72c756e4b8cb842b9ed26603
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 691}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 772
      width: 73
      height: 73
    spriteID: 1d8fd4f341121ed45b1701dc92df07e2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 772}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.5625, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 4
      width: 80
      height: 73
    spriteID: 605201c1cd57ca4419f6168751893573
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49206352, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 669
      width: 63
      height: 75
    spriteID: a14606dbe31a51c49a01a512b26d1b6b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 669}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 254
      width: 64
      height: 75
    spriteID: 3a58687e2267d0148bd5954c26880004
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 254}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 448
      width: 55
      height: 75
    spriteID: 99fb6609c1284c347831fb8e5a717d32
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 448}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 170
      width: 92
      height: 75
    spriteID: 7b79e446ce03531409916e88ee1158c6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 170}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.52459013, y: -0.5930233}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 934
      width: 61
      height: 86
    spriteID: bfd4bad2c56d555499328b955fe1dd1d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 934}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 4
      width: 67
      height: 88
    spriteID: 04b86980040ddd84b821b3d59ef9e32c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 4}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 100
      width: 67
      height: 88
    spriteID: 1baf0183c0b055c419ef36a44fd40318
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 100}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.5675675, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 253
      width: 74
      height: 89
    spriteID: 926a29833d30d4b4a89d0a2028b7b9df
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 253}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 166
      width: 69
      height: 81
    spriteID: 33c2696f75ef30a4d946b27416aba52d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 166}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 337
      width: 64
      height: 75
    spriteID: 6af5c2ef63427ef49944400205eb40b1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 337}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 531
      width: 55
      height: 75
    spriteID: b2ebf6376c41aa745ac3dfaf54590b20
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 531}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.36904767, y: -0.70512825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 350
      width: 84
      height: 78
    spriteID: 3a43e09e2100edc459d3160290cad7c1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 350}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.58181816, y: -0.45744678}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 381
      width: 55
      height: 94
    spriteID: 133c4c2f36c844d4ba32e3734fbb674c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 381}
  - name: Frame_49
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 817
      width: 55
      height: 89
    spriteID: d42cf0c7f8491c349949e29d62256ed7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 817}
  - name: Frame_50
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 914
      width: 55
      height: 89
    spriteID: 4fa8b480c2e2e3940b86b10ba3633c87
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 914}
  - name: Frame_51
    originalName: 
    pivot: {x: 0.5932203, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 196
      width: 59
      height: 89
    spriteID: e8f0a34ca330f1d459641fc2e9c89875
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 196}
  - name: Frame_52
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 255
      width: 69
      height: 81
    spriteID: 641053a7225857149abb148f7fc0293d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 255}
  - name: Frame_53
    originalName: 
    pivot: {x: 0.49206352, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 995
      width: 63
      height: 14
    spriteID: 042adf89fe2ee724dbd07fc8ccc740d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 995}
  - name: Frame_54
    originalName: 
    pivot: {x: 0.6326531, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 614
      width: 49
      height: 14
    spriteID: 6bef55884dca5a64a97193cf9fc9b2d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 614}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 685912054
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Archer_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 70c5190ac8fa8ea4abff6e1b3689266d
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 75
      spriteId: e11c35b1eaaa19548beea605e446e696
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: f3e12867e83454d4cb9b08c7db419814
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 11544377c32e9cd43acdb5d5d1bbe9d7
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 74
      spriteId: 7243130e4cdeafd4f908e5a46406a0b4
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 65
        height: 71
      spriteId: 63afc6787625d5043970558ff5225765
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 73
      spriteId: fd569d054c5ed4741ac4f95e78986677
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 77
      spriteId: a66e046e2b0caf045893ef8d42b0ebe4
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 76
      spriteId: 8fe429760c6ebb5499c298e9bea78363
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: 0ec5d177fce4e7c4bb34627f22625590
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 72
        height: 77
      spriteId: 3a7390d9e8b9add4fb5a2e3988f0ff88
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 58
        width: 69
        height: 76
      spriteId: e0ce16c64f1c453498eac90f02584585
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: 0a455b224178ee64082944f2cde43d1f
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 80
      spriteId: d52324fadf4ac28488cad66c72548804
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 75
        height: 82
      spriteId: a7585aebe5ab84a499c596bddf0ef4b2
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 114
      spriteId: cea5ab94bdd17624ea1e38c2ef59dbbf
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 98
      spriteId: 8a04ce9f80a8d544da8cc28662a5089c
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: e3e89197b6c3d7841b81f6e0b7c47d60
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: 8240fa5cb23a5e04b9434b9d3ec3b301
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 69
        height: 94
      spriteId: c62a23f5d9a54a94cb8161242e808988
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 88
      spriteId: 65f0bb52b364f774ea4a4b2e8c7faab7
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 27369a6d9b123084d9427b352fce38ec
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: d9d08344b827a5245b656ba7441a56db
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: b0f87db543276614ab4fc30a7265e9d6
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 72
        height: 73
      spriteId: 1e1069bf0d3c7a04a805de11244d42ab
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: 1d86949df646b7942afab5bb418505d5
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: a53225cfc91c41947a99886c78814cea
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 78
        height: 76
      spriteId: e01f7b4528907dc4091848ca0eb2c416
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 66
        height: 75
      spriteId: 3d075a5454777704480b459970b92e0a
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 70ea2fe4c891c8549bb530a1b8765e10
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 1284e24cb854eb24788157e5319190dc
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: d44d4b640ed1f4f47abf019987756614
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 79
        height: 73
      spriteId: 7dbe9ca6fb2906546b8398141ff06a58
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: b5b7bebb72c756e4b8cb842b9ed26603
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 1d8fd4f341121ed45b1701dc92df07e2
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 58
        width: 80
        height: 73
      spriteId: 605201c1cd57ca4419f6168751893573
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 63
        height: 75
      spriteId: a14606dbe31a51c49a01a512b26d1b6b
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 3a58687e2267d0148bd5954c26880004
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 99fb6609c1284c347831fb8e5a717d32
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 7b79e446ce03531409916e88ee1158c6
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 51
        width: 61
        height: 86
      spriteId: bfd4bad2c56d555499328b955fe1dd1d
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: 04b86980040ddd84b821b3d59ef9e32c
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: 1baf0183c0b055c419ef36a44fd40318
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 48
        width: 74
        height: 89
      spriteId: 926a29833d30d4b4a89d0a2028b7b9df
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 33c2696f75ef30a4d946b27416aba52d
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 6af5c2ef63427ef49944400205eb40b1
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: b2ebf6376c41aa745ac3dfaf54590b20
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 55
        width: 84
        height: 78
      spriteId: 3a43e09e2100edc459d3160290cad7c1
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 43
        width: 55
        height: 94
      spriteId: 133c4c2f36c844d4ba32e3734fbb674c
    - name: Frame_49
      frameIndex: 49
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: d42cf0c7f8491c349949e29d62256ed7
    - name: Frame_50
      frameIndex: 50
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: 4fa8b480c2e2e3940b86b10ba3633c87
    - name: Frame_51
      frameIndex: 51
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 48
        width: 59
        height: 89
      spriteId: e8f0a34ca330f1d459641fc2e9c89875
    - name: Frame_52
      frameIndex: 52
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 641053a7225857149abb148f7fc0293d
    - name: Frame_53
      frameIndex: 53
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 63
        height: 14
      spriteId: 042adf89fe2ee724dbd07fc8ccc740d3
    - name: Frame_54
      frameIndex: 54
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 49
        height: 14
      spriteId: 6bef55884dca5a64a97193cf9fc9b2d2
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

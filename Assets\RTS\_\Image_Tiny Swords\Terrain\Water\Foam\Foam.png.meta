fileFormatVersion: 2
guid: 440ea22921fb4214fb5aac2fafe8ff77
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3794750738687043901
    second: Foam_0
  - first:
      213: -5671047757210412307
    second: Foam_1
  - first:
      213: -2426117413377100321
    second: Foam_2
  - first:
      213: 3742273068816866060
    second: Foam_3
  - first:
      213: -6926256516878963600
    second: Foam_4
  - first:
      213: 7752229987998269982
    second: Foam_5
  - first:
      213: -4710801888167562894
    second: Foam_6
  - first:
      213: 6853023835892009477
    second: Foam_7
  - first:
      213: 8475118079717534232
    second: Foam_8
  - first:
      213: -5206953073759774522
    second: Foam_9
  - first:
      213: -8207782414505271864
    second: Foam_10
  - first:
      213: 7863764357917645232
    second: Foam_11
  - first:
      213: 8903087110320532241
    second: Foam_12
  - first:
      213: 3869517897790652103
    second: Foam_13
  - first:
      213: 8309962125709424731
    second: Foam_14
  - first:
      213: 7739904226070429335
    second: Foam_15
  - first:
      213: -8872482218116189624
    second: Foam_16
  - first:
      213: 849305278062809255
    second: Foam_17
  - first:
      213: 2981339718526562382
    second: Foam_18
  - first:
      213: 7924269410711525378
    second: Foam_19
  - first:
      213: 2183709705315645786
    second: Foam_20
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Foam_0
      rect:
        serializedVersion: 2
        x: 54
        y: 54
        width: 84
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c658610f46565bc0800000000000000
      internalID: -3794750738687043901
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_1
      rect:
        serializedVersion: 2
        x: 247
        y: 54
        width: 83
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dea923f7c146c41b0800000000000000
      internalID: -5671047757210412307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_2
      rect:
        serializedVersion: 2
        x: 439
        y: 54
        width: 83
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd5b4401113b45ed0800000000000000
      internalID: -2426117413377100321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_3
      rect:
        serializedVersion: 2
        x: 631
        y: 54
        width: 83
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0b175757893fe330800000000000000
      internalID: 3742273068816866060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_4
      rect:
        serializedVersion: 2
        x: 822
        y: 54
        width: 84
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07ce130156ef0ef90800000000000000
      internalID: -6926256516878963600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_5
      rect:
        serializedVersion: 2
        x: 1015
        y: 55
        width: 82
        height: 82
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e1284cbd914759b60800000000000000
      internalID: 7752229987998269982
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_7
      rect:
        serializedVersion: 2
        x: 1206
        y: 54
        width: 84
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50ac0afaed4da1f50800000000000000
      internalID: 6853023835892009477
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Foam_9
      rect:
        serializedVersion: 2
        x: 1398
        y: 53
        width: 85
        height: 86
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c89b6c54cf2db7b0800000000000000
      internalID: -5206953073759774522
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 8e7ad1ce075792a44a307a793aab6995
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Foam_0: -3794750738687043901
      Foam_1: -5671047757210412307
      Foam_10: -8207782414505271864
      Foam_11: 7863764357917645232
      Foam_12: 8903087110320532241
      Foam_13: 3869517897790652103
      Foam_14: 8309962125709424731
      Foam_15: 7739904226070429335
      Foam_16: -8872482218116189624
      Foam_17: 849305278062809255
      Foam_18: 2981339718526562382
      Foam_19: 7924269410711525378
      Foam_2: -2426117413377100321
      Foam_20: 2183709705315645786
      Foam_3: 3742273068816866060
      Foam_4: -6926256516878963600
      Foam_5: 7752229987998269982
      Foam_6: -4710801888167562894
      Foam_7: 6853023835892009477
      Foam_8: 8475118079717534232
      Foam_9: -5206953073759774522
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

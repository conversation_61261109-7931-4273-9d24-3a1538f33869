fileFormatVersion: 2
guid: 5d8ad8164fcdaae408cac12912e60d60
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 321
      width: 50
      height: 70
    spriteID: 44fdb5f601f896d4c98e600920efa2ae
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 321}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 399
      width: 50
      height: 70
    spriteID: e58f4ab4a7ff64840b6743d155154135
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 399}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 4
      width: 58
      height: 62
    spriteID: 921d73dfb19dbb14398d9fdf9b446fcd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 229
      width: 42
      height: 84
    spriteID: 2167837b9825b9c4d93d776631b7423e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 229}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: -0.26881722}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 50
      height: 93
    spriteID: 14b9b7ca75c35e3408644a4ba03aaa5a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 4
      width: 54
      height: 67
    spriteID: 51637695c6eae33438dea44aa11023ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: -0.32500002}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 181
      width: 50
      height: 80
    spriteID: d7b4c24571f7ba043861c348462b8718
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 181}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 294
      width: 50
      height: 83
    spriteID: e1ca03ced181c6f44a6280815a732484
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 294}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 385
      width: 50
      height: 83
    spriteID: c90da24b5f321184d9f3fc3e0f9403a9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 385}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 79
      width: 54
      height: 67
    spriteID: 0b737bd0a2d95434d94bdaab8e6ecada
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 79}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: -0.28089887}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 105
      width: 50
      height: 89
    spriteID: 34996bf26e6fd574aa41e7f81beca7da
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 105}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: -0.2631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 269
      width: 42
      height: 95
    spriteID: 20e1ae7fedb1c334bac5a1500b7e7dd2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 269}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 74
      width: 58
      height: 62
    spriteID: 9a33c3139ddb9dd44825ce6ba1d5eeab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 74}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.51111114, y: -0.38157895}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 222
      width: 45
      height: 76
    spriteID: cbe5073089d28414c98244360c9a15ea
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 222}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 144
      width: 50
      height: 70
    spriteID: 6572b0c268a310c4f934ac0502b7d60d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 144}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 154
      width: 54
      height: 67
    spriteID: 93488d401b5c95647837216d0141e702
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 154}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 202
      width: 50
      height: 84
    spriteID: 77be929251d94694a99d290d0fc12765
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 202}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: -0.29069766}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 87
      width: 48
      height: 86
    spriteID: 231dc70a34c75e740ade5abea511807c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 87}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.50877196, y: -0.3866667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 4
      width: 57
      height: 75
    spriteID: 6f8f97eb71689354abe4ab1c18e035f3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 4}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 127
      y: 4
      width: 46
      height: 79
    spriteID: 4cf3ffdf21bc4fd4b9a987fe5ed184bf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 127, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 372
      width: 50
      height: 79
    spriteID: 69d18cde17355604ca0ab2563a2b12d5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 372}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1634596422
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Barrel_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 44fdb5f601f896d4c98e600920efa2ae
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: e58f4ab4a7ff64840b6743d155154135
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 921d73dfb19dbb14398d9fdf9b446fcd
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 84
      spriteId: 2167837b9825b9c4d93d776631b7423e
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 93
      spriteId: 14b9b7ca75c35e3408644a4ba03aaa5a
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 51637695c6eae33438dea44aa11023ed
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 80
      spriteId: d7b4c24571f7ba043861c348462b8718
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: e1ca03ced181c6f44a6280815a732484
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: c90da24b5f321184d9f3fc3e0f9403a9
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 0b737bd0a2d95434d94bdaab8e6ecada
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 89
      spriteId: 34996bf26e6fd574aa41e7f81beca7da
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 95
      spriteId: 20e1ae7fedb1c334bac5a1500b7e7dd2
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 9a33c3139ddb9dd44825ce6ba1d5eeab
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 45
        height: 76
      spriteId: cbe5073089d28414c98244360c9a15ea
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 6572b0c268a310c4f934ac0502b7d60d
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 93488d401b5c95647837216d0141e702
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 84
      spriteId: 77be929251d94694a99d290d0fc12765
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 25
        width: 48
        height: 86
      spriteId: 231dc70a34c75e740ade5abea511807c
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 57
        height: 75
      spriteId: 6f8f97eb71689354abe4ab1c18e035f3
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 46
        height: 79
      spriteId: 4cf3ffdf21bc4fd4b9a987fe5ed184bf
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 79
      spriteId: 69d18cde17355604ca0ab2563a2b12d5
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 512, y: 512}

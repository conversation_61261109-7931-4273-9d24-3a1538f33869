fileFormatVersion: 2
guid: 07b4fa<PERSON><PERSON><PERSON><PERSON>36409bee0646276f82f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6621879719761532180
    second: <PERSON><PERSON><PERSON>_Red_0
  - first:
      213: -5027493556857751391
    second: <PERSON><PERSON><PERSON>_Red_1
  - first:
      213: 1471423178526637504
    second: <PERSON>_<PERSON>_Red_2
  - first:
      213: 5381711356807848486
    second: <PERSON><PERSON><PERSON>_Red_3
  - first:
      213: 2018272972269428440
    second: <PERSON><PERSON><PERSON>_Red_4
  - first:
      213: 6705607281689757201
    second: <PERSON>_<PERSON>_Red_5
  - first:
      213: -8823537440155487856
    second: <PERSON>_<PERSON>_Red_6
  - first:
      213: -1736907535177212932
    second: <PERSON>_<PERSON>_Red_7
  - first:
      213: 4740330362687764126
    second: <PERSON>_<PERSON>_Red_8
  - first:
      213: -1226000696888810249
    second: <PERSON><PERSON><PERSON>_Red_9
  - first:
      213: -5039884926314722745
    second: <PERSON><PERSON><PERSON>_Red_10
  - first:
      213: -3281972243948475544
    second: <PERSON><PERSON><PERSON>_Red_11
  - first:
      213: 8478020320446159523
    second: <PERSON>_Bow_Red_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Bow_Red_0
      rect:
        serializedVersion: 2
        x: 68
        y: 259
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 415dfb95584a5eb50800000000000000
      internalID: 6621879719761532180
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_1
      rect:
        serializedVersion: 2
        x: 112
        y: 251
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a8bde03f31ca3ab0800000000000000
      internalID: -5027493556857751391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_2
      rect:
        serializedVersion: 2
        x: 46
        y: 62
        width: 48
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0cd3b5eb59b8b6410800000000000000
      internalID: 1471423178526637504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_3
      rect:
        serializedVersion: 2
        x: 96
        y: 63
        width: 20
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 622a73a27fdafaa40800000000000000
      internalID: 5381711356807848486
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_4
      rect:
        serializedVersion: 2
        x: 256
        y: 62
        width: 55
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8de14e33398520c10800000000000000
      internalID: 2018272972269428440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_5
      rect:
        serializedVersion: 2
        x: 475
        y: 62
        width: 67
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 116046c7b4a1f0d50800000000000000
      internalID: 6705607281689757201
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_6
      rect:
        serializedVersion: 2
        x: 641
        y: 59
        width: 74
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0913c21827f7c8580800000000000000
      internalID: -8823537440155487856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_7
      rect:
        serializedVersion: 2
        x: 828
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cfb101e9cc345e7e0800000000000000
      internalID: -1736907535177212932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_8
      rect:
        serializedVersion: 2
        x: 1020
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e96b00a795909c140800000000000000
      internalID: 4740330362687764126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_9
      rect:
        serializedVersion: 2
        x: 1202
        y: 73
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f4af8f9cde5cfee0800000000000000
      internalID: -1226000696888810249
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_10
      rect:
        serializedVersion: 2
        x: 1265
        y: 59
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74a4a2aac5bbe0ab0800000000000000
      internalID: -5039884926314722745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_11
      rect:
        serializedVersion: 2
        x: 1409
        y: 67
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86b62ccf1a71472d0800000000000000
      internalID: -3281972243948475544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Red_12
      rect:
        serializedVersion: 2
        x: 1454
        y: 57
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3aa9cbe109af7a570800000000000000
      internalID: 8478020320446159523
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Bow_Red_0: 6621879719761532180
      Archer_Bow_Red_1: -5027493556857751391
      Archer_Bow_Red_10: -5039884926314722745
      Archer_Bow_Red_11: -3281972243948475544
      Archer_Bow_Red_12: 8478020320446159523
      Archer_Bow_Red_2: 1471423178526637504
      Archer_Bow_Red_3: 5381711356807848486
      Archer_Bow_Red_4: 2018272972269428440
      Archer_Bow_Red_5: 6705607281689757201
      Archer_Bow_Red_6: -8823537440155487856
      Archer_Bow_Red_7: -1736907535177212932
      Archer_Bow_Red_8: 4740330362687764126
      Archer_Bow_Red_9: -1226000696888810249
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

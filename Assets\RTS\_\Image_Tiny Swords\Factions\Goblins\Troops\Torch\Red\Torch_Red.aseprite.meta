fileFormatVersion: 2
guid: 1e0f400a13d844a4590c0676ba1286fd
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5633803, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 420
      width: 71
      height: 97
    spriteID: 5079d86b59f5795478628443f9edc49f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.6233766, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 809
      width: 77
      height: 97
    spriteID: 2949d0e39e93e89439596b6f2270fcc3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 809}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.35999998, y: -0.6020408}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 914
      width: 75
      height: 98
    spriteID: 005ff0281bc55c349b0dca79455f05d6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 914}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5810811, y: -0.7468354}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 281
      width: 74
      height: 79
    spriteID: f2606758552241047958fccbb6532957
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 281}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.59999996, y: -0.71951216}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 889
      width: 75
      height: 82
    spriteID: b0825a3a3386bd84187cabcbbbbac81b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 889}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.59459454, y: -0.7108433}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 4
      width: 74
      height: 83
    spriteID: 0870785a0c79daf45b7485aff97ace7f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5833334, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 189
      width: 72
      height: 84
    spriteID: e012350f2b44af848a94febdfea6e9d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 189}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.57746476, y: -0.68604654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 95
      width: 71
      height: 86
    spriteID: 0143c35645b49a84ba3a2d3bd3750597
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 95}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.57746476, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 793
      width: 71
      height: 88
    spriteID: 053594de2d7a3cd4f8c81c5dbed9463e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 793}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5633803, y: -0.6629213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 696
      width: 71
      height: 89
    spriteID: e3a8eef83164fc5409e737893c9354a5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 696}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.7129629, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 85
      width: 108
      height: 73
    spriteID: addaad18b65edb84496c89165e42fcca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 85}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.6868687, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 725
      width: 99
      height: 76
    spriteID: 973032a665c7ea142a07955e46ef80bc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 725}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 166
      width: 105
      height: 73
    spriteID: 8e31f98d495edd34d88c9edd28c97315
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 166}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.6936937, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 4
      width: 111
      height: 73
    spriteID: 6a07c062f8e9aca4187b3ab1b0341b70
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 4}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.68, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 641
      width: 100
      height: 76
    spriteID: bd368fef2edaf1d4fb0b641e0b765cd0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 641}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 247
      width: 105
      height: 73
    spriteID: de21f4cf048baa749b47f4ee59bdb514
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 247}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.65, y: -0.6145833}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 537
      width: 80
      height: 96
    spriteID: 562686f0b9d6bf149b06cd3d7bec069f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 537}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.6627907, y: -0.57843137}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 229
      width: 86
      height: 102
    spriteID: 1b446a69b3687cd4596aac603d3d3da9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 229}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.67777777, y: -0.5412844}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 112
      width: 90
      height: 109
    spriteID: 94f5a324f5996a946b6a3250ef4d28e8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 112}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.4160584, y: -0.54716986}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 118
      width: 137
      height: 106
    spriteID: 98695b137a4da1b4ca2297360ca9a180
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 118}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.41481486, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 232
      width: 135
      height: 107
    spriteID: 739b37217f0d4e04fbd56cf4cfb78760
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 232}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.43089432, y: -0.5462963}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 585
      width: 123
      height: 108
    spriteID: 3960a041bd7b780458d51d01fda444bf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 585}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.6202531, y: -0.7375}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 608
      width: 79
      height: 80
    spriteID: 9fa28fea093833a4fbc453ad14e14d4b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 608}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.6395349, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 328
      width: 86
      height: 84
    spriteID: c5f0056d6a66fa842b260fa10d256a9b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 328}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.65168536, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 441
      width: 89
      height: 88
    spriteID: bfd519de494c50c4fb804b6272a52e2e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 441}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.50862074, y: -0.2566372}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 701
      width: 116
      height: 113
    spriteID: 61da06ceb9992554e954660a823be4fa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 701}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.48739493, y: -0.1965812}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 460
      width: 119
      height: 117
    spriteID: deb3fc5bead54064095f024445499653
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 460}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.39215687, y: -0.26999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 4
      width: 102
      height: 100
    spriteID: e3a0609067b8b744380efc3e8be23254
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 4}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.30769235, y: -0.78666663}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 525
      width: 91
      height: 75
    spriteID: a6c6ee88826d8154e85d3db6c6655dff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 525}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.32978725, y: -0.4835165}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 923
      width: 94
      height: 91
    spriteID: 49aa287be828ffd47b922f3aeb5c46ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 923}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.34065938, y: -0.43617022}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 339
      width: 91
      height: 94
    spriteID: 0de572cc9629c0847b9f90e6124ef63f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 339}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.5928571, y: -0.44339624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 140
      height: 106
    spriteID: e65afdd1965e6974f9c55de1eed41581
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.6058394, y: -0.4857143}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 347
      width: 137
      height: 105
    spriteID: 65fb719c9569a2a4fbbf718d8a954493
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 347}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.69827586, y: -0.63440853}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 822
      width: 116
      height: 93
    spriteID: 4285f3d44b5a57d4399680925aded0fa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 822}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 370124028
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Torch_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 97
      spriteId: 5079d86b59f5795478628443f9edc49f
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 59
        width: 77
        height: 97
      spriteId: 2949d0e39e93e89439596b6f2270fcc3
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 59
        width: 75
        height: 98
      spriteId: 005ff0281bc55c349b0dca79455f05d6
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 59
        width: 74
        height: 79
      spriteId: f2606758552241047958fccbb6532957
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 59
        width: 75
        height: 82
      spriteId: b0825a3a3386bd84187cabcbbbbac81b
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 52
        y: 59
        width: 74
        height: 83
      spriteId: 0870785a0c79daf45b7485aff97ace7f
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 59
        width: 72
        height: 84
      spriteId: e012350f2b44af848a94febdfea6e9d3
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 86
      spriteId: 0143c35645b49a84ba3a2d3bd3750597
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 88
      spriteId: 053594de2d7a3cd4f8c81c5dbed9463e
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 89
      spriteId: e3a8eef83164fc5409e737893c9354a5
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 108
        height: 73
      spriteId: addaad18b65edb84496c89165e42fcca
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 99
        height: 76
      spriteId: 973032a665c7ea142a07955e46ef80bc
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: 8e31f98d495edd34d88c9edd28c97315
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 111
        height: 73
      spriteId: 6a07c062f8e9aca4187b3ab1b0341b70
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 100
        height: 76
      spriteId: bd368fef2edaf1d4fb0b641e0b765cd0
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: de21f4cf048baa749b47f4ee59bdb514
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 44
        y: 59
        width: 80
        height: 96
      spriteId: 562686f0b9d6bf149b06cd3d7bec069f
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 59
        width: 86
        height: 102
      spriteId: 1b446a69b3687cd4596aac603d3d3da9
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 59
        width: 90
        height: 109
      spriteId: 94f5a324f5996a946b6a3250ef4d28e8
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 58
        width: 137
        height: 106
      spriteId: 98695b137a4da1b4ca2297360ca9a180
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 57
        width: 135
        height: 107
      spriteId: 739b37217f0d4e04fbd56cf4cfb78760
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 59
        width: 123
        height: 108
      spriteId: 3960a041bd7b780458d51d01fda444bf
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 47
        y: 59
        width: 79
        height: 80
      spriteId: 9fa28fea093833a4fbc453ad14e14d4b
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 59
        width: 86
        height: 84
      spriteId: c5f0056d6a66fa842b260fa10d256a9b
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 59
        width: 89
        height: 88
      spriteId: bfd519de494c50c4fb804b6272a52e2e
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 116
        height: 113
      spriteId: 61da06ceb9992554e954660a823be4fa
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 23
        width: 119
        height: 117
      spriteId: deb3fc5bead54064095f024445499653
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 27
        width: 102
        height: 100
      spriteId: e3a0609067b8b744380efc3e8be23254
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 59
        width: 91
        height: 75
      spriteId: a6c6ee88826d8154e85d3db6c6655dff
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 44
        width: 94
        height: 91
      spriteId: 49aa287be828ffd47b922f3aeb5c46ed
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 41
        width: 91
        height: 94
      spriteId: 0de572cc9629c0847b9f90e6124ef63f
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 47
        width: 140
        height: 106
      spriteId: e65afdd1965e6974f9c55de1eed41581
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 51
        width: 137
        height: 105
      spriteId: 65fb719c9569a2a4fbbf718d8a954493
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 15
        y: 59
        width: 116
        height: 93
      spriteId: 4285f3d44b5a57d4399680925aded0fa
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

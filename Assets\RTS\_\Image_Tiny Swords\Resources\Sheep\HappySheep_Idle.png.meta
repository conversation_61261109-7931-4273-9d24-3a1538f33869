fileFormatVersion: 2
guid: 9301bb3a2d9700f499a96cde1f2f2923
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -6367260613296689715
    second: HappySheep_Idle_0
  - first:
      213: -8626619091392344333
    second: HappySheep_Idle_1
  - first:
      213: 3723718632635307859
    second: HappySheep_Idle_2
  - first:
      213: -352071954382587109
    second: HappySheep_Idle_3
  - first:
      213: -2625141565471614998
    second: HappySheep_Idle_4
  - first:
      213: 6036646026520181864
    second: HappySheep_Idle_5
  - first:
      213: -8912526117797851183
    second: HappySheep_Idle_6
  - first:
      213: 3473659580424667232
    second: HappySheep_Idle_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: HappySheep_Idle_0
      rect:
        serializedVersion: 2
        x: 42
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcd6d181532f2a7a0800000000000000
      internalID: -6367260613296689715
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_1
      rect:
        serializedVersion: 2
        x: 170
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fec4f464a7184880800000000000000
      internalID: -8626619091392344333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_2
      rect:
        serializedVersion: 2
        x: 298
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3571bb50e5e4da330800000000000000
      internalID: 3723718632635307859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_3
      rect:
        serializedVersion: 2
        x: 426
        y: 41
        width: 45
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1bf5af3c603d1bf0800000000000000
      internalID: -352071954382587109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_4
      rect:
        serializedVersion: 2
        x: 553
        y: 41
        width: 48
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae793f0a7af919bd0800000000000000
      internalID: -2625141565471614998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_5
      rect:
        serializedVersion: 2
        x: 681
        y: 41
        width: 48
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86c328dd39976c350800000000000000
      internalID: 6036646026520181864
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_6
      rect:
        serializedVersion: 2
        x: 810
        y: 41
        width: 46
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1df844967b8505480800000000000000
      internalID: -8912526117797851183
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Idle_7
      rect:
        serializedVersion: 2
        x: 938
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06048d1fbfae43030800000000000000
      internalID: 3473659580424667232
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      HappySheep_Idle_0: -6367260613296689715
      HappySheep_Idle_1: -8626619091392344333
      HappySheep_Idle_2: 3723718632635307859
      HappySheep_Idle_3: -352071954382587109
      HappySheep_Idle_4: -2625141565471614998
      HappySheep_Idle_5: 6036646026520181864
      HappySheep_Idle_6: -8912526117797851183
      HappySheep_Idle_7: 3473659580424667232
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

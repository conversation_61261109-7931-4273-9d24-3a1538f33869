fileFormatVersion: 2
guid: badd633dcae1b8a43a066bbb004e3aff
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 295
      width: 58
      height: 59
    spriteID: 4a6a6ebdbd2f92a47beebce2b5ffae56
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 295}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 295
      width: 58
      height: 59
    spriteID: 8a1dc9a1a3f3e584ca503037168eface
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 295}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5172414, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 363
      width: 58
      height: 57
    spriteID: 696b51f19bdab3749a4fbeddf7068f1c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 363}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5172414, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 363
      width: 58
      height: 56
    spriteID: f38be991db53ef0418c71c8519556b01
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 363}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 363
      width: 58
      height: 57
    spriteID: 17356d1afa88fce49ba82855dde070e7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 363}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 363
      width: 58
      height: 58
    spriteID: 43a92c34b691ed64b86c2bad1b89b62d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 363}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 295
      width: 58
      height: 59
    spriteID: 1043ef8bfa8c13741b694929d68dbd14
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 295}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 141
      y: 227
      width: 58
      height: 60
    spriteID: 4340a3dcd6d054a4dbee15e698024d5f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 141, y: 227}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 295
      width: 58
      height: 59
    spriteID: 7e33f9c4529b7024080788b43db06222
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 295}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 78
      width: 58
      height: 55
    spriteID: a44737d7a794c8340a68c4b8511ff489
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 78}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48387095, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 227
      width: 62
      height: 60
    spriteID: 604b6c5e01848bd43a2fbadac97b0cc0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 227}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.49152538, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 227
      width: 59
      height: 59
    spriteID: 70f6ca70aab6bac438e7912a19c3fe88
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 227}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 309
      y: 78
      width: 58
      height: 55
    spriteID: 90f6b29ee638bf5419d76c4f4a251399
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 309, y: 78}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.49230766, y: -0.85333335}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 150
      y: 142
      width: 65
      height: 75
    spriteID: 91a1b0e36f6ee714da2cec7481a3c27b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 150, y: 142}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 142
      width: 65
      height: 77
    spriteID: 4e5332cdcbdc4e1449b95a53a168c59c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 142}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 77
      y: 142
      width: 65
      height: 77
    spriteID: f5b111675fda58f4ba1ae3a9fb63789d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 77, y: 142}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.34782606, y: -0.969697}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 92
      height: 66
    spriteID: f6f1668df92113b4dbbd93fb6fb3406f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.3483146, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 414
      y: 4
      width: 89
      height: 56
    spriteID: ee46d7597a6b92a4bb0b073e9c318ba7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 414, y: 4}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.3421052, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 389
      y: 142
      width: 76
      height: 58
    spriteID: 42bda06edbe140d4fa2bbc4f30b2832f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 389, y: 142}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.654321, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 223
      y: 142
      width: 81
      height: 60
    spriteID: 9d90481afc5f51c47a975870ee20afee
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 223, y: 142}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 222
      y: 4
      width: 88
      height: 64
    spriteID: ff1c30499a7299445994887fe5c578ec
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 222, y: 4}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 318
      y: 4
      width: 88
      height: 64
    spriteID: a11621e1a6d2bf5499ac1d0f2c9ec4b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 318, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.3909091, y: -1.1851852}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 104
      y: 4
      width: 110
      height: 54
    spriteID: 3053ad1cdac737b46b7b8523610ddbd3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 104, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33333334, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 78
      width: 99
      height: 56
    spriteID: 08665423b091dda4097cb59cfed0e0de
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 78}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.53623194, y: -0.8484849}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 312
      y: 142
      width: 69
      height: 66
    spriteID: 5a7ad157fe246b24eba249bee019c20f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 312, y: 142}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.49999997, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 207
      y: 227
      width: 58
      height: 60
    spriteID: 480a56c9901f1ae4f8a12e7a5b438a25
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 207, y: 227}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 295
      width: 58
      height: 59
    spriteID: 046c19f251b27174b973c201329ff9fd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 295}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.5172414, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 363
      width: 58
      height: 58
    spriteID: 3b03c20f450f0c54ea409af96d3672c0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 363}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 363
      width: 58
      height: 57
    spriteID: 2958a0d741d8f55458f90f9cd81221bd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 363}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 363
      width: 58
      height: 58
    spriteID: 30086824923b03240ab24f7ec5e71ed6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 363}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 295
      width: 58
      height: 59
    spriteID: c6f1e88f8c7f72b4987a58daaad09ab7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 295}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 227
      width: 58
      height: 60
    spriteID: 600ed6f89a1e64a40aff570feadc1184
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 227}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.5172414, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 339
      y: 227
      width: 58
      height: 60
    spriteID: 7611f8451f301094a9964536559b7797
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 339, y: 227}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 78
      width: 58
      height: 56
    spriteID: 01912e07bf00af94a932a2db7bf26ee8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 78}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.46551725, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 405
      y: 227
      width: 58
      height: 60
    spriteID: ea6b2f404a3da0b4cb532e8a805fda8f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 405, y: 227}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.48275867, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 295
      width: 58
      height: 60
    spriteID: 156f0931f3803e343921ffb6e940f61e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 295}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 177
      y: 78
      width: 58
      height: 56
    spriteID: 63ff3fa844fdc404f81952e7318d7b14
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 177, y: 78}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2453150660
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Pawn_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: 4a6a6ebdbd2f92a47beebce2b5ffae56
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: 8a1dc9a1a3f3e584ca503037168eface
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 57
      spriteId: 696b51f19bdab3749a4fbeddf7068f1c
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 56
      spriteId: f38be991db53ef0418c71c8519556b01
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: 17356d1afa88fce49ba82855dde070e7
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 43a92c34b691ed64b86c2bad1b89b62d
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: 1043ef8bfa8c13741b694929d68dbd14
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 4340a3dcd6d054a4dbee15e698024d5f
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: 7e33f9c4529b7024080788b43db06222
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: a44737d7a794c8340a68c4b8511ff489
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 62
        height: 60
      spriteId: 604b6c5e01848bd43a2fbadac97b0cc0
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 59
        height: 59
      spriteId: 70f6ca70aab6bac438e7912a19c3fe88
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: 90f6b29ee638bf5419d76c4f4a251399
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 65
        height: 75
      spriteId: 91a1b0e36f6ee714da2cec7481a3c27b
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 4e5332cdcbdc4e1449b95a53a168c59c
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: f5b111675fda58f4ba1ae3a9fb63789d
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 92
        height: 66
      spriteId: f6f1668df92113b4dbbd93fb6fb3406f
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 89
        height: 56
      spriteId: ee46d7597a6b92a4bb0b073e9c318ba7
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 64
        width: 76
        height: 58
      spriteId: 42bda06edbe140d4fa2bbc4f30b2832f
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 64
        width: 81
        height: 60
      spriteId: 9d90481afc5f51c47a975870ee20afee
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: ff1c30499a7299445994887fe5c578ec
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: a11621e1a6d2bf5499ac1d0f2c9ec4b7
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 64
        width: 110
        height: 54
      spriteId: 3053ad1cdac737b46b7b8523610ddbd3
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 99
        height: 56
      spriteId: 08665423b091dda4097cb59cfed0e0de
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 56
        width: 69
        height: 66
      spriteId: 5a7ad157fe246b24eba249bee019c20f
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 60
      spriteId: 480a56c9901f1ae4f8a12e7a5b438a25
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: 046c19f251b27174b973c201329ff9fd
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 58
      spriteId: 3b03c20f450f0c54ea409af96d3672c0
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: 2958a0d741d8f55458f90f9cd81221bd
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 30086824923b03240ab24f7ec5e71ed6
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: c6f1e88f8c7f72b4987a58daaad09ab7
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 600ed6f89a1e64a40aff570feadc1184
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 60
      spriteId: 7611f8451f301094a9964536559b7797
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 01912e07bf00af94a932a2db7bf26ee8
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 64
        width: 58
        height: 60
      spriteId: ea6b2f404a3da0b4cb532e8a805fda8f
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 60
      spriteId: 156f0931f3803e343921ffb6e940f61e
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 63ff3fa844fdc404f81952e7318d7b14
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

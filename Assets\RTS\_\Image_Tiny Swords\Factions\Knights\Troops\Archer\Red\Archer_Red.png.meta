fileFormatVersion: 2
guid: c4587dd72564f1044b8bcda9d5b2c7c3
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4400776375116481744
    second: <PERSON><PERSON><PERSON>_0
  - first:
      213: 1136124461552903849
    second: <PERSON>_<PERSON>_1
  - first:
      213: 126929281214319637
    second: <PERSON>_Red_2
  - first:
      213: -622083725890771740
    second: <PERSON>_Red_3
  - first:
      213: -4222610557812350838
    second: <PERSON>_<PERSON>_4
  - first:
      213: -8573953027354753612
    second: <PERSON>_Red_5
  - first:
      213: 7730236880828550387
    second: <PERSON>_Red_6
  - first:
      213: 7384206803078448407
    second: <PERSON>_Red_7
  - first:
      213: -644584665483635374
    second: <PERSON>_Red_8
  - first:
      213: 6972283758920586944
    second: <PERSON>_<PERSON>_9
  - first:
      213: -3055530344466322971
    second: <PERSON>_<PERSON>_10
  - first:
      213: -3316005115636549241
    second: <PERSON><PERSON><PERSON>_11
  - first:
      213: 3726790606949586149
    second: <PERSON>_<PERSON>_12
  - first:
      213: -3648586840031123400
    second: Archer_Red_13
  - first:
      213: 3453891309040877054
    second: Archer_Red_14
  - first:
      213: 3579480904467970907
    second: Archer_Red_15
  - first:
      213: 7849552762536989564
    second: Archer_Red_16
  - first:
      213: -6407178080133641867
    second: Archer_Red_17
  - first:
      213: -1524081414278569139
    second: Archer_Red_18
  - first:
      213: -4720607019404386898
    second: Archer_Red_19
  - first:
      213: -3021743667001321343
    second: Archer_Red_20
  - first:
      213: -2997593059316322164
    second: Archer_Red_21
  - first:
      213: -7493622089575495089
    second: Archer_Red_22
  - first:
      213: -6943619996103097623
    second: Archer_Red_23
  - first:
      213: -1623983504614675475
    second: Archer_Red_24
  - first:
      213: -4523136034371963086
    second: Archer_Red_25
  - first:
      213: 4926891450617677041
    second: Archer_Red_26
  - first:
      213: 4923307170496784893
    second: Archer_Red_27
  - first:
      213: 2380772313794947860
    second: Archer_Red_28
  - first:
      213: 5916182030208804323
    second: Archer_Red_29
  - first:
      213: 2561738574722122756
    second: Archer_Red_30
  - first:
      213: -485255802015202031
    second: Archer_Red_31
  - first:
      213: 4561154574868123772
    second: Archer_Red_32
  - first:
      213: 166914430076898261
    second: Archer_Red_33
  - first:
      213: 3688121629015854348
    second: Archer_Red_34
  - first:
      213: 2946825324931628206
    second: Archer_Red_35
  - first:
      213: 1246524044592915709
    second: Archer_Red_36
  - first:
      213: 2807082372412967066
    second: Archer_Red_37
  - first:
      213: 7710375500158145116
    second: Archer_Red_38
  - first:
      213: 2041368906820227279
    second: Archer_Red_39
  - first:
      213: 181715782970787894
    second: Archer_Red_40
  - first:
      213: -4117689316079300187
    second: Archer_Red_41
  - first:
      213: 4412643741660377778
    second: Archer_Red_42
  - first:
      213: 8661717971770838617
    second: Archer_Red_43
  - first:
      213: 163993933469049498
    second: Archer_Red_44
  - first:
      213: 2333173364470197370
    second: Archer_Red_45
  - first:
      213: 1147196846029993638
    second: Archer_Red_46
  - first:
      213: 8017050108030293877
    second: Archer_Red_47
  - first:
      213: -623652901369292717
    second: Archer_Red_48
  - first:
      213: -2223024008222796978
    second: Archer_Red_49
  - first:
      213: 6162025479534200164
    second: Archer_Red_50
  - first:
      213: 4885339166613706275
    second: Archer_Red_51
  - first:
      213: 38731427506134349
    second: Archer_Red_52
  - first:
      213: 4861346512092301724
    second: Archer_Red_53
  - first:
      213: -5598994424892066932
    second: Archer_Red_54
  - first:
      213: -637483423264735770
    second: Archer_Red_55
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Red_0
      rect:
        serializedVersion: 2
        x: 67
        y: 1209
        width: 64
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03b3041043d4de2c0800000000000000
      internalID: -4400776375116481744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_1
      rect:
        serializedVersion: 2
        x: 259
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a62e77ad2354cf00800000000000000
      internalID: 1136124461552903849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_2
      rect:
        serializedVersion: 2
        x: 451
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51c006e3281f2c100800000000000000
      internalID: 126929281214319637
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_3
      rect:
        serializedVersion: 2
        x: 641
        y: 1209
        width: 69
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e4aab1b52aed57f0800000000000000
      internalID: -622083725890771740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_4
      rect:
        serializedVersion: 2
        x: 833
        y: 1209
        width: 67
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a80e7cce1164665c0800000000000000
      internalID: -4222610557812350838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_5
      rect:
        serializedVersion: 2
        x: 1026
        y: 1209
        width: 64
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b582abf523330980800000000000000
      internalID: -8573953027354753612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_6
      rect:
        serializedVersion: 2
        x: 65
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fcc86f8c71574b60800000000000000
      internalID: 7730236880828550387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_7
      rect:
        serializedVersion: 2
        x: 258
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7158385d6f8f97660800000000000000
      internalID: 7384206803078448407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_8
      rect:
        serializedVersion: 2
        x: 451
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2551f2279a9fd07f0800000000000000
      internalID: -644584665483635374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_9
      rect:
        serializedVersion: 2
        x: 636
        y: 1035
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c6dfa3532782c060800000000000000
      internalID: 6972283758920586944
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_10
      rect:
        serializedVersion: 2
        x: 641
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e968a137539895d0800000000000000
      internalID: -3055530344466322971
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_11
      rect:
        serializedVersion: 2
        x: 830
        y: 1030
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 789fa119aee2bf1d0800000000000000
      internalID: -3316005115636549241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_12
      rect:
        serializedVersion: 2
        x: 834
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e41e77cf4838b330800000000000000
      internalID: 3726790606949586149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_13
      rect:
        serializedVersion: 2
        x: 1027
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 838a7cb999d9d5dc0800000000000000
      internalID: -3648586840031123400
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_14
      rect:
        serializedVersion: 2
        x: 66
        y: 825
        width: 67
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: efdb03898dfaeef20800000000000000
      internalID: 3453891309040877054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_15
      rect:
        serializedVersion: 2
        x: 258
        y: 825
        width: 77
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5b7b1feaeedca130800000000000000
      internalID: 3579480904467970907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_16
      rect:
        serializedVersion: 2
        x: 450
        y: 825
        width: 64
        height: 116
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7314b816a63fec60800000000000000
      internalID: 7849552762536989564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_17
      rect:
        serializedVersion: 2
        x: 636
        y: 825
        width: 68
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57907f89b712517a0800000000000000
      internalID: -6407178080133641867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_18
      rect:
        serializedVersion: 2
        x: 828
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d43a625570069dae0800000000000000
      internalID: -1524081414278569139
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_19
      rect:
        serializedVersion: 2
        x: 1020
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ead9f2c3de80d7eb0800000000000000
      internalID: -4720607019404386898
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_20
      rect:
        serializedVersion: 2
        x: 1210
        y: 825
        width: 62
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 188991e052c9016d0800000000000000
      internalID: -3021743667001321343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_21
      rect:
        serializedVersion: 2
        x: 1238
        y: 891
        width: 43
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8036469ef86666d0800000000000000
      internalID: -2997593059316322164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_22
      rect:
        serializedVersion: 2
        x: 1410
        y: 825
        width: 57
        height: 71
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4a05d5996e410890800000000000000
      internalID: -7493622089575495089
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_23
      rect:
        serializedVersion: 2
        x: 1439
        y: 875
        width: 35
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e2c9f4076e43af90800000000000000
      internalID: -6943619996103097623
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_24
      rect:
        serializedVersion: 2
        x: 55
        y: 633
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: deb29565b937679e0800000000000000
      internalID: -1623983504614675475
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_25
      rect:
        serializedVersion: 2
        x: 256
        y: 633
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2377d78cdb79a31c0800000000000000
      internalID: -4523136034371963086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_26
      rect:
        serializedVersion: 2
        x: 448
        y: 633
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f4c9f8aba5df5440800000000000000
      internalID: 4926891450617677041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_27
      rect:
        serializedVersion: 2
        x: 634
        y: 633
        width: 74
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df9289969c9135440800000000000000
      internalID: 4923307170496784893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_28
      rect:
        serializedVersion: 2
        x: 826
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41b25d63ec33a0120800000000000000
      internalID: 2380772313794947860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_29
      rect:
        serializedVersion: 2
        x: 1018
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e5252116308a1250800000000000000
      internalID: 5916182030208804323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_30
      rect:
        serializedVersion: 2
        x: 1207
        y: 633
        width: 80
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4044ab939af1d8320800000000000000
      internalID: 2561738574722122756
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_31
      rect:
        serializedVersion: 2
        x: 1408
        y: 633
        width: 68
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 115cfa74b660449f0800000000000000
      internalID: -485255802015202031
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_32
      rect:
        serializedVersion: 2
        x: 55
        y: 441
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7099a87be97c4f30800000000000000
      internalID: 4561154574868123772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_33
      rect:
        serializedVersion: 2
        x: 256
        y: 441
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5dbce903acff05200800000000000000
      internalID: 166914430076898261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_34
      rect:
        serializedVersion: 2
        x: 448
        y: 441
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0dcce1c417de2330800000000000000
      internalID: 3688121629015854348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_35
      rect:
        serializedVersion: 2
        x: 634
        y: 441
        width: 81
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eacbca3250a35e820800000000000000
      internalID: 2946825324931628206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_36
      rect:
        serializedVersion: 2
        x: 826
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df4a1f1a30b8c4110800000000000000
      internalID: 1246524044592915709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_37
      rect:
        serializedVersion: 2
        x: 1018
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a988fee8b82c4f620800000000000000
      internalID: 2807082372412967066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_38
      rect:
        serializedVersion: 2
        x: 1202
        y: 441
        width: 82
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c521dc38aa1c00b60800000000000000
      internalID: 7710375500158145116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_39
      rect:
        serializedVersion: 2
        x: 1408
        y: 441
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc01c828436645c10800000000000000
      internalID: 2041368906820227279
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_40
      rect:
        serializedVersion: 2
        x: 55
        y: 249
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6384e06ba85958200800000000000000
      internalID: 181715782970787894
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_41
      rect:
        serializedVersion: 2
        x: 256
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a11cab22670bd6c0800000000000000
      internalID: -4117689316079300187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_42
      rect:
        serializedVersion: 2
        x: 448
        y: 249
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b274069a1cdc3d30800000000000000
      internalID: 4412643741660377778
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_43
      rect:
        serializedVersion: 2
        x: 639
        y: 242
        width: 63
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95e84048a9a943870800000000000000
      internalID: 8661717971770838617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_44
      rect:
        serializedVersion: 2
        x: 827
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a92edb51d9f964200800000000000000
      internalID: 163993933469049498
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_45
      rect:
        serializedVersion: 2
        x: 1019
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7c26a430d8116020800000000000000
      internalID: 2333173364470197370
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_46
      rect:
        serializedVersion: 2
        x: 1205
        y: 239
        width: 76
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a6f01f0479abef00800000000000000
      internalID: 1147196846029993638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_47
      rect:
        serializedVersion: 2
        x: 1402
        y: 243
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57717abd898424f60800000000000000
      internalID: 8017050108030293877
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_48
      rect:
        serializedVersion: 2
        x: 55
        y: 57
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 358ddbe8df65857f0800000000000000
      internalID: -623652901369292717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_49
      rect:
        serializedVersion: 2
        x: 256
        y: 57
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4317be417b3621e0800000000000000
      internalID: -2223024008222796978
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_50
      rect:
        serializedVersion: 2
        x: 448
        y: 54
        width: 86
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 465ca8c9689e38550800000000000000
      internalID: 6162025479534200164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_51
      rect:
        serializedVersion: 2
        x: 639
        y: 42
        width: 57
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3261051a6163cc340800000000000000
      internalID: 4885339166613706275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_52
      rect:
        serializedVersion: 2
        x: 831
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d4d9522370a998000800000000000000
      internalID: 38731427506134349
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_53
      rect:
        serializedVersion: 2
        x: 1023
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c99885f65e8f67340800000000000000
      internalID: 4861346512092301724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_54
      rect:
        serializedVersion: 2
        x: 1212
        y: 47
        width: 61
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c837aec3c306c42b0800000000000000
      internalID: -5598994424892066932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Red_55
      rect:
        serializedVersion: 2
        x: 1402
        y: 51
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6edfa1e34343727f0800000000000000
      internalID: -637483423264735770
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Red_0: -4400776375116481744
      Archer_Red_1: 1136124461552903849
      Archer_Red_10: -3055530344466322971
      Archer_Red_11: -3316005115636549241
      Archer_Red_12: 3726790606949586149
      Archer_Red_13: -3648586840031123400
      Archer_Red_14: 3453891309040877054
      Archer_Red_15: 3579480904467970907
      Archer_Red_16: 7849552762536989564
      Archer_Red_17: -6407178080133641867
      Archer_Red_18: -1524081414278569139
      Archer_Red_19: -4720607019404386898
      Archer_Red_2: 126929281214319637
      Archer_Red_20: -3021743667001321343
      Archer_Red_21: -2997593059316322164
      Archer_Red_22: -7493622089575495089
      Archer_Red_23: -6943619996103097623
      Archer_Red_24: -1623983504614675475
      Archer_Red_25: -4523136034371963086
      Archer_Red_26: 4926891450617677041
      Archer_Red_27: 4923307170496784893
      Archer_Red_28: 2380772313794947860
      Archer_Red_29: 5916182030208804323
      Archer_Red_3: -622083725890771740
      Archer_Red_30: 2561738574722122756
      Archer_Red_31: -485255802015202031
      Archer_Red_32: 4561154574868123772
      Archer_Red_33: 166914430076898261
      Archer_Red_34: 3688121629015854348
      Archer_Red_35: 2946825324931628206
      Archer_Red_36: 1246524044592915709
      Archer_Red_37: 2807082372412967066
      Archer_Red_38: 7710375500158145116
      Archer_Red_39: 2041368906820227279
      Archer_Red_4: -4222610557812350838
      Archer_Red_40: 181715782970787894
      Archer_Red_41: -4117689316079300187
      Archer_Red_42: 4412643741660377778
      Archer_Red_43: 8661717971770838617
      Archer_Red_44: 163993933469049498
      Archer_Red_45: 2333173364470197370
      Archer_Red_46: 1147196846029993638
      Archer_Red_47: 8017050108030293877
      Archer_Red_48: -623652901369292717
      Archer_Red_49: -2223024008222796978
      Archer_Red_5: -8573953027354753612
      Archer_Red_50: 6162025479534200164
      Archer_Red_51: 4885339166613706275
      Archer_Red_52: 38731427506134349
      Archer_Red_53: 4861346512092301724
      Archer_Red_54: -5598994424892066932
      Archer_Red_55: -637483423264735770
      Archer_Red_6: 7730236880828550387
      Archer_Red_7: 7384206803078448407
      Archer_Red_8: -644584665483635374
      Archer_Red_9: 6972283758920586944
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

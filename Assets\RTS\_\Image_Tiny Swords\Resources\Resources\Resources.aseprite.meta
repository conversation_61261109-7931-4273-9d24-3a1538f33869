fileFormatVersion: 2
guid: 3787304558cf6ef4e84f83f044b6480b
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5102041, y: -0.86666673}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 303
      y: 136
      width: 49
      height: 30
    spriteID: 2c96683968059564fb94cb2b820a5efe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 303, y: 136}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.48214287, y: -0.453125}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 208
      y: 4
      width: 56
      height: 64
    spriteID: 73765e4218999a94cb5ace3d31185b19
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 208, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.48333335, y: -0.43283582}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 60
      height: 67
    spriteID: 6dfec5d1c3536204b9f6c40b32b9c49f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.44, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 79
      width: 50
      height: 49
    spriteID: 0ad3bd4a53250a440998965845e16a85
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 79}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.50877196, y: -0.80645156}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 186
      y: 136
      width: 57
      height: 31
    spriteID: 07cbf138303675c41bb0048c681a5a95
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 186, y: 136}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: -0.7222222}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 251
      y: 136
      width: 44
      height: 36
    spriteID: 05a4fd8a62233c642aa0ba074950b688
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 251, y: 136}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5102041, y: -0.86666673}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 360
      y: 136
      width: 49
      height: 30
    spriteID: f9b15d587f16dbf46adc471ed6ff12ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 360, y: 136}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.37209302, y: -0.6041667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 180
      y: 79
      width: 43
      height: 48
    spriteID: 34956cb25e6c01c4ab932b2230fc9f5e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 180, y: 79}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48214287, y: -0.453125}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 272
      y: 4
      width: 56
      height: 64
    spriteID: c6c8c9eb8c72d974f8c560a14aeb8c22
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 272, y: 4}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.48333335, y: -0.43283582}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 72
      y: 4
      width: 60
      height: 67
    spriteID: cbf8417557605f04b8e0b58b85c44f41
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 72, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.40909094, y: -0.48333335}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 460
      y: 4
      width: 44
      height: 60
    spriteID: e7f363fea742550418b835aa409368f7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 460, y: 4}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.44827586, y: -0.8787879}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 49
      y: 136
      width: 58
      height: 33
    spriteID: 73239a2010a38f442ba07db5733341e4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 49, y: 136}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.4054054, y: -0.5471698}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 136
      width: 37
      height: 53
    spriteID: 35f69d492a4c8764891bb30c597cb807
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 136}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.37209302, y: -0.6041667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 231
      y: 79
      width: 43
      height: 48
    spriteID: 7470126a66e46c64eb2e17e92d86e716
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 231, y: 79}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.43137255, y: -0.6829268}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 79
      width: 51
      height: 41
    spriteID: e187b092e2b59d243bad0a55059f0e4f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 79}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.48214287, y: -0.453125}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 336
      y: 4
      width: 56
      height: 64
    spriteID: f4ce00d7dd8c91449865646b587c3b6a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 336, y: 4}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.48333335, y: -0.43283582}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 140
      y: 4
      width: 60
      height: 67
    spriteID: f4dea119d91dc6147a4e7ed87834d9cb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 140, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.4615385, y: -0.537037}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 4
      width: 52
      height: 54
    spriteID: c4e0fb3d18be4df4a9544d180989676d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 4}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.44444448, y: -1.0344827}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 115
      y: 136
      width: 63
      height: 29
    spriteID: 5513b2875bb553d4393e4c5e56214c5a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 115, y: 136}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.41860464, y: -0.64444447}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 438
      y: 79
      width: 43
      height: 45
    spriteID: 068e5dc052bf152428be0ee7b919aa77
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 438, y: 79}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.43137255, y: -0.6829268}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 121
      y: 79
      width: 51
      height: 41
    spriteID: 73afc14d13883b64495680de633b7ae1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 121, y: 79}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.47727275, y: -0.97727275}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 282
      y: 79
      width: 44
      height: 44
    spriteID: 833abe0d967cc5947a8592eede503b97
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 282, y: 79}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.47727275, y: -0.97727275}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 79
      width: 44
      height: 44
    spriteID: 7eca58e852c85ab4a9027bafd6f8e06b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 79}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.47727275, y: -0.97727275}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 386
      y: 79
      width: 44
      height: 44
    spriteID: a834d49f89a048b4581d3a69f0daa98a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 386, y: 79}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 191819257
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Resources
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 49
        height: 30
      spriteId: 2c96683968059564fb94cb2b820a5efe
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 56
        height: 64
      spriteId: 73765e4218999a94cb5ace3d31185b19
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 60
        height: 67
      spriteId: 6dfec5d1c3536204b9f6c40b32b9c49f
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 29
        width: 50
        height: 49
      spriteId: 0ad3bd4a53250a440998965845e16a85
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 25
        width: 57
        height: 31
      spriteId: 07cbf138303675c41bb0048c681a5a95
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 26
        width: 44
        height: 36
      spriteId: 05a4fd8a62233c642aa0ba074950b688
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 49
        height: 30
      spriteId: f9b15d587f16dbf46adc471ed6ff12ca
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 29
        width: 43
        height: 48
      spriteId: 34956cb25e6c01c4ab932b2230fc9f5e
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 56
        height: 64
      spriteId: c6c8c9eb8c72d974f8c560a14aeb8c22
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 60
        height: 67
      spriteId: cbf8417557605f04b8e0b58b85c44f41
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 29
        width: 44
        height: 60
      spriteId: e7f363fea742550418b835aa409368f7
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 29
        width: 58
        height: 33
      spriteId: 73239a2010a38f442ba07db5733341e4
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 49
        y: 29
        width: 37
        height: 53
      spriteId: 35f69d492a4c8764891bb30c597cb807
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 29
        width: 43
        height: 48
      spriteId: 7470126a66e46c64eb2e17e92d86e716
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 28
        width: 51
        height: 41
      spriteId: e187b092e2b59d243bad0a55059f0e4f
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 56
        height: 64
      spriteId: f4ce00d7dd8c91449865646b587c3b6a
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 60
        height: 67
      spriteId: f4dea119d91dc6147a4e7ed87834d9cb
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 29
        width: 52
        height: 54
      spriteId: c4e0fb3d18be4df4a9544d180989676d
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 36
        y: 30
        width: 63
        height: 29
      spriteId: 5513b2875bb553d4393e4c5e56214c5a
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 29
        width: 43
        height: 45
      spriteId: 068e5dc052bf152428be0ee7b919aa77
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 28
        width: 51
        height: 41
      spriteId: 73afc14d13883b64495680de633b7ae1
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 43
        width: 44
        height: 44
      spriteId: 833abe0d967cc5947a8592eede503b97
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 43
        width: 44
        height: 44
      spriteId: 7eca58e852c85ab4a9027bafd6f8e06b
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 43
        width: 44
        height: 44
      spriteId: a834d49f89a048b4581d3a69f0daa98a
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 512, y: 256}

fileFormatVersion: 2
guid: 0b50e83f99e4f9644a75971fdb379135
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 420
      width: 63
      height: 75
    spriteID: 1427eb0f56d316944a8a7723f5a2ed46
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.45161292, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 752
      width: 62
      height: 75
    spriteID: 5e23df45e8638314098de5302091959f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 752}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 503
      width: 63
      height: 75
    spriteID: 1e54660af2bd67b40bccc8ca1bdbcbb0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 503}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 586
      width: 63
      height: 75
    spriteID: 42d6786780d696441bcf111ef31cbe16
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 586}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.44776118, y: -0.7837838}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 568
      width: 67
      height: 74
    spriteID: e2589f91b050079478ddc7bb85d2d8b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 568}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.46153846, y: -0.81690145}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 835
      width: 65
      height: 71
    spriteID: d094c532bcb887d4c8173d3dcf03df39
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 835}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.46774188, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 914
      width: 62
      height: 73
    spriteID: ef9c660432fc4a64e84f036dc88eec4c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 914}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.44776118, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 483
      width: 67
      height: 77
    spriteID: bb64a381ee9dc994bbb38fcd0557096c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 483}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.44615382, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 733
      width: 65
      height: 76
    spriteID: 1c7dc831eed141442b3d4405ed9c0aa2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 733}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 126
      width: 62
      height: 70
    spriteID: 1913da8292c29834bac3fab0b7b9c220
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 126}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.4861111, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 510
      width: 72
      height: 77
    spriteID: d71a9f5f446364140a28dc8a3628c4ff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 510}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47826087, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 4
      width: 69
      height: 76
    spriteID: 7f55095e809808d46b9969ece8dc95da
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 62
      height: 70
    spriteID: f2174f3d3328b9340b1bc227ed2ab9d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.44615382, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 293
      width: 65
      height: 80
    spriteID: fe0c30c92589d7c418e19fdc6191ea0a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 293}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.38666663, y: -0.70731705}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 644
      width: 75
      height: 82
    spriteID: 2c27727970ad6964eb3c0bb3a3062284
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 644}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.46774188, y: -0.50877196}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 62
      height: 114
    spriteID: 87dad9052a07ea740b2570449affb5ec
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.530303, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 538
      width: 66
      height: 98
    spriteID: 7edd6987270ac274c97c074e1a591fbd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 538}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 734
      width: 66
      height: 92
    spriteID: ef49404aa83fd6b40b095764b577007f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 734}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 834
      width: 66
      height: 92
    spriteID: f8c35e433beacd84b95758ed4f82e186
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 834}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.53623194, y: -0.6170213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 436
      width: 69
      height: 94
    spriteID: a587130b78c7ffa429f7629c95696d58
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 436}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.46774188, y: -0.65909094}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 595
      width: 62
      height: 88
    spriteID: d8bae4472aa9e2745bb2d69d8d63ea3c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 595}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 88
      width: 64
      height: 75
    spriteID: d29c53b1a597ab7458b404cd42933262
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 88}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 282
      width: 55
      height: 75
    spriteID: 362d97e49a2536242bf5b7ac0e1b0ff9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 282}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 4
      width: 92
      height: 75
    spriteID: dfbfe3df40df0dc4fa82cbb6439d8079
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 4}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.51388896, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 853
      width: 72
      height: 73
    spriteID: 91793990f7ffb1c449837100482e4ed9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 853}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 344
      width: 74
      height: 75
    spriteID: 3cda917dfcb5d7c46936a2ea4fd4e96c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 344}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 427
      width: 74
      height: 75
    spriteID: 5af56ac38731699428c969443e2a805d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 427}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.51282054, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 934
      width: 78
      height: 76
    spriteID: 3260b22969c9ef84aac3ed80c0e3e674
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 934}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.469697, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 650
      width: 66
      height: 75
    spriteID: a343c13ad656557479c4ff91b57c4d4a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 650}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 171
      width: 64
      height: 75
    spriteID: 7e562139eb041064ea64cdb2dd3596b4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 171}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 365
      width: 55
      height: 75
    spriteID: badb876742228f24a80a32786ddeb08f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 365}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 87
      width: 92
      height: 75
    spriteID: b37a7ea8ab212864daaf1f7c5b15d54f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 87}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.46835443, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 85
      width: 79
      height: 73
    spriteID: e8a90d9c7118742468ce617a9ccbfb57
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 85}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 691
      width: 73
      height: 73
    spriteID: 8efdb30233d606e4eb16cdb1494c910c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 691}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 772
      width: 73
      height: 73
    spriteID: 4fd59376382fea842a0c2bc231050fdb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 772}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.5625, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 4
      width: 80
      height: 73
    spriteID: 125966f10ebda48439ec1c20203554e9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49206352, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 669
      width: 63
      height: 75
    spriteID: 38540476302f0124b83dbdabc63ad5fc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 669}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 254
      width: 64
      height: 75
    spriteID: 8889a25b9654a8846820878d6d878b32
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 254}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 448
      width: 55
      height: 75
    spriteID: bcd07df7441ead5478e3d1060e5407a3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 448}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 170
      width: 92
      height: 75
    spriteID: be64cd6206e47894aa60465a12c0eafa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 170}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.52459013, y: -0.5930233}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 934
      width: 61
      height: 86
    spriteID: b50c46b0acec9cf4990871ee8667d826
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 934}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 4
      width: 67
      height: 88
    spriteID: b80e9faf60b001e48953dcfc83225354
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 4}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 100
      width: 67
      height: 88
    spriteID: 2dd8f9485a0c5f342bdf9f934de39e16
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 100}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.5675675, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 253
      width: 74
      height: 89
    spriteID: f8b4f9af6c9b254409b2a034a27c8ced
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 253}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 166
      width: 69
      height: 81
    spriteID: 4c01be716cd768243aac170fb8bbfb11
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 166}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 337
      width: 64
      height: 75
    spriteID: 5496580c45b639749944214b85d7c308
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 337}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 531
      width: 55
      height: 75
    spriteID: 055c689eb08b15141a8b98cc603ce479
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 531}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.36904767, y: -0.70512825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 350
      width: 84
      height: 78
    spriteID: 5ceba66a36d435b45b2cc8d2bd5423f1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 350}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.58181816, y: -0.45744678}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 381
      width: 55
      height: 94
    spriteID: 4d9fe28cbd937b348bb953c39b9dd8ac
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 381}
  - name: Frame_49
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 817
      width: 55
      height: 89
    spriteID: a90854d6553aa1e4e9596eaee6c83ba2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 817}
  - name: Frame_50
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 914
      width: 55
      height: 89
    spriteID: 0a3ac7f4af4ac504ba53ee9da7ec023c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 914}
  - name: Frame_51
    originalName: 
    pivot: {x: 0.5932203, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 196
      width: 59
      height: 89
    spriteID: 8e55e6cd756a0ec46a886a4526d803bc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 196}
  - name: Frame_52
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 255
      width: 69
      height: 81
    spriteID: 8f7a4353ffc6913439bf48798856383b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 255}
  - name: Frame_53
    originalName: 
    pivot: {x: 0.49206352, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 995
      width: 63
      height: 14
    spriteID: f6e570caadf585b47bfbcab236a06dcb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 995}
  - name: Frame_54
    originalName: 
    pivot: {x: 0.6326531, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 614
      width: 49
      height: 14
    spriteID: 6984fe3414832b641b6a7fcba3382cbc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 614}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2225973814
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Archer_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 1427eb0f56d316944a8a7723f5a2ed46
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 75
      spriteId: 5e23df45e8638314098de5302091959f
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 1e54660af2bd67b40bccc8ca1bdbcbb0
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 42d6786780d696441bcf111ef31cbe16
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 74
      spriteId: e2589f91b050079478ddc7bb85d2d8b7
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 65
        height: 71
      spriteId: d094c532bcb887d4c8173d3dcf03df39
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 73
      spriteId: ef9c660432fc4a64e84f036dc88eec4c
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 77
      spriteId: bb64a381ee9dc994bbb38fcd0557096c
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 76
      spriteId: 1c7dc831eed141442b3d4405ed9c0aa2
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: 1913da8292c29834bac3fab0b7b9c220
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 72
        height: 77
      spriteId: d71a9f5f446364140a28dc8a3628c4ff
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 58
        width: 69
        height: 76
      spriteId: 7f55095e809808d46b9969ece8dc95da
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: f2174f3d3328b9340b1bc227ed2ab9d3
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 80
      spriteId: fe0c30c92589d7c418e19fdc6191ea0a
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 75
        height: 82
      spriteId: 2c27727970ad6964eb3c0bb3a3062284
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 114
      spriteId: 87dad9052a07ea740b2570449affb5ec
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 98
      spriteId: 7edd6987270ac274c97c074e1a591fbd
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: ef49404aa83fd6b40b095764b577007f
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: f8c35e433beacd84b95758ed4f82e186
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 69
        height: 94
      spriteId: a587130b78c7ffa429f7629c95696d58
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 88
      spriteId: d8bae4472aa9e2745bb2d69d8d63ea3c
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: d29c53b1a597ab7458b404cd42933262
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 362d97e49a2536242bf5b7ac0e1b0ff9
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: dfbfe3df40df0dc4fa82cbb6439d8079
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 72
        height: 73
      spriteId: 91793990f7ffb1c449837100482e4ed9
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: 3cda917dfcb5d7c46936a2ea4fd4e96c
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: 5af56ac38731699428c969443e2a805d
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 78
        height: 76
      spriteId: 3260b22969c9ef84aac3ed80c0e3e674
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 66
        height: 75
      spriteId: a343c13ad656557479c4ff91b57c4d4a
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 7e562139eb041064ea64cdb2dd3596b4
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: badb876742228f24a80a32786ddeb08f
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: b37a7ea8ab212864daaf1f7c5b15d54f
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 79
        height: 73
      spriteId: e8a90d9c7118742468ce617a9ccbfb57
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 8efdb30233d606e4eb16cdb1494c910c
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 4fd59376382fea842a0c2bc231050fdb
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 58
        width: 80
        height: 73
      spriteId: 125966f10ebda48439ec1c20203554e9
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 63
        height: 75
      spriteId: 38540476302f0124b83dbdabc63ad5fc
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 8889a25b9654a8846820878d6d878b32
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: bcd07df7441ead5478e3d1060e5407a3
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: be64cd6206e47894aa60465a12c0eafa
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 51
        width: 61
        height: 86
      spriteId: b50c46b0acec9cf4990871ee8667d826
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: b80e9faf60b001e48953dcfc83225354
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: 2dd8f9485a0c5f342bdf9f934de39e16
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 48
        width: 74
        height: 89
      spriteId: f8b4f9af6c9b254409b2a034a27c8ced
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 4c01be716cd768243aac170fb8bbfb11
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 5496580c45b639749944214b85d7c308
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 055c689eb08b15141a8b98cc603ce479
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 55
        width: 84
        height: 78
      spriteId: 5ceba66a36d435b45b2cc8d2bd5423f1
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 43
        width: 55
        height: 94
      spriteId: 4d9fe28cbd937b348bb953c39b9dd8ac
    - name: Frame_49
      frameIndex: 49
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: a90854d6553aa1e4e9596eaee6c83ba2
    - name: Frame_50
      frameIndex: 50
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: 0a3ac7f4af4ac504ba53ee9da7ec023c
    - name: Frame_51
      frameIndex: 51
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 48
        width: 59
        height: 89
      spriteId: 8e55e6cd756a0ec46a886a4526d803bc
    - name: Frame_52
      frameIndex: 52
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 8f7a4353ffc6913439bf48798856383b
    - name: Frame_53
      frameIndex: 53
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 63
        height: 14
      spriteId: f6e570caadf585b47bfbcab236a06dcb
    - name: Frame_54
      frameIndex: 54
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 49
        height: 14
      spriteId: 6984fe3414832b641b6a7fcba3382cbc
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

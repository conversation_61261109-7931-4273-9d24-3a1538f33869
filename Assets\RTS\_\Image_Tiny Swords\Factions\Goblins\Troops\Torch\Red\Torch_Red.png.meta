fileFormatVersion: 2
guid: 73693bf51408baa46ba4c1c38105ce01
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4230181108591323607
    second: Torch_Red_0
  - first:
      213: 1276301746398581189
    second: Torch_Red_1
  - first:
      213: 8474622326328232153
    second: Torch_Red_2
  - first:
      213: -4071349217737685964
    second: Torch_Red_3
  - first:
      213: -7369122931189845133
    second: Torch_Red_4
  - first:
      213: 914313290774012156
    second: Torch_Red_5
  - first:
      213: -7114989445226215597
    second: Torch_Red_6
  - first:
      213: 7099923657110077353
    second: Torch_Red_7
  - first:
      213: -1649346297522608045
    second: Torch_Red_8
  - first:
      213: -7044588005900942876
    second: Torch_Red_9
  - first:
      213: -7250923013278643159
    second: Torch_Red_10
  - first:
      213: -759784588031407353
    second: Torch_Red_11
  - first:
      213: -462455619930006344
    second: Torch_Red_12
  - first:
      213: -4045980632669398402
    second: Torch_Red_13
  - first:
      213: 2400894962720278677
    second: Torch_Red_14
  - first:
      213: 3118926007335269732
    second: Torch_Red_15
  - first:
      213: 9113646813091510599
    second: Torch_Red_16
  - first:
      213: 6248227852262120436
    second: Torch_Red_17
  - first:
      213: 6084245027050260555
    second: Torch_Red_18
  - first:
      213: -2997398667913583680
    second: Torch_Red_19
  - first:
      213: -5878108617965649021
    second: Torch_Red_20
  - first:
      213: 4362545556120400592
    second: Torch_Red_21
  - first:
      213: 5867112988911455960
    second: Torch_Red_22
  - first:
      213: -2701350933335208944
    second: Torch_Red_23
  - first:
      213: -6788512504216807580
    second: Torch_Red_24
  - first:
      213: -6152072047719948750
    second: Torch_Red_25
  - first:
      213: -7947289817204367736
    second: Torch_Red_26
  - first:
      213: 6443348011037700053
    second: Torch_Red_27
  - first:
      213: -4146183439964304651
    second: Torch_Red_28
  - first:
      213: 9055524857887277902
    second: Torch_Red_29
  - first:
      213: 8493538717480454915
    second: Torch_Red_30
  - first:
      213: -4900098501004535040
    second: Torch_Red_31
  - first:
      213: -4325488850844732842
    second: Torch_Red_32
  - first:
      213: -4486315415672803369
    second: Torch_Red_33
  - first:
      213: 8916338481023245157
    second: Torch_Red_34
  - first:
      213: 4592332568881346839
    second: Torch_Red_35
  - first:
      213: -1709421964988718071
    second: Torch_Red_36
  - first:
      213: 2356305789195521809
    second: Torch_Red_37
  - first:
      213: 552807240558472972
    second: Torch_Red_38
  - first:
      213: -637619430680877308
    second: Torch_Red_39
  - first:
      213: 5350158233536662628
    second: Torch_Red_40
  - first:
      213: 5000852720746546742
    second: Torch_Red_41
  - first:
      213: -6411630704831134195
    second: Torch_Red_42
  - first:
      213: -2412321023182316411
    second: Torch_Red_43
  - first:
      213: 8464253452077613183
    second: Torch_Red_44
  - first:
      213: -3065764301500857402
    second: Torch_Red_45
  - first:
      213: 8425392675165650466
    second: Torch_Red_46
  - first:
      213: 942934686704506993
    second: Torch_Red_47
  - first:
      213: -7079746115744764609
    second: Torch_Red_48
  - first:
      213: 7572155922822532492
    second: Torch_Red_49
  - first:
      213: -2097473375111496110
    second: Torch_Red_50
  - first:
      213: -2130209083304089009
    second: Torch_Red_51
  - first:
      213: -378429574461971776
    second: Torch_Red_52
  - first:
      213: 4712993094387143494
    second: Torch_Red_53
  - first:
      213: -8617498972278274312
    second: Torch_Red_54
  - first:
      213: 8667919236409483043
    second: Torch_Red_55
  - first:
      213: 5399335214889680233
    second: Torch_Red_56
  - first:
      213: -3882765700670896107
    second: Torch_Red_57
  - first:
      213: -6741247101954892729
    second: Torch_Red_58
  - first:
      213: 7770955427382116890
    second: Torch_Red_59
  - first:
      213: -4789019683098857370
    second: Torch_Red_60
  - first:
      213: 600381876937575802
    second: Torch_Red_61
  - first:
      213: -3353011486682343474
    second: Torch_Red_62
  - first:
      213: -7117251695677950216
    second: Torch_Red_63
  - first:
      213: 6162565395391482622
    second: Torch_Red_64
  - first:
      213: -1817103508302252420
    second: Torch_Red_65
  - first:
      213: 4603316556706280259
    second: Torch_Red_66
  - first:
      213: 6788868534399382501
    second: Torch_Red_67
  - first:
      213: 6284465534316468161
    second: Torch_Red_68
  - first:
      213: -843894682099968855
    second: Torch_Red_69
  - first:
      213: 319119393225024771
    second: Torch_Red_70
  - first:
      213: 9094337743951809700
    second: Torch_Red_71
  - first:
      213: -7920373560248185494
    second: Torch_Red_72
  - first:
      213: -1297680780161171876
    second: Torch_Red_73
  - first:
      213: -663314108799487433
    second: Torch_Red_74
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Torch_Red_0
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92698d6b1b06b45c0800000000000000
      internalID: -4230181108591323607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_1
      rect:
        serializedVersion: 2
        x: 192
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c15c4c1da556b110800000000000000
      internalID: 1276301746398581189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_2
      rect:
        serializedVersion: 2
        x: 384
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d88366fa18eb9570800000000000000
      internalID: 8474622326328232153
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_3
      rect:
        serializedVersion: 2
        x: 576
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4344e281579af77c0800000000000000
      internalID: -4071349217737685964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_4
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3737e34ccbd9bb990800000000000000
      internalID: -7369122931189845133
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_5
      rect:
        serializedVersion: 2
        x: 960
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf0e00ef71b40bc00800000000000000
      internalID: 914313290774012156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_6
      rect:
        serializedVersion: 2
        x: 1152
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35743a0ebca724d90800000000000000
      internalID: -7114989445226215597
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_7
      rect:
        serializedVersion: 2
        x: 0
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9af307af2fef78260800000000000000
      internalID: 7099923657110077353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_8
      rect:
        serializedVersion: 2
        x: 192
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3582f80f7485c19e0800000000000000
      internalID: -1649346297522608045
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_9
      rect:
        serializedVersion: 2
        x: 384
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e10c4f48889c3e90800000000000000
      internalID: -7044588005900942876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_10
      rect:
        serializedVersion: 2
        x: 576
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92c42dbbfeb8f5b90800000000000000
      internalID: -7250923013278643159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_11
      rect:
        serializedVersion: 2
        x: 768
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70b429451f3b475f0800000000000000
      internalID: -759784588031407353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_12
      rect:
        serializedVersion: 2
        x: 960
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bc50b950170599f0800000000000000
      internalID: -462455619930006344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_13
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7a51a51d0ac9d7c0800000000000000
      internalID: -4045980632669398402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_14
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59887f38f31b15120800000000000000
      internalID: 2400894962720278677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_15
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46532fbcda6a84b20800000000000000
      internalID: 3118926007335269732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_16
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 749b0b68d7d2a7e70800000000000000
      internalID: 9113646813091510599
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_17
      rect:
        serializedVersion: 2
        x: 768
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4fb19c9802a26b650800000000000000
      internalID: 6248227852262120436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_18
      rect:
        serializedVersion: 2
        x: 960
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4884cbcd949f6450800000000000000
      internalID: 6084245027050260555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_19
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c328ccdac91766d0800000000000000
      internalID: -2997398667913583680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_20
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 383cef20b53cc6ea0800000000000000
      internalID: -5878108617965649021
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_21
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d2f30dc110ea8c30800000000000000
      internalID: 4362545556120400592
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_22
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dec5299d2c2c6150800000000000000
      internalID: 5867112988911455960
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_23
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 01c82e473afd28ad0800000000000000
      internalID: -2701350933335208944
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_24
      rect:
        serializedVersion: 2
        x: 960
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4671cd038db5ac1a0800000000000000
      internalID: -6788512504216807580
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_25
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23eda0351137f9aa0800000000000000
      internalID: -6152072047719948750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_26
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88e547e840e85b190800000000000000
      internalID: -7947289817204367736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_27
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5df2f12b0ee5b6950800000000000000
      internalID: 6443348011037700053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_28
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5fa0257f02cc576c0800000000000000
      internalID: -4146183439964304651
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_29
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e43471941efabad70800000000000000
      internalID: 9055524857887277902
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Red_30
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30b037d767c1fd570800000000000000
      internalID: 8493538717480454915
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 580b0cfece6804141bbab395da2670c4
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Torch_Red_0: -4230181108591323607
      Torch_Red_1: 1276301746398581189
      Torch_Red_10: -7250923013278643159
      Torch_Red_11: -759784588031407353
      Torch_Red_12: -462455619930006344
      Torch_Red_13: -4045980632669398402
      Torch_Red_14: 2400894962720278677
      Torch_Red_15: 3118926007335269732
      Torch_Red_16: 9113646813091510599
      Torch_Red_17: 6248227852262120436
      Torch_Red_18: 6084245027050260555
      Torch_Red_19: -2997398667913583680
      Torch_Red_2: 8474622326328232153
      Torch_Red_20: -5878108617965649021
      Torch_Red_21: 4362545556120400592
      Torch_Red_22: 5867112988911455960
      Torch_Red_23: -2701350933335208944
      Torch_Red_24: -6788512504216807580
      Torch_Red_25: -6152072047719948750
      Torch_Red_26: -7947289817204367736
      Torch_Red_27: 6443348011037700053
      Torch_Red_28: -4146183439964304651
      Torch_Red_29: 9055524857887277902
      Torch_Red_3: -4071349217737685964
      Torch_Red_30: 8493538717480454915
      Torch_Red_4: -7369122931189845133
      Torch_Red_5: 914313290774012156
      Torch_Red_6: -7114989445226215597
      Torch_Red_7: 7099923657110077353
      Torch_Red_8: -1649346297522608045
      Torch_Red_9: -7044588005900942876
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 


using UnityEngine;


public class SoldierUnit : HumanoidUnit
{

    private bool m_IsRetreating = false;


    public override void SetUnitStance(UnitStanceActionSO unitStanceActionSO)
    {
        base.SetUnitStance(unitStanceActionSO);

        if (CurrentStance == UnitStance.Defensive)
        {
            SetUnitState(UnitState.Idle);
            StopMovement();
            m_IsRetreating = false;
        }
    }

    protected override void OnSetUnitState(UnitState oldState, UnitState newState)
    {
        if (newState == UnitState.Attacking)
        {
            m_NextAutoAttackTime = Time.time + m_AutoAttackFrequency / 2;
        }

        base.OnSetUnitState(oldState, newState);
    }

    protected override void OnSetUnitTask(UnitTask oldTask, UnitTask newTask)
    {
        //Debug.Log($"New Task Set: {newTask.ToString()}");
        if (newTask == UnitTask.Attack && HasTarget)
        {
            MoveTo(Target.transform.position);
        }
        base.OnSetUnitTask(oldTask, newTask);
    }


    protected override void OnSetDestination(DestinationSource destinationSource)
    {
        if (HasTarget && destinationSource == DestinationSource.PlayerClick && (CurrentTask == UnitTask.Attack || CurrentState == UnitState.Attacking))
        {
            m_IsRetreating = true;
            SetTarget(null);
            SetUnitTask(UnitTask.None);
            Debug.Log("Retreating");
        }

    }

    protected override void OnDestinationReached()
    {
        if (m_IsRetreating) m_IsRetreating = false;
    }


    protected override void UpdateBehavior()
    {
        if (CurrentState == UnitState.Idle || CurrentState == UnitState.Moving)
        {
            if (HasTarget)
            {
                if (IsTargetInRange(Target))
                {
                    StopMovement();
                    SetUnitState(UnitState.Attacking);
                }
                else if (CurrentStance == UnitStance.Offensive)
                {
                    MoveTo(Target.transform.position);
                }
            }
            else
            {
                if (CurrentStance == UnitStance.Offensive)
                {
                    if (!m_IsRetreating && TryFindClosestTarget(out var target))
                    {
                        SetTarget(target);
                        SetUnitTask(UnitTask.Attack);
                    }
                }

            }
        }

        else if (CurrentState == UnitState.Attacking)
        {
            if (HasTarget)
            {
                if (IsTargetInRange(Target))
                {
                    TryAttackCurrentTarget();
                    StopMovement();
                }
                else
                {
                    if (CurrentStance == UnitStance.Defensive)
                    {
                        SetTarget(null);
                        SetUnitState(UnitState.Idle);
                    }
                    else
                    {
                        MoveTo(Target.transform.position);
                    }
                }
            }
            else
            {
                SetUnitState(UnitState.Idle);
            }
        }
    }



}// end class
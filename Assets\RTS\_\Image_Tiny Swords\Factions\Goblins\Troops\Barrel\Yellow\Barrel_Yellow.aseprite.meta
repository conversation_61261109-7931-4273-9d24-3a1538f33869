fileFormatVersion: 2
guid: 3353d3c69b6b2c6418cd46ebf96251df
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 321
      width: 50
      height: 70
    spriteID: cf0ef504733afc6439f1eb0cde8991b8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 321}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 399
      width: 50
      height: 70
    spriteID: d0bc416a4fd729940b33a9a0530be64c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 399}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 4
      width: 58
      height: 62
    spriteID: 3b8b70b9ddef94a4fb067e75c116a662
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 229
      width: 42
      height: 84
    spriteID: 2517b94582d8b6f40a9d03610620fe24
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 229}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: -0.26881722}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 50
      height: 93
    spriteID: eaa4601a2794cc34583ceb87bbfcad0f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 4
      width: 54
      height: 67
    spriteID: bbd372d304020114e9737c34ad78abe2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: -0.32500002}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 181
      width: 50
      height: 80
    spriteID: d80424d0c39f3a6468d512a77436ddad
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 181}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 294
      width: 50
      height: 83
    spriteID: 4e4bd04750dd8a242ba9a7a3c4931524
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 294}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 385
      width: 50
      height: 83
    spriteID: ec5aeac82d79a68438c2844827867ff7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 385}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 79
      width: 54
      height: 67
    spriteID: e0353e4e45462894b8dc9ef1ba3acda6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 79}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: -0.28089887}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 105
      width: 50
      height: 89
    spriteID: 7b3d8ddc57a5d244b9a72bcfe9903a9b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 105}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: -0.2631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 269
      width: 42
      height: 95
    spriteID: 8346f647ffb4b3640ac55f4b1c4ee475
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 269}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 74
      width: 58
      height: 62
    spriteID: ce7b1003fa762ad4a96c36ec8d4304f1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 74}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.51111114, y: -0.38157895}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 222
      width: 45
      height: 76
    spriteID: 60b7d9c1493a5a041b698bf2efdc25ef
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 222}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 144
      width: 50
      height: 70
    spriteID: 03c07481ca2459444bb5eb22ef7944fa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 144}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 154
      width: 54
      height: 67
    spriteID: 9fa392d829ddadf44a35e7826fa7a109
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 154}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 202
      width: 50
      height: 84
    spriteID: e1e252f5d1aa48c4395bd56be4500160
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 202}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: -0.29069766}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 87
      width: 48
      height: 86
    spriteID: 9a4076d126aa52e4bbe0024174a7504b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 87}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.50877196, y: -0.3866667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 4
      width: 57
      height: 75
    spriteID: c5db8d296eb3c9c48ab8aa1c45fb2275
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 4}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 127
      y: 4
      width: 46
      height: 79
    spriteID: e614e2828057cf34894ecf6aaded855f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 127, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 372
      width: 50
      height: 79
    spriteID: 3448070dce512604393941aeb3cd96d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 372}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2896261041
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Barrel_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: cf0ef504733afc6439f1eb0cde8991b8
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: d0bc416a4fd729940b33a9a0530be64c
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 3b8b70b9ddef94a4fb067e75c116a662
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 84
      spriteId: 2517b94582d8b6f40a9d03610620fe24
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 93
      spriteId: eaa4601a2794cc34583ceb87bbfcad0f
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: bbd372d304020114e9737c34ad78abe2
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 80
      spriteId: d80424d0c39f3a6468d512a77436ddad
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: 4e4bd04750dd8a242ba9a7a3c4931524
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: ec5aeac82d79a68438c2844827867ff7
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: e0353e4e45462894b8dc9ef1ba3acda6
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 89
      spriteId: 7b3d8ddc57a5d244b9a72bcfe9903a9b
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 95
      spriteId: 8346f647ffb4b3640ac55f4b1c4ee475
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: ce7b1003fa762ad4a96c36ec8d4304f1
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 45
        height: 76
      spriteId: 60b7d9c1493a5a041b698bf2efdc25ef
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 03c07481ca2459444bb5eb22ef7944fa
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 9fa392d829ddadf44a35e7826fa7a109
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 84
      spriteId: e1e252f5d1aa48c4395bd56be4500160
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 25
        width: 48
        height: 86
      spriteId: 9a4076d126aa52e4bbe0024174a7504b
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 57
        height: 75
      spriteId: c5db8d296eb3c9c48ab8aa1c45fb2275
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 46
        height: 79
      spriteId: e614e2828057cf34894ecf6aaded855f
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 79
      spriteId: 3448070dce512604393941aeb3cd96d3
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 512, y: 512}

fileFormatVersion: 2
guid: 760f917ccd8b0e3429431da23a2849a3
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 809
      y: 479
      width: 78
      height: 91
    spriteID: 301e0414cc3ab1d45bb553790b27d310
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 809, y: 479}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 895
      y: 479
      width: 78
      height: 91
    spriteID: 04138379a2fea1a4fa20143cc2c228b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 895, y: 479}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 241
      y: 590
      width: 78
      height: 91
    spriteID: f715f1912aafbb348a0f6204dec30011
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 241, y: 590}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.42500004, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 721
      y: 479
      width: 80
      height: 89
    spriteID: ea82ca3aaa4ce78468d7633e1e5b3e2c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 721, y: 479}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.42500004, y: -0.6436781}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 413
      y: 590
      width: 80
      height: 87
    spriteID: eb4bcc8a37ecd3f47ab109bd6b947eda
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 413, y: 590}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.42307693, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 501
      y: 590
      width: 78
      height: 88
    spriteID: 7fcad58abab69ba4b833b00dd009b5bd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 501, y: 590}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 327
      y: 590
      width: 78
      height: 91
    spriteID: 562de722342613e41ab2abaab1f85ef0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 327, y: 590}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.41666672, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 956
      y: 129
      width: 60
      height: 93
    spriteID: 075ec22ffe162024f8338945f93b7a73
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 956, y: 129}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.40579712, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 661
      y: 590
      width: 69
      height: 94
    spriteID: 99e3bc8ae8a9d024d9744799d0d5067d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 661, y: 590}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.41025642, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 825
      y: 590
      width: 78
      height: 80
    spriteID: da967f962ba973c4db73d3e0c1d6e466
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 825, y: 590}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44210526, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 538
      y: 274
      width: 95
      height: 93
    spriteID: 74c628eab630bc949818218167b35e93
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 538, y: 274}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.43820223, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 641
      y: 274
      width: 89
      height: 94
    spriteID: c58f039c7d1d4c941b9a354cb40e1914
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 641, y: 274}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.4177215, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 590
      width: 79
      height: 80
    spriteID: e4aa7d29978c70340a1cffa39c2152c5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 590}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5352112, y: -0.5185185}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 590
      width: 71
      height: 108
    spriteID: 6c64101c4be307e41b778c7d18a825d0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 590}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 590
      width: 71
      height: 112
    spriteID: 0f23f0f289ff03b4dbdb38937502c176
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 590}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 83
      y: 590
      width: 71
      height: 112
    spriteID: c8a72db6a322bbb448b370b90742ff1f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 83, y: 590}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.18691586, y: -0.49107146}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 858
      y: 4
      width: 107
      height: 112
    spriteID: 213b8f78e8539c44e85b7e189a1d56c1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 858, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.20560749, y: -0.57608694}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 841
      y: 129
      width: 107
      height: 92
    spriteID: 78ec7d2447b28b14d8fc1960ecf3d59e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 841, y: 129}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.28999996, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 430
      y: 274
      width: 100
      height: 89
    spriteID: c8d18d977efa3774ab45616c5d12f3ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 430, y: 274}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.75609756, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 631
      y: 479
      width: 82
      height: 89
    spriteID: d80ce6d6e73858840bab182b8ae0c165
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 631, y: 479}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 359
      y: 479
      width: 84
      height: 89
    spriteID: 845f039d9570deb439cf106c24c3e2b4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 359, y: 479}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 451
      y: 479
      width: 84
      height: 89
    spriteID: d0a21fc8e11a0e046b2a2a65274da94b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 451, y: 479}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.30894306, y: -0.45999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 593
      y: 4
      width: 123
      height: 100
    spriteID: ef2c822ff43d61245989106ab64818dd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 593, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.29508197, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 508
      y: 129
      width: 122
      height: 94
    spriteID: 832575ec700ed50449906836d27a6496
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 508, y: 129}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.40000004, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 916
      y: 274
      width: 85
      height: 88
    spriteID: e335a056f9977bb4da0a22934b0d1a73
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 916, y: 274}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.4871795, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 186
      y: 479
      width: 78
      height: 101
    spriteID: a67d884335951e245be822b882a7b4f7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 186, y: 479}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 479
      width: 83
      height: 103
    spriteID: 49d2d9869437f4247b006f674cdcfc21
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 479}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 95
      y: 479
      width: 83
      height: 103
    spriteID: 9c149a762f4e247439d00a99cf824936
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 95, y: 479}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.42372882, y: -0.09489051}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 129
      width: 118
      height: 137
    spriteID: bc929966842ef304e9ffc52bc8e50e4b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 129}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.6179775, y: -0.20161289}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 638
      y: 129
      width: 89
      height: 124
    spriteID: c6cfe943b214d44408b563eb88d63301
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 638, y: 129}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.6363636, y: -0.5252526}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 587
      y: 590
      width: 66
      height: 99
    spriteID: 8d6e5a19f08351642914ac52379176d7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 587, y: 590}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.75, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 543
      y: 479
      width: 80
      height: 93
    spriteID: 6838f2a672fd8cf49be0de7491f6d3bc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 543, y: 479}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 274
      width: 81
      height: 94
    spriteID: 16d1e38899f6fd742bef76edf2bb92e7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 274}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 827
      y: 274
      width: 81
      height: 94
    spriteID: 9062ef483de5d1b4a8795d988f16bcd8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 827, y: 274}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.39041096, y: -0.23931624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 146
      height: 117
    spriteID: 4d7117e540239bf4ca98a4aea86d81aa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.24137934, y: -0.24786326}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 469
      y: 4
      width: 116
      height: 117
    spriteID: 4963a6ba39c0fa944ac2f414eb9e3a84
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 469, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.34343436, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 274
      width: 99
      height: 94
    spriteID: eed987ade85c95944a85a772f3fd112f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 274}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.65254235, y: -0.56565654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 382
      y: 129
      width: 118
      height: 99
    spriteID: b47c9885fd0294240a91448b6a5b75bc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 382, y: 129}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 130
      y: 129
      width: 118
      height: 101
    spriteID: e55c59b0c8369ed43ba02ef92d85d821
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 130, y: 129}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 256
      y: 129
      width: 118
      height: 101
    spriteID: bfa28c95ac3fded4eab012b5e2ed3ec0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 256, y: 129}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.5037038, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 326
      y: 4
      width: 135
      height: 107
    spriteID: 12a299981a56eb443a7459df8cc120b0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 326, y: 4}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.26530612, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 735
      y: 129
      width: 98
      height: 107
    spriteID: c45c83e1a5b0b95438536361c83ec48f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 735, y: 129}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.25316453, y: -0.5833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 272
      y: 479
      width: 79
      height: 96
    spriteID: b04649ef7dbc2be4093efa615b2c767d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 272, y: 479}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.20618555, y: -0.6086956}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 325
      y: 274
      width: 97
      height: 92
    spriteID: c5bea744a936d444eb79418d681d5eca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 325, y: 274}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 274
      width: 99
      height: 91
    spriteID: d1f4e4a8aa47dd14fa4f4d520e5cedb2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 274}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 218
      y: 274
      width: 99
      height: 91
    spriteID: 3bb2939c666ea06459eea6f7350d079b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 218, y: 274}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.54375005, y: -0.57}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 158
      y: 4
      width: 160
      height: 100
    spriteID: e013987cd33d1e24eb0eb4df4171da89
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 158, y: 4}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.72222227, y: -0.58762884}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 724
      y: 4
      width: 126
      height: 97
    spriteID: cce06260b3fbb584b9c6ba7b900687d1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 724, y: 4}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.59782606, y: -0.58947366}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 376
      width: 92
      height: 95
    spriteID: 3606f493b4b31f145a378159eaa934e2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 376}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1150698364
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Warrior_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 301e0414cc3ab1d45bb553790b27d310
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 04138379a2fea1a4fa20143cc2c228b7
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: f715f1912aafbb348a0f6204dec30011
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 89
      spriteId: ea82ca3aaa4ce78468d7633e1e5b3e2c
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 87
      spriteId: eb4bcc8a37ecd3f47ab109bd6b947eda
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 88
      spriteId: 7fcad58abab69ba4b833b00dd009b5bd
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 562de722342613e41ab2abaab1f85ef0
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 71
        y: 56
        width: 60
        height: 93
      spriteId: 075ec22ffe162024f8338945f93b7a73
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 56
        width: 69
        height: 94
      spriteId: 99e3bc8ae8a9d024d9744799d0d5067d
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 56
        width: 78
        height: 80
      spriteId: da967f962ba973c4db73d3e0c1d6e466
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 56
        width: 95
        height: 93
      spriteId: 74c628eab630bc949818218167b35e93
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 89
        height: 94
      spriteId: c58f039c7d1d4c941b9a354cb40e1914
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 79
        height: 80
      spriteId: e4aa7d29978c70340a1cffa39c2152c5
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 71
        height: 108
      spriteId: 6c64101c4be307e41b778c7d18a825d0
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: 0f23f0f289ff03b4dbdb38937502c176
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: c8a72db6a322bbb448b370b90742ff1f
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 55
        width: 107
        height: 112
      spriteId: 213b8f78e8539c44e85b7e189a1d56c1
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 74
        y: 53
        width: 107
        height: 92
      spriteId: 78ec7d2447b28b14d8fc1960ecf3d59e
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 56
        width: 100
        height: 89
      spriteId: c8d18d977efa3774ab45616c5d12f3ca
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 34
        y: 56
        width: 82
        height: 89
      spriteId: d80ce6d6e73858840bab182b8ae0c165
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: 845f039d9570deb439cf106c24c3e2b4
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: d0a21fc8e11a0e046b2a2a65274da94b
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 46
        width: 123
        height: 100
      spriteId: ef2c822ff43d61245989106ab64818dd
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 56
        width: 122
        height: 94
      spriteId: 832575ec700ed50449906836d27a6496
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 85
        height: 88
      spriteId: e335a056f9977bb4da0a22934b0d1a73
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 78
        height: 101
      spriteId: a67d884335951e245be822b882a7b4f7
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 49d2d9869437f4247b006f674cdcfc21
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 9c149a762f4e247439d00a99cf824936
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 13
        width: 118
        height: 137
      spriteId: bc929966842ef304e9ffc52bc8e50e4b
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 25
        width: 89
        height: 124
      spriteId: c6cfe943b214d44408b563eb88d63301
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 52
        width: 66
        height: 99
      spriteId: 8d6e5a19f08351642914ac52379176d7
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 36
        y: 56
        width: 80
        height: 93
      spriteId: 6838f2a672fd8cf49be0de7491f6d3bc
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: 16d1e38899f6fd742bef76edf2bb92e7
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: 9062ef483de5d1b4a8795d988f16bcd8
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 28
        width: 146
        height: 117
      spriteId: 4d7117e540239bf4ca98a4aea86d81aa
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 29
        width: 116
        height: 117
      spriteId: 4963a6ba39c0fa944ac2f414eb9e3a84
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 99
        height: 94
      spriteId: eed987ade85c95944a85a772f3fd112f
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 56
        width: 118
        height: 99
      spriteId: b47c9885fd0294240a91448b6a5b75bc
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: e55c59b0c8369ed43ba02ef92d85d821
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: bfa28c95ac3fded4eab012b5e2ed3ec0
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 57
        width: 135
        height: 107
      spriteId: 12a299981a56eb443a7459df8cc120b0
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 57
        width: 98
        height: 107
      spriteId: c45c83e1a5b0b95438536361c83ec48f
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 79
        height: 96
      spriteId: b04649ef7dbc2be4093efa615b2c767d
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 97
        height: 92
      spriteId: c5bea744a936d444eb79418d681d5eca
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: d1f4e4a8aa47dd14fa4f4d520e5cedb2
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 3bb2939c666ea06459eea6f7350d079b
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 9
        y: 57
        width: 160
        height: 100
      spriteId: e013987cd33d1e24eb0eb4df4171da89
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 5
        y: 57
        width: 126
        height: 97
      spriteId: cce06260b3fbb584b9c6ba7b900687d1
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 56
        width: 92
        height: 95
      spriteId: 3606f493b4b31f145a378159eaa934e2
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 1024, y: 1024}

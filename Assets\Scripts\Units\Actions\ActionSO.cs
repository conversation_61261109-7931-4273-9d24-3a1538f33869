using UnityEngine;

/// <summary>
/// كلاس أساسي مجرد لجميع إجراءات اللعبة، يستخدم نمط ScriptableObject لتخزين بيانات الإجراءات. يوفر واجهة موحدة لتنفيذ الإجراءات.
/// </summary>
/// <remarks>
/// يرث من: ScriptableObject
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public abstract class ActionSO : ScriptableObject
{
    public Sprite Icon;
    public string ActionName;
    public string Guid = System.Guid.NewGuid().ToString();


    /// <summary>
    /// تنفذ هذه الدالة الإجراء المرتبط بهذا الكائن، وهي جزء من واجهة الإجراءات في اللعبة.
    /// </summary>
    /// <param name="gameManager">معامل من نوع GameManager يستخدم في الدالة لتحديد gameManager.</param>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    public abstract void Execute(GameManager gameManager);


}// end class
fileFormatVersion: 2
guid: f04de71f9b5403d489920b3e9740c8bd
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -219413305308306393
    second: Explosions_0
  - first:
      213: 2086932433408658518
    second: Explosions_1
  - first:
      213: -1496003835769365672
    second: Explosions_2
  - first:
      213: 8155359584847542395
    second: Explosions_3
  - first:
      213: -2748982251013895145
    second: Explosions_4
  - first:
      213: -4000024841062830767
    second: Explosions_5
  - first:
      213: 2440391306043018828
    second: Explosions_6
  - first:
      213: -5811049801739478279
    second: Explosions_7
  - first:
      213: 4692797990331628042
    second: Explosions_8
  - first:
      213: -2954503319071914102
    second: Explosions_9
  - first:
      213: 8775652852170063420
    second: Explosions_10
  - first:
      213: -4510438909644432361
    second: Explosions_11
  - first:
      213: 5629080682068195546
    second: Explosions_12
  - first:
      213: 8077181301257362129
    second: Explosions_13
  - first:
      213: 7313615633994041012
    second: Explosions_14
  - first:
      213: -4140207718246244285
    second: Explosions_15
  - first:
      213: 6704892612394352278
    second: Explosions_16
  - first:
      213: -2877182862650568363
    second: Explosions_17
  - first:
      213: -5911512441146267429
    second: Explosions_18
  - first:
      213: -8697329043848114858
    second: Explosions_19
  - first:
      213: 7929671693153291184
    second: Explosions_20
  - first:
      213: -3298970926722172130
    second: Explosions_21
  - first:
      213: 7525184402854162994
    second: Explosions_22
  - first:
      213: -8506858873097493397
    second: Explosions_23
  - first:
      213: 4881995402669703931
    second: Explosions_24
  - first:
      213: 1428966248761878080
    second: Explosions_25
  - first:
      213: 5087084283503695632
    second: Explosions_26
  - first:
      213: 3734320313703903120
    second: Explosions_27
  - first:
      213: -7542038626762510176
    second: Explosions_28
  - first:
      213: 3605112816071999896
    second: Explosions_29
  - first:
      213: -6087320794081736211
    second: Explosions_30
  - first:
      213: -2818956064181698402
    second: Explosions_31
  - first:
      213: 3314618265817649379
    second: Explosions_32
  - first:
      213: -3830283168876422072
    second: Explosions_33
  - first:
      213: -9069596609865337503
    second: Explosions_34
  - first:
      213: -5007335976962760624
    second: Explosions_35
  - first:
      213: 3860587452364819767
    second: Explosions_36
  - first:
      213: 6127323474448029497
    second: Explosions_37
  - first:
      213: 6729241830990315157
    second: Explosions_38
  - first:
      213: -5816702259533972444
    second: Explosions_39
  - first:
      213: -1484441783502042958
    second: Explosions_40
  - first:
      213: 6684000849916791865
    second: Explosions_41
  - first:
      213: 8302178842386709873
    second: Explosions_42
  - first:
      213: 6979966350457639186
    second: Explosions_43
  - first:
      213: -6992708459162112669
    second: Explosions_44
  - first:
      213: -7196347337717082127
    second: Explosions_45
  - first:
      213: 7727001707065293090
    second: Explosions_46
  - first:
      213: -5739187193692992617
    second: Explosions_47
  - first:
      213: 1560084027039907509
    second: Explosions_48
  - first:
      213: 2159734964066158122
    second: Explosions_49
  - first:
      213: 1691654077426809344
    second: Explosions_50
  - first:
      213: 2146579624928794146
    second: Explosions_51
  - first:
      213: 8834838309783084681
    second: Explosions_52
  - first:
      213: 1959208971821433034
    second: Explosions_53
  - first:
      213: 1657252276611019741
    second: Explosions_54
  - first:
      213: -3256887309998956978
    second: Explosions_55
  - first:
      213: -4134514694995104005
    second: Explosions_56
  - first:
      213: -3554603910607544017
    second: Explosions_57
  - first:
      213: 458427537821127443
    second: Explosions_58
  - first:
      213: 3349372749450208724
    second: Explosions_59
  - first:
      213: 7313240638807208865
    second: Explosions_60
  - first:
      213: 2268942282217140800
    second: Explosions_61
  - first:
      213: -4364247461635691706
    second: Explosions_62
  - first:
      213: -186340760298466691
    second: Explosions_63
  - first:
      213: 2789318554359249845
    second: Explosions_64
  - first:
      213: -5955190807883891449
    second: Explosions_65
  - first:
      213: 3581781632038837464
    second: Explosions_66
  - first:
      213: -6315276453992148585
    second: Explosions_67
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Explosions_0
      rect:
        serializedVersion: 2
        x: 622
        y: 139
        width: 11
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 724aa16e2cc74fcf0800000000000000
      internalID: -219413305308306393
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_1
      rect:
        serializedVersion: 2
        x: 811
        y: 145
        width: 10
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65c809bbdf546fc10800000000000000
      internalID: 2086932433408658518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_2
      rect:
        serializedVersion: 2
        x: 1003
        y: 147
        width: 8
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85b585200702d3be0800000000000000
      internalID: -1496003835769365672
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_3
      rect:
        serializedVersion: 2
        x: 1196
        y: 118
        width: 33
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b783a6e5558ad2170800000000000000
      internalID: 8155359584847542395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_4
      rect:
        serializedVersion: 2
        x: 439
        y: 57
        width: 83
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71c867a1537a9d9d0800000000000000
      internalID: -2748982251013895145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_5
      rect:
        serializedVersion: 2
        x: 713
        y: 129
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15d26bf999e0d78c0800000000000000
      internalID: -4000024841062830767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_6
      rect:
        serializedVersion: 2
        x: 819
        y: 138
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4e45ad86f20ed120800000000000000
      internalID: 2440391306043018828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_7
      rect:
        serializedVersion: 2
        x: 829
        y: 131
        width: 9
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fad63b5ef00b5fa0800000000000000
      internalID: -5811049801739478279
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_8
      rect:
        serializedVersion: 2
        x: 893
        y: 125
        width: 25
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0a3260a8ea202140800000000000000
      internalID: 4692797990331628042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_9
      rect:
        serializedVersion: 2
        x: 1015
        y: 129
        width: 10
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a87810b82ee7ff6d0800000000000000
      internalID: -2954503319071914102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_10
      rect:
        serializedVersion: 2
        x: 1099
        y: 131
        width: 10
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3e7dbd06c169c970800000000000000
      internalID: 8775652852170063420
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_11
      rect:
        serializedVersion: 2
        x: 251
        y: 59
        width: 74
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71c1b8185b3b761c0800000000000000
      internalID: -4510438909644432361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_12
      rect:
        serializedVersion: 2
        x: 629
        y: 120
        width: 12
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad830b26e038e1e40800000000000000
      internalID: 5629080682068195546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_13
      rect:
        serializedVersion: 2
        x: 630
        y: 50
        width: 86
        height: 87
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1da5fd74999e71070800000000000000
      internalID: 8077181301257362129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_14
      rect:
        serializedVersion: 2
        x: 819
        y: 123
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b645ff7bae2f7560800000000000000
      internalID: 7313615633994041012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_15
      rect:
        serializedVersion: 2
        x: 824
        y: 131
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3402a9914070b86c0800000000000000
      internalID: -4140207718246244285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_16
      rect:
        serializedVersion: 2
        x: 1013
        y: 125
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69ef7e29e409c0d50800000000000000
      internalID: 6704892612394352278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_17
      rect:
        serializedVersion: 2
        x: 1087
        y: 121
        width: 12
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5511fdd2e613218d0800000000000000
      internalID: -2877182862650568363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_18
      rect:
        serializedVersion: 2
        x: 1273
        y: 98
        width: 23
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd8ac2c51c616fda0800000000000000
      internalID: -5911512441146267429
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_19
      rect:
        serializedVersion: 2
        x: 63
        y: 55
        width: 66
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65dceb6a051ec4780800000000000000
      internalID: -8697329043848114858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_20
      rect:
        serializedVersion: 2
        x: 832
        y: 54
        width: 69
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bb4fd7446adb0e60800000000000000
      internalID: 7929671693153291184
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_21
      rect:
        serializedVersion: 2
        x: 896
        y: 122
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e1b326cbb63b732d0800000000000000
      internalID: -3298970926722172130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_22
      rect:
        serializedVersion: 2
        x: 900
        y: 120
        width: 9
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 232be612853de6860800000000000000
      internalID: 7525184402854162994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_23
      rect:
        serializedVersion: 2
        x: 1025
        y: 106
        width: 18
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b64e4aa1de091f980800000000000000
      internalID: -8506858873097493397
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_24
      rect:
        serializedVersion: 2
        x: 1036
        y: 115
        width: 11
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf2c73be3f450c340800000000000000
      internalID: 4881995402669703931
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_25
      rect:
        serializedVersion: 2
        x: 1048
        y: 122
        width: 8
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04669757b35b4d310800000000000000
      internalID: 1428966248761878080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_26
      rect:
        serializedVersion: 2
        x: 1231
        y: 113
        width: 7
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 017d5bc0434f89640800000000000000
      internalID: 5087084283503695632
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_27
      rect:
        serializedVersion: 2
        x: 1241
        y: 116
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09fa8df3a88f2d330800000000000000
      internalID: 3734320313703903120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_28
      rect:
        serializedVersion: 2
        x: 1416
        y: 114
        width: 12
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a0bb8114db455790800000000000000
      internalID: -7542038626762510176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_29
      rect:
        serializedVersion: 2
        x: 885
        y: 88
        width: 20
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8910e29610fe70230800000000000000
      internalID: 3605112816071999896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_30
      rect:
        serializedVersion: 2
        x: 1052
        y: 115
        width: 15
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de9c8e26dfd758ba0800000000000000
      internalID: -6087320794081736211
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_31
      rect:
        serializedVersion: 2
        x: 1069
        y: 104
        width: 10
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e904405256e01e8d0800000000000000
      internalID: -2818956064181698402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_32
      rect:
        serializedVersion: 2
        x: 1086
        y: 90
        width: 10
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e0151c40c3effd20800000000000000
      internalID: 3314618265817649379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_33
      rect:
        serializedVersion: 2
        x: 1261
        y: 108
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84c96f960c918dac0800000000000000
      internalID: -3830283168876422072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_34
      rect:
        serializedVersion: 2
        x: 1419
        y: 77
        width: 231
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 165b3e4afe1522280800000000000000
      internalID: -9069596609865337503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_35
      rect:
        serializedVersion: 2
        x: 1037
        y: 102
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 050e91e757e528ab0800000000000000
      internalID: -5007335976962760624
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_36
      rect:
        serializedVersion: 2
        x: 1047
        y: 99
        width: 8
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73d098a65df839530800000000000000
      internalID: 3860587452364819767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_37
      rect:
        serializedVersion: 2
        x: 1055
        y: 101
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 937136e6c30a80550800000000000000
      internalID: 6127323474448029497
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_38
      rect:
        serializedVersion: 2
        x: 1080
        y: 100
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59e70efcac1136d50800000000000000
      internalID: 6729241830990315157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_39
      rect:
        serializedVersion: 2
        x: 1081
        y: 105
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42cf348fc1ce64fa0800000000000000
      internalID: -5816702259533972444
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_40
      rect:
        serializedVersion: 2
        x: 1231
        y: 91
        width: 13
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b80d941014366be0800000000000000
      internalID: -1484441783502042958
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_41
      rect:
        serializedVersion: 2
        x: 1247
        y: 103
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 93c03b11c5752cc50800000000000000
      internalID: 6684000849916791865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_42
      rect:
        serializedVersion: 2
        x: 1425
        y: 104
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17d393a9ba3473370800000000000000
      internalID: 8302178842386709873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_43
      rect:
        serializedVersion: 2
        x: 1444
        y: 106
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 211153f0a62ddd060800000000000000
      internalID: 6979966350457639186
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_44
      rect:
        serializedVersion: 2
        x: 1042
        y: 90
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36de5c194b8e4fe90800000000000000
      internalID: -6992708459162112669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_45
      rect:
        serializedVersion: 2
        x: 1047
        y: 76
        width: 33
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f79b076930712c90800000000000000
      internalID: -7196347337717082127
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_46
      rect:
        serializedVersion: 2
        x: 1056
        y: 91
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 229b678fc13db3b60800000000000000
      internalID: 7727001707065293090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_47
      rect:
        serializedVersion: 2
        x: 1249
        y: 95
        width: 9
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79b4574a7af4a50b0800000000000000
      internalID: -5739187193692992617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_48
      rect:
        serializedVersion: 2
        x: 1249
        y: 72
        width: 24
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5be797b282886a510800000000000000
      internalID: 1560084027039907509
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_49
      rect:
        serializedVersion: 2
        x: 1442
        y: 95
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a26872ec18be8fd10800000000000000
      internalID: 2159734964066158122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_50
      rect:
        serializedVersion: 2
        x: 1032
        y: 69
        width: 22
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00abddfab66f97710800000000000000
      internalID: 1691654077426809344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_51
      rect:
        serializedVersion: 2
        x: 1056
        y: 57
        width: 31
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 226d8f9bbce2acd10800000000000000
      internalID: 2146579624928794146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_52
      rect:
        serializedVersion: 2
        x: 1073
        y: 70
        width: 14
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98e12ae32a6ab9a70800000000000000
      internalID: 8834838309783084681
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_53
      rect:
        serializedVersion: 2
        x: 1226
        y: 75
        width: 11
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac0cc26fd22803b10800000000000000
      internalID: 1959208971821433034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_54
      rect:
        serializedVersion: 2
        x: 625
        y: 54
        width: 15
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd37b924a2ebff610800000000000000
      internalID: 1657252276611019741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_55
      rect:
        serializedVersion: 2
        x: 821
        y: 65
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4ed80e4f363dc2d0800000000000000
      internalID: -3256887309998956978
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_56
      rect:
        serializedVersion: 2
        x: 828
        y: 64
        width: 13
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bfe2f2e5ac04f96c0800000000000000
      internalID: -4134514694995104005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_57
      rect:
        serializedVersion: 2
        x: 831
        y: 59
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f21e50f83928baec0800000000000000
      internalID: -3554603910607544017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_58
      rect:
        serializedVersion: 2
        x: 1015
        y: 61
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 313d513ba69ac5600800000000000000
      internalID: 458427537821127443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_59
      rect:
        serializedVersion: 2
        x: 1039
        y: 61
        width: 24
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d56a5715cc5b7e20800000000000000
      internalID: 3349372749450208724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_60
      rect:
        serializedVersion: 2
        x: 1084
        y: 58
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a3fd012d99dd7560800000000000000
      internalID: 7313240638807208865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_61
      rect:
        serializedVersion: 2
        x: 812
        y: 49
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 042ba6bfbf6ec7f10800000000000000
      internalID: 2268942282217140800
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_62
      rect:
        serializedVersion: 2
        x: 818
        y: 54
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64bed417e041f63c0800000000000000
      internalID: -4364247461635691706
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_63
      rect:
        serializedVersion: 2
        x: 901
        y: 46
        width: 9
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d72db37d01cf96df0800000000000000
      internalID: -186340760298466691
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_64
      rect:
        serializedVersion: 2
        x: 1005
        y: 50
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bb3dfd5276a5b620800000000000000
      internalID: 2789318554359249845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_65
      rect:
        serializedVersion: 2
        x: 1200
        y: 52
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70db86e1389ea5da0800000000000000
      internalID: -5955190807883891449
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_66
      rect:
        serializedVersion: 2
        x: 1095
        y: 42
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dc91b0da6b05b130800000000000000
      internalID: 3581781632038837464
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Explosions_67
      rect:
        serializedVersion: 2
        x: 1289
        y: 44
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 791d86d5681ab58a0800000000000000
      internalID: -6315276453992148585
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Explosions_0: -219413305308306393
      Explosions_1: 2086932433408658518
      Explosions_10: 8775652852170063420
      Explosions_11: -4510438909644432361
      Explosions_12: 5629080682068195546
      Explosions_13: 8077181301257362129
      Explosions_14: 7313615633994041012
      Explosions_15: -4140207718246244285
      Explosions_16: 6704892612394352278
      Explosions_17: -2877182862650568363
      Explosions_18: -5911512441146267429
      Explosions_19: -8697329043848114858
      Explosions_2: -1496003835769365672
      Explosions_20: 7929671693153291184
      Explosions_21: -3298970926722172130
      Explosions_22: 7525184402854162994
      Explosions_23: -8506858873097493397
      Explosions_24: 4881995402669703931
      Explosions_25: 1428966248761878080
      Explosions_26: 5087084283503695632
      Explosions_27: 3734320313703903120
      Explosions_28: -7542038626762510176
      Explosions_29: 3605112816071999896
      Explosions_3: 8155359584847542395
      Explosions_30: -6087320794081736211
      Explosions_31: -2818956064181698402
      Explosions_32: 3314618265817649379
      Explosions_33: -3830283168876422072
      Explosions_34: -9069596609865337503
      Explosions_35: -5007335976962760624
      Explosions_36: 3860587452364819767
      Explosions_37: 6127323474448029497
      Explosions_38: 6729241830990315157
      Explosions_39: -5816702259533972444
      Explosions_4: -2748982251013895145
      Explosions_40: -1484441783502042958
      Explosions_41: 6684000849916791865
      Explosions_42: 8302178842386709873
      Explosions_43: 6979966350457639186
      Explosions_44: -6992708459162112669
      Explosions_45: -7196347337717082127
      Explosions_46: 7727001707065293090
      Explosions_47: -5739187193692992617
      Explosions_48: 1560084027039907509
      Explosions_49: 2159734964066158122
      Explosions_5: -4000024841062830767
      Explosions_50: 1691654077426809344
      Explosions_51: 2146579624928794146
      Explosions_52: 8834838309783084681
      Explosions_53: 1959208971821433034
      Explosions_54: 1657252276611019741
      Explosions_55: -3256887309998956978
      Explosions_56: -4134514694995104005
      Explosions_57: -3554603910607544017
      Explosions_58: 458427537821127443
      Explosions_59: 3349372749450208724
      Explosions_6: 2440391306043018828
      Explosions_60: 7313240638807208865
      Explosions_61: 2268942282217140800
      Explosions_62: -4364247461635691706
      Explosions_63: -186340760298466691
      Explosions_64: 2789318554359249845
      Explosions_65: -5955190807883891449
      Explosions_66: 3581781632038837464
      Explosions_67: -6315276453992148585
      Explosions_7: -5811049801739478279
      Explosions_8: 4692797990331628042
      Explosions_9: -2954503319071914102
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

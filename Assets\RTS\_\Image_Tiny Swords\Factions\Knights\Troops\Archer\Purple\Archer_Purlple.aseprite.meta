fileFormatVersion: 2
guid: 4ff7941a8f139b44fbc3230e749e0f20
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 420
      width: 63
      height: 75
    spriteID: 31afbac0507c34d4aa460a006e0a9904
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.45161292, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 752
      width: 62
      height: 75
    spriteID: ff371c751f7c9c84dbe173ccd4002e3b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 752}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 503
      width: 63
      height: 75
    spriteID: 91152d95f94c00a4a8124f98e6fa21a0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 503}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 586
      width: 63
      height: 75
    spriteID: 84a3a1f8d04506741ae84786ab2d0048
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 586}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.44776118, y: -0.7837838}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 568
      width: 67
      height: 74
    spriteID: 97d5008efb493a847b02afeb63734fac
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 568}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.46153846, y: -0.81690145}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 835
      width: 65
      height: 71
    spriteID: 141522cf4a9a0b2458adf1b3e9dc984b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 835}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.46774188, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 914
      width: 62
      height: 73
    spriteID: 3e06834ca09867244ac26ad8576721c5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 914}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.44776118, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 483
      width: 67
      height: 77
    spriteID: 8021397840fba794b9722f5022a40dfc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 483}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.44615382, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 733
      width: 65
      height: 76
    spriteID: da813a17a82e4e84f9ab010d48c3813b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 733}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 126
      width: 62
      height: 70
    spriteID: 14667d7833246764faf5ab9004f81bc2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 126}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.4861111, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 510
      width: 72
      height: 77
    spriteID: 797142700a81a9146a8c3eb5ef87e120
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 510}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47826087, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 4
      width: 69
      height: 76
    spriteID: 07c631245404fb843afcc0c600bad87a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 62
      height: 70
    spriteID: 5c22f86baf0d1cd43859838578d5cba2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.44615382, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 293
      width: 65
      height: 80
    spriteID: 97e42a80e5d182a408d6003815fb6c0b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 293}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.38666663, y: -0.70731705}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 644
      width: 75
      height: 82
    spriteID: 57cb621abacf60d4d8eaa1e065d90a3f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 644}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.46774188, y: -0.50877196}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 62
      height: 114
    spriteID: fcca2559907817c40b1dbea32f2da18d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.530303, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 538
      width: 66
      height: 98
    spriteID: 2daffa43a0b6f0c4cb59f994778e3b24
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 538}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 734
      width: 66
      height: 92
    spriteID: c02eeafa2600f46448f160f6159684de
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 734}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 834
      width: 66
      height: 92
    spriteID: 21bea5511a369824dbb14244cbfdafa1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 834}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.53623194, y: -0.6170213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 436
      width: 69
      height: 94
    spriteID: 3241a241540ee0a4981def3fc01a4dcb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 436}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.46774188, y: -0.65909094}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 595
      width: 62
      height: 88
    spriteID: 136a03a300f7017479c240f9ccecd4c7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 595}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 88
      width: 64
      height: 75
    spriteID: a3142bfe33e27e54d9b5b55b2d1e6705
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 88}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 282
      width: 55
      height: 75
    spriteID: d2e0a87867d6ae946b6daedb92a3a48d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 282}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 4
      width: 92
      height: 75
    spriteID: 54ed934af65ccee4bbfcdb86a4af8121
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 4}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.51388896, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 853
      width: 72
      height: 73
    spriteID: e834020c643e01043b226931855bdd89
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 853}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 344
      width: 74
      height: 75
    spriteID: 20a6bd2e5ab146947ad92f610ab1eb98
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 344}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 427
      width: 74
      height: 75
    spriteID: 1d83f0e7035bd6441abfc30297b26260
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 427}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.51282054, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 934
      width: 78
      height: 76
    spriteID: 6211438e89a8e8a4e9c0a39a31b36965
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 934}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.469697, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 650
      width: 66
      height: 75
    spriteID: 90f37c504345be84496d501768514fbf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 650}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 171
      width: 64
      height: 75
    spriteID: 5f5d120e985936540af37770cc5bef2a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 171}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 365
      width: 55
      height: 75
    spriteID: 376cd080e8f7ff74491232926c73ad04
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 365}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 87
      width: 92
      height: 75
    spriteID: 3f04732b90ae6aa42a31516f7e1c7c05
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 87}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.46835443, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 85
      width: 79
      height: 73
    spriteID: 92b9308f68257b24fbb6b01c997ff8ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 85}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 691
      width: 73
      height: 73
    spriteID: 5cf5d3af4741e6b4db3980f9f4e1c4e1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 691}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 772
      width: 73
      height: 73
    spriteID: 24467956b4eca5c43b30be508c0db5db
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 772}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.5625, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 4
      width: 80
      height: 73
    spriteID: cc0a6bb9fea33a5489caa5997731f559
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49206352, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 669
      width: 63
      height: 75
    spriteID: ee8b5297d3da2bc498364c2ab2a26c84
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 669}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 254
      width: 64
      height: 75
    spriteID: 98a1eb4319a94e844bf4af2735d34aa2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 254}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 448
      width: 55
      height: 75
    spriteID: cd64663002d073d40a3b67d718acb8a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 448}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 170
      width: 92
      height: 75
    spriteID: 4452c19a60f1bdd4b87a0bc8f4fcc82c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 170}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.52459013, y: -0.5930233}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 934
      width: 61
      height: 86
    spriteID: f2dcd770f67720445b54665eff6308d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 934}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 4
      width: 67
      height: 88
    spriteID: b71d577dc66f5c14e9a3a260506f3bb9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 4}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 100
      width: 67
      height: 88
    spriteID: e1451dbaaab0cf24b83b139765d945c0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 100}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.5675675, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 253
      width: 74
      height: 89
    spriteID: 22b51f4bf1e8dba48b4f1ebc03a2eac2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 253}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 166
      width: 69
      height: 81
    spriteID: 7906978b459d84442b8260cccacab75d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 166}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 337
      width: 64
      height: 75
    spriteID: 8a6fcc5edb2272d4881bac3a2ab30fdb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 337}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 531
      width: 55
      height: 75
    spriteID: 662207ce6bfd80044bac26ffc1a89905
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 531}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.36904767, y: -0.70512825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 350
      width: 84
      height: 78
    spriteID: 71af04f125dfc39468d23df19cd05717
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 350}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.58181816, y: -0.45744678}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 381
      width: 55
      height: 94
    spriteID: 113f38e0d25166d47b03ab860d13f6ff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 381}
  - name: Frame_49
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 817
      width: 55
      height: 89
    spriteID: 7cfdbf5b3da4905449d0931b65732daf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 817}
  - name: Frame_50
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 914
      width: 55
      height: 89
    spriteID: 554de1fb7fc4c734e957ecbf34e21576
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 914}
  - name: Frame_51
    originalName: 
    pivot: {x: 0.5932203, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 196
      width: 59
      height: 89
    spriteID: ba84a2ad5f521a14bbdaed4e16363655
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 196}
  - name: Frame_52
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 255
      width: 69
      height: 81
    spriteID: 6a9a879ce9374204e8395256e740983c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 255}
  - name: Frame_53
    originalName: 
    pivot: {x: 0.49206352, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 995
      width: 63
      height: 14
    spriteID: 882c357387a537c4fb1c5a1136dabc99
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 995}
  - name: Frame_54
    originalName: 
    pivot: {x: 0.6326531, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 614
      width: 49
      height: 14
    spriteID: 8132e86f927e4094eb1b41aedc30ff59
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 614}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 379656746
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Archer_Purlple
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 31afbac0507c34d4aa460a006e0a9904
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 75
      spriteId: ff371c751f7c9c84dbe173ccd4002e3b
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 91152d95f94c00a4a8124f98e6fa21a0
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: 84a3a1f8d04506741ae84786ab2d0048
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 74
      spriteId: 97d5008efb493a847b02afeb63734fac
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 65
        height: 71
      spriteId: 141522cf4a9a0b2458adf1b3e9dc984b
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 73
      spriteId: 3e06834ca09867244ac26ad8576721c5
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 77
      spriteId: 8021397840fba794b9722f5022a40dfc
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 76
      spriteId: da813a17a82e4e84f9ab010d48c3813b
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: 14667d7833246764faf5ab9004f81bc2
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 72
        height: 77
      spriteId: 797142700a81a9146a8c3eb5ef87e120
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 58
        width: 69
        height: 76
      spriteId: 07c631245404fb843afcc0c600bad87a
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: 5c22f86baf0d1cd43859838578d5cba2
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 80
      spriteId: 97e42a80e5d182a408d6003815fb6c0b
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 75
        height: 82
      spriteId: 57cb621abacf60d4d8eaa1e065d90a3f
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 114
      spriteId: fcca2559907817c40b1dbea32f2da18d
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 98
      spriteId: 2daffa43a0b6f0c4cb59f994778e3b24
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: c02eeafa2600f46448f160f6159684de
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: 21bea5511a369824dbb14244cbfdafa1
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 69
        height: 94
      spriteId: 3241a241540ee0a4981def3fc01a4dcb
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 88
      spriteId: 136a03a300f7017479c240f9ccecd4c7
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: a3142bfe33e27e54d9b5b55b2d1e6705
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: d2e0a87867d6ae946b6daedb92a3a48d
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 54ed934af65ccee4bbfcdb86a4af8121
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 72
        height: 73
      spriteId: e834020c643e01043b226931855bdd89
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: 20a6bd2e5ab146947ad92f610ab1eb98
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: 1d83f0e7035bd6441abfc30297b26260
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 78
        height: 76
      spriteId: 6211438e89a8e8a4e9c0a39a31b36965
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 66
        height: 75
      spriteId: 90f37c504345be84496d501768514fbf
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 5f5d120e985936540af37770cc5bef2a
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 376cd080e8f7ff74491232926c73ad04
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 3f04732b90ae6aa42a31516f7e1c7c05
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 79
        height: 73
      spriteId: 92b9308f68257b24fbb6b01c997ff8ed
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 5cf5d3af4741e6b4db3980f9f4e1c4e1
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 24467956b4eca5c43b30be508c0db5db
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 58
        width: 80
        height: 73
      spriteId: cc0a6bb9fea33a5489caa5997731f559
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 63
        height: 75
      spriteId: ee8b5297d3da2bc498364c2ab2a26c84
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 98a1eb4319a94e844bf4af2735d34aa2
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: cd64663002d073d40a3b67d718acb8a8
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 4452c19a60f1bdd4b87a0bc8f4fcc82c
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 51
        width: 61
        height: 86
      spriteId: f2dcd770f67720445b54665eff6308d2
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: b71d577dc66f5c14e9a3a260506f3bb9
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: e1451dbaaab0cf24b83b139765d945c0
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 48
        width: 74
        height: 89
      spriteId: 22b51f4bf1e8dba48b4f1ebc03a2eac2
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 7906978b459d84442b8260cccacab75d
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 8a6fcc5edb2272d4881bac3a2ab30fdb
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 662207ce6bfd80044bac26ffc1a89905
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 55
        width: 84
        height: 78
      spriteId: 71af04f125dfc39468d23df19cd05717
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 43
        width: 55
        height: 94
      spriteId: 113f38e0d25166d47b03ab860d13f6ff
    - name: Frame_49
      frameIndex: 49
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: 7cfdbf5b3da4905449d0931b65732daf
    - name: Frame_50
      frameIndex: 50
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: 554de1fb7fc4c734e957ecbf34e21576
    - name: Frame_51
      frameIndex: 51
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 48
        width: 59
        height: 89
      spriteId: ba84a2ad5f521a14bbdaed4e16363655
    - name: Frame_52
      frameIndex: 52
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 6a9a879ce9374204e8395256e740983c
    - name: Frame_53
      frameIndex: 53
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 63
        height: 14
      spriteId: 882c357387a537c4fb1c5a1136dabc99
    - name: Frame_54
      frameIndex: 54
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 49
        height: 14
      spriteId: 8132e86f927e4094eb1b41aedc30ff59
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}


using System.Collections;
using UnityEngine;


public enum UnitState { Idle, Moving, Attacking, Chopping, Mining, Building, Dead }
public enum UnitTask { None, Build, Chop, Mine, Attack, ReturnResource }

public enum DestinationSource { CodeTriggered, PlayerClick }



public abstract class Unit : MonoBehaviour
{



    [SerializeField] private ActionSO[] m_ActionSO;

    [SerializeField] protected float m_ObjectDetectionRadius = 1f;
    [SerializeField] protected float m_UnitDetectionCheckRate = 0.5f;
    [SerializeField] protected float m_AttackRange = 1.0f;
    [SerializeField] protected float m_AutoAttackFrequency = 1.5f;
    [SerializeField] protected float m_AutoAttackDamageDelay = 0.5f;
    [SerializeField] protected int m_AutoAttackDamage = 7;
    [SerializeField] protected int m_Health = 100;
    [SerializeField] protected Color m_DamageFlashColor = new Color(1f, 0.27f, 0.25f, 1);


    //public bool IsMoving;
    public bool IsTargeted;

    protected GameManager m_GameManager;
    protected Animator m_Animator;

    protected AIPawn m_AIPawn;

    protected SpriteRenderer m_SpriteRenderer;

    protected Material m_OriginalMaterial;
    protected Material m_HighlightMaterial;
    protected CapsuleCollider2D m_Collider;
    protected float m_NextUnitDetectionTime;
    protected float m_NextAutoAttackTime;
    protected int m_CurrentHealth;

    protected UnitStance m_CurrentStance = UnitStance.Offensive;

    public UnitState CurrentState { get; protected set; } = UnitState.Idle;
    public UnitTask CurrentTask { get; protected set; } = UnitTask.None;
    public Unit Target { get; protected set; }

    public virtual bool IsPlayer => true;
    public virtual bool IsBuilding => false;

    public ActionSO[] Actions => m_ActionSO;
    public SpriteRenderer Renderer => m_SpriteRenderer;

    public bool HasTarget => Target != null;
    public int CurrentHealth => m_CurrentHealth;
    public UnitStance CurrentStance => m_CurrentStance;
    public CapsuleCollider2D Collider => m_Collider;



    protected virtual void Start()
    {
        RegisterUnit();
    }

    protected void Awake()
    {
        if (TryGetComponent<Animator>(out var animator))
        {
            m_Animator = animator;
        }

        if (TryGetComponent<AIPawn>(out var aiPawn))
        {
            m_AIPawn = aiPawn;
            m_AIPawn.OnNewPositionSelected += TurnToPosition;
            m_AIPawn.OnDestinationReached += OnDestinationReached;
        }

        m_Collider = GetComponent<CapsuleCollider2D>();
        m_GameManager = GameManager.Get();
        m_SpriteRenderer = GetComponent<SpriteRenderer>();
        m_OriginalMaterial = m_SpriteRenderer.material;
        m_HighlightMaterial = Resources.Load<Material>("Materials/Outline");

        m_CurrentHealth = m_Health;
    }


    void OnDestroy()
    {
        if (m_AIPawn != null)
        {
            m_AIPawn.OnNewPositionSelected -= TurnToPosition;
            m_AIPawn.OnDestinationReached -= OnDestinationReached;
        }

    }

    public void SetUnitTask(UnitTask task)
    {
        OnSetUnitTask(CurrentTask, task);
    }

    public void SetUnitState(UnitState state)
    {
        OnSetUnitState(CurrentState, state);
    }

    public void SetTarget(Unit target)
    {
        Target = target;
    }


    public virtual void SetUnitStance(UnitStanceActionSO unitStanceActionSO)
    {
        m_CurrentStance = unitStanceActionSO.UnitStance;

        for (int i = 0; i < m_ActionSO.Length; i++)
        {
            if (m_ActionSO[i] == unitStanceActionSO)
            {
                m_GameManager.FocusActionUI(i);
                return;
            }
        }

    }


    protected virtual void RegisterUnit()
    {
        m_GameManager.RegisterUnit(this);
    }

    protected virtual void UnregisterUnit()
    {
        m_GameManager.UnregisterUnit(this);
    }


    protected virtual bool TryFindClosestTarget(out Unit target)
    {
        if (Time.time >= m_NextUnitDetectionTime)
        {
            m_NextUnitDetectionTime = Time.time + m_UnitDetectionCheckRate;

            target = m_GameManager.FindClosestUnit(transform.position, m_ObjectDetectionRadius, !IsPlayer);
            return target != null;
        }
        else
        {
            target = null;
            /// <summary>
            /// متغير من نوع return يستخدم في كلاس Unit لتخزين بيانات مرتبطة بوظائف الكلاس.
            /// </summary>
            return false;
        }
    }

    public void HideWorker()
    {
        m_SpriteRenderer.enabled = false;
        m_Collider.enabled = false;
    }

    public void ShowWorker()
    {
        m_SpriteRenderer.enabled = true;
        m_Collider.enabled = true;
    }

    public void MoveTo(Vector3 destination, DestinationSource source = DestinationSource.CodeTriggered)
    {
        //if (!Application.isFocused) return;
        var direction = (destination - transform.position).normalized;
        m_SpriteRenderer.flipX = direction.x < 0;

        m_AIPawn.SetDestination(destination);
        OnSetDestination(source);
    }


    public void Select()
    {
        Highlight();
        IsTargeted = true;

        for (int i = 0; i < m_ActionSO.Length; i++)
        {
            if (m_ActionSO[i] is UnitStanceActionSO unitStanceAction && unitStanceAction.UnitStance == m_CurrentStance)
            {
                m_GameManager.FocusActionUI(i);
                return;
            }
        }
    }

    public void Deselect()
    {
        UnHighlight();
        IsTargeted = false;
    }

    public void StopMovement()
    {
        m_AIPawn.Stop();
    }

    public Vector3 GetTopPosition()
    {
        if (m_Collider == null) return transform.position;
        return transform.position + Vector3.up * m_Collider.size.y / 2;
    }

    // public Vector3 GetTopPosition2()
    // {
    //     if (m_SpriteRenderer != null)
    //     {
    //         var bounds = m_SpriteRenderer.bounds;
    //         return new Vector3(bounds.center.x, bounds.max.y, bounds.center.z);
    //     }
    //     return transform.position;
    // }

    protected virtual void OnSetDestination(DestinationSource destinationSource) { }


    protected virtual void OnSetUnitTask(UnitTask oldTask, UnitTask newTask)
    {
        CurrentTask = newTask;
    }


    protected virtual void OnSetUnitState(UnitState oldState, UnitState newState)
    {
        CurrentState = newState;
    }


    protected virtual void OnDestinationReached() { }

    protected Collider2D[] RunProximityObjectDetection()
    {
        return Physics2D.OverlapCircleAll(transform.position, m_ObjectDetectionRadius);
    }

    protected virtual void OnAttakReady(Unit target)
    {
        PerformAttackAnimation();
        StartCoroutine(DelayDamage(m_AutoAttackDamageDelay, m_AutoAttackDamage, Target));
    }

    protected virtual bool TryAttackCurrentTarget()
    {
        if (Target.CurrentState == UnitState.Dead) return false;

        if (Time.time >= m_NextAutoAttackTime)
        {
            m_NextAutoAttackTime = Time.time + m_AutoAttackFrequency;

            OnAttakReady(Target);

            return true;
        }


        return false;
    }



    private Coroutine m_FlashCoroutine;

    public virtual void TackDamage(int damage, Unit damager)
    {
        if (CurrentState == UnitState.Dead) return;

        m_CurrentHealth -= damage;

        if (!HasTarget)
        {
            SetTarget(damager);
        }

        // Debug.Log($"{this.gameObject.name} took: {damage} point from {damager.gameObject.name}");
        m_GameManager.ShowTextPopup(damage.ToString(), GetTopPosition(), Color.red);

        if (m_CurrentHealth <= 0)
        {
            Die();
        }

        m_FlashCoroutine ??= StartCoroutine(FlashEffectColor(0.1f, 2, m_DamageFlashColor));

    }


    protected virtual void PerformAttackAnimation() { }

    protected virtual void RunDeadEffect() { }

    protected virtual void Die()
    {
        SetUnitState(UnitState.Dead);
        if (m_AIPawn != null)
        {
            StopMovement();
        }
        UnregisterUnit();
        RunDeadEffect();
    }

    protected IEnumerator FlashEffectColor(float duration, int flashCount, Color color)
    {

        Color originalColor = m_SpriteRenderer.color;

        for (int i = 0; i < flashCount; i++)
        {
            m_SpriteRenderer.color = color;
            yield return new WaitForSeconds(duration);
            m_SpriteRenderer.color = originalColor;
            yield return new WaitForSeconds(duration);
        }

        m_SpriteRenderer.color = originalColor;
        m_FlashCoroutine = null;

    }

    protected IEnumerator DelayDamage(float delay, int damage, Unit target)
    {
        yield return new WaitForSeconds(delay);

        if (target != null)
        {
            if (target.CurrentState == UnitState.Dead)
            {
                SetTarget(null);
            }
            else
            {
                target.TackDamage(damage, this);
            }
        }
    }

    protected bool IsTargetInRange(Unit target)
    {
        CapsuleCollider2D targetCollider = target.Collider;
        Vector2 targetClosestPoint = targetCollider.ClosestPoint(transform.position);


        return Vector3.Distance(targetClosestPoint, transform.position) <= m_AttackRange;
    }

    void TurnToPosition(Vector3 newPosition)
    {
        if (HasTarget && !IsPlayer) return;

        var direction = (newPosition - transform.position).normalized;
        m_SpriteRenderer.flipX = direction.x < 0;
    }


    void Highlight()
    {
        m_SpriteRenderer.material = m_HighlightMaterial;
    }


    void UnHighlight()
    {
        m_SpriteRenderer.material = m_OriginalMaterial;
    }


    void OnDrawGizmos()
    {
        Gizmos.color = new Color(0, 0, 1, 0.3f);
        Gizmos.DrawSphere(transform.position, m_ObjectDetectionRadius);

        Gizmos.color = new Color(1, 0, 0, 0.3f);
        Gizmos.DrawSphere(transform.position, m_AttackRange);
    }

}//end class
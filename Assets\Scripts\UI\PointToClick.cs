using UnityEngine;

/// <summary>
/// يعرض مؤشراً مرئياً عند النقر على نقطة في اللعبة. يساعد اللاعب في تحديد نقاط الوجهة والتفاعل.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class PointToClick : MonoBehaviour
{
    [SerializeField] private float m_Duration = 1f;
    [SerializeField] private SpriteRenderer m_SpriteRenderer;
    [SerializeField] private AnimationCurve m_ScaleCurve;
    private float m_Timer;
    private float m_FreqTimer;
    private Vector3 m_InitialScale;

    void Start()
    {
        m_InitialScale = transform.localScale;
    }


    void Update()
    {
        m_Timer += Time.deltaTime;
        m_FreqTimer += Time.deltaTime;

        if (m_FreqTimer >= 1f)
        {
            m_FreqTimer = 0;
        }


        float ScaleMultiplier = m_ScaleCurve.Evaluate(m_FreqTimer);
        transform.localScale = m_InitialScale * ScaleMultiplier;
        // fadeOut
        if (m_Timer >= m_Duration * 0.9f)
        {
            float fadeProgress = (m_Timer - m_Duration * 0.9f) / (m_Duration * 0.1f);
            m_SpriteRenderer.color = new Color(1, 1, 1, 1 - fadeProgress);
        }
        if (m_Timer >= m_Duration)
        {
            Destroy(gameObject);
        }
    }








}//end class
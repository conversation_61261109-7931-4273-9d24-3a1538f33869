fileFormatVersion: 2
guid: 6c4e22f78b4c227429e4dce7527a791a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 9180185992643791543
    second: Pawn_Red_0
  - first:
      213: 4801281181304304430
    second: Pawn_Red_1
  - first:
      213: -6547139239109680926
    second: Pawn_Red_2
  - first:
      213: 7367665279927838961
    second: Pawn_Red_3
  - first:
      213: -2659041373972772730
    second: Pawn_Red_4
  - first:
      213: 280982078449467795
    second: Pawn_Red_5
  - first:
      213: 1934121725140675633
    second: Pawn_Red_6
  - first:
      213: -8977658323074668451
    second: Pawn_Red_7
  - first:
      213: -2575088360240535255
    second: Pawn_Red_8
  - first:
      213: -4502589318312374030
    second: Pawn_Red_9
  - first:
      213: -8104818306435371255
    second: Pawn_Red_10
  - first:
      213: 7982996489120576284
    second: Pawn_Red_11
  - first:
      213: -7253572454762149281
    second: Pawn_Red_12
  - first:
      213: -2355214494494117060
    second: Pawn_Red_13
  - first:
      213: -4513171636334542584
    second: Pawn_Red_14
  - first:
      213: -189656830639264770
    second: Pawn_Red_15
  - first:
      213: -8848148577564039487
    second: Pawn_Red_16
  - first:
      213: 6111660008997747302
    second: Pawn_Red_17
  - first:
      213: 8210898571731170116
    second: Pawn_Red_18
  - first:
      213: 2435809698890948877
    second: Pawn_Red_19
  - first:
      213: 1065467464936038101
    second: Pawn_Red_20
  - first:
      213: 5922715457976290344
    second: Pawn_Red_21
  - first:
      213: 654995438457947119
    second: Pawn_Red_22
  - first:
      213: 6546471229822667690
    second: Pawn_Red_23
  - first:
      213: -8396562768945119553
    second: Pawn_Red_24
  - first:
      213: 5504081116300027369
    second: Pawn_Red_25
  - first:
      213: 7956463635488931906
    second: Pawn_Red_26
  - first:
      213: -9191345980618670062
    second: Pawn_Red_27
  - first:
      213: -2020780127887042515
    second: Pawn_Red_28
  - first:
      213: 4844748979538860398
    second: Pawn_Red_29
  - first:
      213: -7989760964488074029
    second: Pawn_Red_30
  - first:
      213: -8135545967999734549
    second: Pawn_Red_31
  - first:
      213: 5395221270978767216
    second: Pawn_Red_32
  - first:
      213: -7481411213992967941
    second: Pawn_Red_33
  - first:
      213: -8924324341725625487
    second: Pawn_Red_34
  - first:
      213: -8928223007732233723
    second: Pawn_Red_35
  - first:
      213: -7992214572940324251
    second: Pawn_Red_36
  - first:
      213: -1099416568045091501
    second: Pawn_Red_37
  - first:
      213: 3408630599137938645
    second: Pawn_Red_38
  - first:
      213: -366227982966077365
    second: Pawn_Red_39
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Pawn_Red_0
      rect:
        serializedVersion: 2
        x: 66
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7bacc878582966f70800000000000000
      internalID: 9180185992643791543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_1
      rect:
        serializedVersion: 2
        x: 257
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e23396ecbc391a240800000000000000
      internalID: 4801281181304304430
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_2
      rect:
        serializedVersion: 2
        x: 449
        y: 1023
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e0a8dddc83e325a0800000000000000
      internalID: -6547139239109680926
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_3
      rect:
        serializedVersion: 2
        x: 642
        y: 1023
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f8e31a59843f3660800000000000000
      internalID: 7367665279927838961
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_4
      rect:
        serializedVersion: 2
        x: 835
        y: 1023
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 688494565ff291bd0800000000000000
      internalID: -2659041373972772730
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_5
      rect:
        serializedVersion: 2
        x: 1027
        y: 1023
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3914b3e26bf36e300800000000000000
      internalID: 280982078449467795
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_6
      rect:
        serializedVersion: 2
        x: 64
        y: 831
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 13c206b267167da10800000000000000
      internalID: 1934121725140675633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_7
      rect:
        serializedVersion: 2
        x: 257
        y: 831
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d582e422253f86380800000000000000
      internalID: -8977658323074668451
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_8
      rect:
        serializedVersion: 2
        x: 450
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 929ae9e97c2734cd0800000000000000
      internalID: -2575088360240535255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_9
      rect:
        serializedVersion: 2
        x: 641
        y: 831
        width: 64
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f0d588eed69381c0800000000000000
      internalID: -4502589318312374030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_10
      rect:
        serializedVersion: 2
        x: 834
        y: 831
        width: 61
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 90f26d301b6e58f80800000000000000
      internalID: -8104818306435371255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_11
      rect:
        serializedVersion: 2
        x: 1026
        y: 831
        width: 60
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c13f839d20d49ce60800000000000000
      internalID: 7982996489120576284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_12
      rect:
        serializedVersion: 2
        x: 63
        y: 639
        width: 67
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5a08519842265b90800000000000000
      internalID: -7253572454762149281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_13
      rect:
        serializedVersion: 2
        x: 254
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c33b3dd45e8905fd0800000000000000
      internalID: -2355214494494117060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_14
      rect:
        serializedVersion: 2
        x: 446
        y: 639
        width: 67
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 80149edfe4efd51c0800000000000000
      internalID: -4513171636334542584
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_15
      rect:
        serializedVersion: 2
        x: 639
        y: 639
        width: 94
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: efb258c0e143e5df0800000000000000
      internalID: -189656830639264770
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_16
      rect:
        serializedVersion: 2
        x: 679
        y: 696
        width: 24
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c6ea6a8fbf053580800000000000000
      internalID: -8848148577564039487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_17
      rect:
        serializedVersion: 2
        x: 832
        y: 639
        width: 69
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66a7540b56af0d450800000000000000
      internalID: 6111660008997747302
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_18
      rect:
        serializedVersion: 2
        x: 884
        y: 642
        width: 39
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4437cce6fb8f2f170800000000000000
      internalID: 8210898571731170116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_19
      rect:
        serializedVersion: 2
        x: 1029
        y: 639
        width: 78
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d01a8d2040cbdc120800000000000000
      internalID: 2435809698890948877
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_20
      rect:
        serializedVersion: 2
        x: 42
        y: 447
        width: 83
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5de9f37a30d49ce00800000000000000
      internalID: 1065467464936038101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_21
      rect:
        serializedVersion: 2
        x: 228
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82c44624456b13250800000000000000
      internalID: 5922715457976290344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_22
      rect:
        serializedVersion: 2
        x: 420
        y: 447
        width: 90
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe389c681e2071900800000000000000
      internalID: 654995438457947119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_23
      rect:
        serializedVersion: 2
        x: 628
        y: 473
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aab05e916ecb9da50800000000000000
      internalID: 6546471229822667690
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_24
      rect:
        serializedVersion: 2
        x: 632
        y: 447
        width: 108
        height: 56
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbe71c606aa697b80800000000000000
      internalID: -8396562768945119553
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_25
      rect:
        serializedVersion: 2
        x: 830
        y: 461
        width: 11
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e5aff70d9c626c40800000000000000
      internalID: 5504081116300027369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_26
      rect:
        serializedVersion: 2
        x: 841
        y: 447
        width: 90
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24081a575890b6e60800000000000000
      internalID: 7956463635488931906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_27
      rect:
        serializedVersion: 2
        x: 1018
        y: 439
        width: 71
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21082894787c17080800000000000000
      internalID: -9191345980618670062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_28
      rect:
        serializedVersion: 2
        x: 66
        y: 255
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d28df142e2fb4f3e0800000000000000
      internalID: -2020780127887042515
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_29
      rect:
        serializedVersion: 2
        x: 257
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e69995f37810c3340800000000000000
      internalID: 4844748979538860398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_30
      rect:
        serializedVersion: 2
        x: 449
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3dce76ffbbaae1190800000000000000
      internalID: -7989760964488074029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_31
      rect:
        serializedVersion: 2
        x: 642
        y: 255
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: beca38bcb0cb81f80800000000000000
      internalID: -8135545967999734549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_32
      rect:
        serializedVersion: 2
        x: 835
        y: 255
        width: 60
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 079225d292dafda40800000000000000
      internalID: 5395221270978767216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_33
      rect:
        serializedVersion: 2
        x: 1027
        y: 255
        width: 60
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf0435b9320bc2890800000000000000
      internalID: -7481411213992967941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_34
      rect:
        serializedVersion: 2
        x: 64
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17382175b4e662480800000000000000
      internalID: -8924324341725625487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_35
      rect:
        serializedVersion: 2
        x: 257
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50a86ac6a74981480800000000000000
      internalID: -8928223007732233723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_36
      rect:
        serializedVersion: 2
        x: 450
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5621133c033f51190800000000000000
      internalID: -7992214572940324251
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_37
      rect:
        serializedVersion: 2
        x: 644
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3554e71d4761eb0f0800000000000000
      internalID: -1099416568045091501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_38
      rect:
        serializedVersion: 2
        x: 835
        y: 63
        width: 60
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5dcc4206873ed4f20800000000000000
      internalID: 3408630599137938645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Pawn_Red_39
      rect:
        serializedVersion: 2
        x: 1026
        y: 63
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b48cf700795eaeaf0800000000000000
      internalID: -366227982966077365
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Pawn_Red_0: 9180185992643791543
      Pawn_Red_1: 4801281181304304430
      Pawn_Red_10: -8104818306435371255
      Pawn_Red_11: 7982996489120576284
      Pawn_Red_12: -7253572454762149281
      Pawn_Red_13: -2355214494494117060
      Pawn_Red_14: -4513171636334542584
      Pawn_Red_15: -189656830639264770
      Pawn_Red_16: -8848148577564039487
      Pawn_Red_17: 6111660008997747302
      Pawn_Red_18: 8210898571731170116
      Pawn_Red_19: 2435809698890948877
      Pawn_Red_2: -6547139239109680926
      Pawn_Red_20: 1065467464936038101
      Pawn_Red_21: 5922715457976290344
      Pawn_Red_22: 654995438457947119
      Pawn_Red_23: 6546471229822667690
      Pawn_Red_24: -8396562768945119553
      Pawn_Red_25: 5504081116300027369
      Pawn_Red_26: 7956463635488931906
      Pawn_Red_27: -9191345980618670062
      Pawn_Red_28: -2020780127887042515
      Pawn_Red_29: 4844748979538860398
      Pawn_Red_3: 7367665279927838961
      Pawn_Red_30: -7989760964488074029
      Pawn_Red_31: -8135545967999734549
      Pawn_Red_32: 5395221270978767216
      Pawn_Red_33: -7481411213992967941
      Pawn_Red_34: -8924324341725625487
      Pawn_Red_35: -8928223007732233723
      Pawn_Red_36: -7992214572940324251
      Pawn_Red_37: -1099416568045091501
      Pawn_Red_38: 3408630599137938645
      Pawn_Red_39: -366227982966077365
      Pawn_Red_4: -2659041373972772730
      Pawn_Red_5: 280982078449467795
      Pawn_Red_6: 1934121725140675633
      Pawn_Red_7: -8977658323074668451
      Pawn_Red_8: -2575088360240535255
      Pawn_Red_9: -4502589318312374030
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

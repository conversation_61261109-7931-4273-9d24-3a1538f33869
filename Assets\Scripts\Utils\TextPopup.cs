using TMPro;
using UnityEngine;

/// <summary>
/// يعرض نصاً منبثقاً مؤقتاً في اللعبة، مثل الأضرار أو الإشعارات. يتحكم في ظهور واختفاء النص وحركته.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class TextPopup : MonoBehaviour
{

    [SerializeField] private TextMeshProUGUI m_Text;
    [SerializeField] private float m_Duration = 1f;

    [Header("Animation Setting")]
    [SerializeField] private AnimationCurve m_FontSizeCurve;
    [SerializeField] private AnimationCurve m_XOffsetCurve;
    [SerializeField] private AnimationCurve m_YOffsetCurve;
    [SerializeField] private AnimationCurve m_AlphaCurve;


    private float m_ElapsedTime;



    public void SetText(string text, Color color)
    {
        m_Text.text = text;
        m_Text.color = color;
    }

    /// <summary>
    /// تُستدعى هذه الدالة مرة واحدة في كل إطار، وتستخدم لتحديث حالة الكائن وتنفيذ المنطق المستمر.
    /// </summary>
    void Update()
    {
        m_ElapsedTime += Time.deltaTime;
        /// <summary>
        /// متغير من نوع var يستخدم في كلاس TextPopup لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var normaizedTime = m_ElapsedTime / m_Duration;

        if (normaizedTime >= 1)
        {
            Destroy(gameObject);
            return;
        }

        /// <summary>
        /// متغير من نوع var يستخدم في كلاس TextPopup لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var alpha = m_AlphaCurve.Evaluate(normaizedTime);
        m_Text.fontSize += m_FontSizeCurve.Evaluate(normaizedTime) / 4;
        m_Text.color = new Color(m_Text.color.r, m_Text.color.g, m_Text.color.b, alpha);
        /// <summary>
        /// متغير من نوع float يستخدم في كلاس TextPopup لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        float xOffset = m_XOffsetCurve.Evaluate(normaizedTime);
        /// <summary>
        /// متغير من نوع float يستخدم في كلاس TextPopup لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        float yOffset = m_YOffsetCurve.Evaluate(normaizedTime);

        transform.position += new Vector3(xOffset, yOffset, 0) * Time.deltaTime;
    }

}// end class

using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

/// <summary>
/// يعرض شريط تأكيد للاعب عند اتخاذ قرارات مهمة، مثل بناء هيكل جديد. يعرض متطلبات الموارد ويوفر خيارات التأكيد أو الإلغاء.
/// </summary>
/// <remarks>
/// يرث من: MonoBehaviour
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class ConfirmationBar : MonoBehaviour
{
    [SerializeField] private ResourceRequirementDisplay m_ResourceRequirementDisplay;
    [SerializeField] private Button m_ConfirmButton;
    [SerializeField] private Button m_CancleButton;


    public void Show(int gold, int wood)
    {
        gameObject.SetActive(true);
        m_ResourceRequirementDisplay.Show(gold, wood);
    }

    /// <summary>
    /// تُستدعى هذه الدالة عندما يتم تعطيل السكريبت أو الكائن، وتستخدم لإيقاف العمليات المستمرة مؤقتاً.
    /// </summary>
    void OnDisable()
    {
        m_ConfirmButton.onClick.RemoveAllListeners();
        m_CancleButton.onClick.RemoveAllListeners();
    }

    public void Hide()
    {
        gameObject.SetActive(false);
    }


    public void SetupHooks(UnityAction onConfirm, UnityAction onCancle)
    {
        m_ConfirmButton.onClick.AddListener(onConfirm);
        m_CancleButton.onClick.AddListener(onCancle);
    }







}// end class

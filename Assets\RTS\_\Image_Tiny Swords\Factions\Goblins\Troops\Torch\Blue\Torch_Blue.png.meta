fileFormatVersion: 2
guid: a3ec87cd42ce5484aa5a6478d9b8dc43
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5849275302159542619
    second: Torch_<PERSON>_0
  - first:
      213: 192938113489121057
    second: Torch_Blue_1
  - first:
      213: 1175497388384161881
    second: Torch_Blue_2
  - first:
      213: -1585621617373756857
    second: Torch_Blue_3
  - first:
      213: -3237992886719057245
    second: Torch_Blue_4
  - first:
      213: -3690140458019582694
    second: Torch_Blue_5
  - first:
      213: 7663593417367887922
    second: Torch_Blue_6
  - first:
      213: -7833860523421712459
    second: Torch_Blue_7
  - first:
      213: -7199191049690257617
    second: Torch_Blue_8
  - first:
      213: -3399481695822269215
    second: Torch_<PERSON>_9
  - first:
      213: 4689512353232432473
    second: Torch_Blue_10
  - first:
      213: -7605220311582029551
    second: Torch_Blue_11
  - first:
      213: -470840644977159979
    second: Torch_Blue_12
  - first:
      213: 6516102808112660780
    second: Torch_Blue_13
  - first:
      213: -2742168057741967382
    second: Torch_Blue_14
  - first:
      213: 1272977271935326613
    second: Torch_Blue_15
  - first:
      213: -415065218717458040
    second: Torch_Blue_16
  - first:
      213: -3362385131632451062
    second: Torch_Blue_17
  - first:
      213: -7961792136640337584
    second: Torch_Blue_18
  - first:
      213: 3048966563036577355
    second: Torch_Blue_19
  - first:
      213: -6467177992834761142
    second: Torch_Blue_20
  - first:
      213: 2692088972368740212
    second: Torch_Blue_21
  - first:
      213: 7757144839608658827
    second: Torch_Blue_22
  - first:
      213: -2699249062053756948
    second: Torch_Blue_23
  - first:
      213: -1285740013562734629
    second: Torch_Blue_24
  - first:
      213: -5766059923197518857
    second: Torch_Blue_25
  - first:
      213: 2427653798115428020
    second: Torch_Blue_26
  - first:
      213: -8860286924835361318
    second: Torch_Blue_27
  - first:
      213: -8573584020412557223
    second: Torch_Blue_28
  - first:
      213: 9055141348980685375
    second: Torch_Blue_29
  - first:
      213: -4786530533210131120
    second: Torch_Blue_30
  - first:
      213: -3035962467897627866
    second: Torch_Blue_31
  - first:
      213: 4475720910524164274
    second: Torch_Blue_32
  - first:
      213: 6950116785800317334
    second: Torch_Blue_33
  - first:
      213: 3201367108746774885
    second: Torch_Blue_34
  - first:
      213: 8049934803706740518
    second: Torch_Blue_35
  - first:
      213: 2389710942775769668
    second: Torch_Blue_36
  - first:
      213: -6059486733713468257
    second: Torch_Blue_37
  - first:
      213: -444127194684849767
    second: Torch_Blue_38
  - first:
      213: -519774619599582941
    second: Torch_Blue_39
  - first:
      213: -4768984577978641304
    second: Torch_Blue_40
  - first:
      213: 8950583711140059739
    second: Torch_Blue_41
  - first:
      213: 5628216108597419302
    second: Torch_Blue_42
  - first:
      213: -7912548979501125525
    second: Torch_Blue_43
  - first:
      213: -4096542991824046365
    second: Torch_Blue_44
  - first:
      213: -1085147601210782896
    second: Torch_Blue_45
  - first:
      213: -6847097540358802152
    second: Torch_Blue_46
  - first:
      213: 8972653976144969498
    second: Torch_Blue_47
  - first:
      213: -3279268926528135470
    second: Torch_Blue_48
  - first:
      213: 2638038486107280292
    second: Torch_Blue_49
  - first:
      213: 3812035378338756190
    second: Torch_Blue_50
  - first:
      213: 2997510652047136156
    second: Torch_Blue_51
  - first:
      213: 2759425026998719389
    second: Torch_Blue_52
  - first:
      213: 3652036102431667876
    second: Torch_Blue_53
  - first:
      213: -7893955301520406316
    second: Torch_Blue_54
  - first:
      213: 2946303737009136156
    second: Torch_Blue_55
  - first:
      213: -692465695238008587
    second: Torch_Blue_56
  - first:
      213: -3054403832069814882
    second: Torch_Blue_57
  - first:
      213: 9050436241686843561
    second: Torch_Blue_58
  - first:
      213: 1403234360409947878
    second: Torch_Blue_59
  - first:
      213: -6926298296411771073
    second: Torch_Blue_60
  - first:
      213: 740580812119642275
    second: Torch_Blue_61
  - first:
      213: 4534082686762586448
    second: Torch_Blue_62
  - first:
      213: 8643522831683519424
    second: Torch_Blue_63
  - first:
      213: -5899591039789758371
    second: Torch_Blue_64
  - first:
      213: 6253684429771434547
    second: Torch_Blue_65
  - first:
      213: 1629697497092787325
    second: Torch_Blue_66
  - first:
      213: -7224211414560640898
    second: Torch_Blue_67
  - first:
      213: 4282104525516467258
    second: Torch_Blue_68
  - first:
      213: 9033607718057917697
    second: Torch_Blue_69
  - first:
      213: 1833702339620649975
    second: Torch_Blue_70
  - first:
      213: -9087119956605864391
    second: Torch_Blue_71
  - first:
      213: 2484998350844414861
    second: Torch_Blue_72
  - first:
      213: 5897143128020793576
    second: Torch_Blue_73
  - first:
      213: 4102384337957012463
    second: Torch_Blue_74
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Torch_Blue_0
      rect:
        serializedVersion: 2
        x: 52
        y: 826
        width: 76
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b596fa285eccc2150800000000000000
      internalID: 5849275302159542619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_1
      rect:
        serializedVersion: 2
        x: 242
        y: 826
        width: 77
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12f272621347da200800000000000000
      internalID: 192938113489121057
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_2
      rect:
        serializedVersion: 2
        x: 435
        y: 826
        width: 76
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 950990d85a4305010800000000000000
      internalID: 1175497388384161881
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_3
      rect:
        serializedVersion: 2
        x: 450
        y: 904
        width: 10
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 742c14c3a8dbef9e0800000000000000
      internalID: -1585621617373756857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_4
      rect:
        serializedVersion: 2
        x: 629
        y: 826
        width: 74
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a27ed900a65013d0800000000000000
      internalID: -3237992886719057245
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_5
      rect:
        serializedVersion: 2
        x: 630
        y: 902
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1d027cfdccf9ccc0800000000000000
      internalID: -3690140458019582694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_6
      rect:
        serializedVersion: 2
        x: 822
        y: 897
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2302c311c9d8a5a60800000000000000
      internalID: 7663593417367887922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_7
      rect:
        serializedVersion: 2
        x: 828
        y: 826
        width: 67
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bf3e790c59884390800000000000000
      internalID: -7833860523421712459
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_8
      rect:
        serializedVersion: 2
        x: 1014
        y: 903
        width: 8
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f276a0d12e5571c90800000000000000
      internalID: -7199191049690257617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_9
      rect:
        serializedVersion: 2
        x: 1018
        y: 826
        width: 69
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e4321ca86d92d0d0800000000000000
      internalID: -3399481695822269215
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_10
      rect:
        serializedVersion: 2
        x: 1207
        y: 826
        width: 73
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95da930a3ae741140800000000000000
      internalID: 4689512353232432473
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_11
      rect:
        serializedVersion: 2
        x: 18
        y: 697
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1194199bb64d47690800000000000000
      internalID: -7605220311582029551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_12
      rect:
        serializedVersion: 2
        x: 27
        y: 634
        width: 101
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d012529dec3779f0800000000000000
      internalID: -470840644977159979
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_13
      rect:
        serializedVersion: 2
        x: 219
        y: 696
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c210b7deaf8dd6a50800000000000000
      internalID: 6516102808112660780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_14
      rect:
        serializedVersion: 2
        x: 224
        y: 634
        width: 96
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aeffddb1eacd1f9d0800000000000000
      internalID: -2742168057741967382
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_15
      rect:
        serializedVersion: 2
        x: 403
        y: 694
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 599d9d495168aa110800000000000000
      internalID: 1272977271935326613
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_16
      rect:
        serializedVersion: 2
        x: 416
        y: 634
        width: 94
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 881e9b7f1646d3af0800000000000000
      internalID: -415065218717458040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_17
      rect:
        serializedVersion: 2
        x: 594
        y: 697
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a022ef699886651d0800000000000000
      internalID: -3362385131632451062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_18
      rect:
        serializedVersion: 2
        x: 603
        y: 634
        width: 104
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 059ed003c38028190800000000000000
      internalID: -7961792136640337584
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_19
      rect:
        serializedVersion: 2
        x: 795
        y: 696
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4eb6d95fea105a20800000000000000
      internalID: 3048966563036577355
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_20
      rect:
        serializedVersion: 2
        x: 800
        y: 634
        width: 97
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4e3cdd41e7ff36a0800000000000000
      internalID: -6467177992834761142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_21
      rect:
        serializedVersion: 2
        x: 979
        y: 694
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47ff8c538a83c5520800000000000000
      internalID: 2692088972368740212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_22
      rect:
        serializedVersion: 2
        x: 992
        y: 634
        width: 94
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8be02ce12ae6ab60800000000000000
      internalID: 7757144839608658827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_23
      rect:
        serializedVersion: 2
        x: 230
        y: 442
        width: 88
        height: 104
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cefbaff77475a8ad0800000000000000
      internalID: -2699249062053756948
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_24
      rect:
        serializedVersion: 2
        x: 418
        y: 442
        width: 92
        height: 111
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd756121542282ee0800000000000000
      internalID: -1285740013562734629
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_25
      rect:
        serializedVersion: 2
        x: 614
        y: 532
        width: 17
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fb123cbc07daffa0800000000000000
      internalID: -5766059923197518857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_26
      rect:
        serializedVersion: 2
        x: 807
        y: 541
        width: 12
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b64c426442c0b120800000000000000
      internalID: 2427653798115428020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_27
      rect:
        serializedVersion: 2
        x: 1002
        y: 546
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: add66255cffe90580800000000000000
      internalID: -8860286924835361318
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_28
      rect:
        serializedVersion: 2
        x: 43
        y: 442
        width: 82
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 958185b12c2840980800000000000000
      internalID: -8573584020412557223
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_29
      rect:
        serializedVersion: 2
        x: 62
        y: 532
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3af1f8a4135aad70800000000000000
      internalID: 9055141348980685375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_30
      rect:
        serializedVersion: 2
        x: 623
        y: 521
        width: 26
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 055f27517d3d29db0800000000000000
      internalID: -4786530533210131120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_31
      rect:
        serializedVersion: 2
        x: 630
        y: 441
        width: 123
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62b2b8d78381ed5d0800000000000000
      internalID: -3035962467897627866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_32
      rect:
        serializedVersion: 2
        x: 818
        y: 527
        width: 12
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b8a4996474fc1e30800000000000000
      internalID: 4475720910524164274
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_33
      rect:
        serializedVersion: 2
        x: 837
        y: 529
        width: 27
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6998b15b466c37060800000000000000
      internalID: 6950116785800317334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_34
      rect:
        serializedVersion: 2
        x: 865
        y: 525
        width: 39
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56988708c6a8d6c20800000000000000
      internalID: 3201367108746774885
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_35
      rect:
        serializedVersion: 2
        x: 1032
        y: 530
        width: 20
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62f7ef7bd0d17bf60800000000000000
      internalID: 8049934803706740518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_36
      rect:
        serializedVersion: 2
        x: 1070
        y: 529
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44a7304f075f92120800000000000000
      internalID: 2389710942775769668
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_37
      rect:
        serializedVersion: 2
        x: 904
        y: 514
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f9411d39be068eba0800000000000000
      internalID: -6059486733713468257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_38
      rect:
        serializedVersion: 2
        x: 839
        y: 440
        width: 105
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 99108b56ba426d9f0800000000000000
      internalID: -444127194684849767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_39
      rect:
        serializedVersion: 2
        x: 1030
        y: 442
        width: 83
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 329c083ccb369c8f0800000000000000
      internalID: -519774619599582941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_40
      rect:
        serializedVersion: 2
        x: 1119
        y: 478
        width: 7
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86454422bc921ddb0800000000000000
      internalID: -4768984577978641304
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_41
      rect:
        serializedVersion: 2
        x: 1103
        y: 444
        width: 21
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5a96ea867cd63c70800000000000000
      internalID: 8950583711140059739
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_42
      rect:
        serializedVersion: 2
        x: 1118
        y: 458
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62d94103bb07b1e40800000000000000
      internalID: 5628216108597419302
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_43
      rect:
        serializedVersion: 2
        x: 46
        y: 250
        width: 81
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b60b4f820aaf03290800000000000000
      internalID: -7912548979501125525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_44
      rect:
        serializedVersion: 2
        x: 232
        y: 250
        width: 88
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e636357ad72627c0800000000000000
      internalID: -4096542991824046365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_45
      rect:
        serializedVersion: 2
        x: 421
        y: 250
        width: 91
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05bc6fc8108c0f0f0800000000000000
      internalID: -1085147601210782896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_46
      rect:
        serializedVersion: 2
        x: 617
        y: 323
        width: 11
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 815767480193af0a0800000000000000
      internalID: -6847097540358802152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_47
      rect:
        serializedVersion: 2
        x: 620
        y: 319
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1ffd9c8045458c70800000000000000
      internalID: 8972653976144969498
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_48
      rect:
        serializedVersion: 2
        x: 810
        y: 325
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2de505f1942bd72d0800000000000000
      internalID: -3279268926528135470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_49
      rect:
        serializedVersion: 2
        x: 612
        y: 268
        width: 15
        height: 51
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a3db34b6023c9420800000000000000
      internalID: 2638038486107280292
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_50
      rect:
        serializedVersion: 2
        x: 617
        y: 220
        width: 113
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e567a15caf117e430800000000000000
      internalID: 3812035378338756190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_51
      rect:
        serializedVersion: 2
        x: 807
        y: 301
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9518b97e0c499920800000000000000
      internalID: 2997510652047136156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_52
      rect:
        serializedVersion: 2
        x: 819
        y: 214
        width: 107
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d93d49621727b4620800000000000000
      internalID: 2759425026998719389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_53
      rect:
        serializedVersion: 2
        x: 1026
        y: 244
        width: 93
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a677506c73aea230800000000000000
      internalID: 3652036102431667876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_54
      rect:
        serializedVersion: 2
        x: 805
        y: 280
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d8801c2a79037290800000000000000
      internalID: -7893955301520406316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_55
      rect:
        serializedVersion: 2
        x: 816
        y: 259
        width: 6
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c12e5dc73af53e820800000000000000
      internalID: 2946303737009136156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_56
      rect:
        serializedVersion: 2
        x: 1015
        y: 241
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f0d1387e1ed366f0800000000000000
      internalID: -692465695238008587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_57
      rect:
        serializedVersion: 2
        x: 1098
        y: 236
        width: 15
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e9503ecc5e39c95d0800000000000000
      internalID: -3054403832069814882
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_58
      rect:
        serializedVersion: 2
        x: 1026
        y: 224
        width: 17
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a0d0108fcb999d70800000000000000
      internalID: 9050436241686843561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_59
      rect:
        serializedVersion: 2
        x: 1053
        y: 218
        width: 24
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ee7bd4573a497310800000000000000
      internalID: 1403234360409947878
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_60
      rect:
        serializedVersion: 2
        x: 1087
        y: 224
        width: 14
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3fabf18568d0ef90800000000000000
      internalID: -6926298296411771073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_61
      rect:
        serializedVersion: 2
        x: 588
        y: 60
        width: 141
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a09cd92352174a00800000000000000
      internalID: 740580812119642275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_62
      rect:
        serializedVersion: 2
        x: 780
        y: 59
        width: 109
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0519bb65e2c4cee30800000000000000
      internalID: 4534082686762586448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_63
      rect:
        serializedVersion: 2
        x: 1003
        y: 141
        width: 18
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c389765836f3f770800000000000000
      internalID: 8643522831683519424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_64
      rect:
        serializedVersion: 2
        x: 1020
        y: 145
        width: 26
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d580149e431702ea0800000000000000
      internalID: -5899591039789758371
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_65
      rect:
        serializedVersion: 2
        x: 1055
        y: 142
        width: 12
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33aeaedfadc89c650800000000000000
      internalID: 6253684429771434547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_66
      rect:
        serializedVersion: 2
        x: 67
        y: 58
        width: 93
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7c0f853d39dd9610800000000000000
      internalID: 1629697497092787325
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_67
      rect:
        serializedVersion: 2
        x: 256
        y: 43
        width: 96
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e707ac47ef17ebb90800000000000000
      internalID: -7224211414560640898
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_68
      rect:
        serializedVersion: 2
        x: 448
        y: 40
        width: 93
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3c608ed0671d6b30800000000000000
      internalID: 4282104525516467258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_69
      rect:
        serializedVersion: 2
        x: 872
        y: 119
        width: 25
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10db1138b52dd5d70800000000000000
      internalID: 9033607718057917697
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_70
      rect:
        serializedVersion: 2
        x: 974
        y: 58
        width: 118
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fb34e0009e927910800000000000000
      internalID: 1833702339620649975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_71
      rect:
        serializedVersion: 2
        x: 902
        y: 107
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 936f42b8b8014e180800000000000000
      internalID: -9087119956605864391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_72
      rect:
        serializedVersion: 2
        x: 912
        y: 71
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8b438185dc7c7220800000000000000
      internalID: 2484998350844414861
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_73
      rect:
        serializedVersion: 2
        x: 713
        y: 46
        width: 17
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e8b0573e6cd6d150800000000000000
      internalID: 5897143128020793576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Blue_74
      rect:
        serializedVersion: 2
        x: 911
        y: 50
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe3cd0ce1d89ee830800000000000000
      internalID: 4102384337957012463
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Torch_Blue_0: 5849275302159542619
      Torch_Blue_1: 192938113489121057
      Torch_Blue_10: 4689512353232432473
      Torch_Blue_11: -7605220311582029551
      Torch_Blue_12: -470840644977159979
      Torch_Blue_13: 6516102808112660780
      Torch_Blue_14: -2742168057741967382
      Torch_Blue_15: 1272977271935326613
      Torch_Blue_16: -415065218717458040
      Torch_Blue_17: -3362385131632451062
      Torch_Blue_18: -7961792136640337584
      Torch_Blue_19: 3048966563036577355
      Torch_Blue_2: 1175497388384161881
      Torch_Blue_20: -6467177992834761142
      Torch_Blue_21: 2692088972368740212
      Torch_Blue_22: 7757144839608658827
      Torch_Blue_23: -2699249062053756948
      Torch_Blue_24: -1285740013562734629
      Torch_Blue_25: -5766059923197518857
      Torch_Blue_26: 2427653798115428020
      Torch_Blue_27: -8860286924835361318
      Torch_Blue_28: -8573584020412557223
      Torch_Blue_29: 9055141348980685375
      Torch_Blue_3: -1585621617373756857
      Torch_Blue_30: -4786530533210131120
      Torch_Blue_31: -3035962467897627866
      Torch_Blue_32: 4475720910524164274
      Torch_Blue_33: 6950116785800317334
      Torch_Blue_34: 3201367108746774885
      Torch_Blue_35: 8049934803706740518
      Torch_Blue_36: 2389710942775769668
      Torch_Blue_37: -6059486733713468257
      Torch_Blue_38: -444127194684849767
      Torch_Blue_39: -519774619599582941
      Torch_Blue_4: -3237992886719057245
      Torch_Blue_40: -4768984577978641304
      Torch_Blue_41: 8950583711140059739
      Torch_Blue_42: 5628216108597419302
      Torch_Blue_43: -7912548979501125525
      Torch_Blue_44: -4096542991824046365
      Torch_Blue_45: -1085147601210782896
      Torch_Blue_46: -6847097540358802152
      Torch_Blue_47: 8972653976144969498
      Torch_Blue_48: -3279268926528135470
      Torch_Blue_49: 2638038486107280292
      Torch_Blue_5: -3690140458019582694
      Torch_Blue_50: 3812035378338756190
      Torch_Blue_51: 2997510652047136156
      Torch_Blue_52: 2759425026998719389
      Torch_Blue_53: 3652036102431667876
      Torch_Blue_54: -7893955301520406316
      Torch_Blue_55: 2946303737009136156
      Torch_Blue_56: -692465695238008587
      Torch_Blue_57: -3054403832069814882
      Torch_Blue_58: 9050436241686843561
      Torch_Blue_59: 1403234360409947878
      Torch_Blue_6: 7663593417367887922
      Torch_Blue_60: -6926298296411771073
      Torch_Blue_61: 740580812119642275
      Torch_Blue_62: 4534082686762586448
      Torch_Blue_63: 8643522831683519424
      Torch_Blue_64: -5899591039789758371
      Torch_Blue_65: 6253684429771434547
      Torch_Blue_66: 1629697497092787325
      Torch_Blue_67: -7224211414560640898
      Torch_Blue_68: 4282104525516467258
      Torch_Blue_69: 9033607718057917697
      Torch_Blue_7: -7833860523421712459
      Torch_Blue_70: 1833702339620649975
      Torch_Blue_71: -9087119956605864391
      Torch_Blue_72: 2484998350844414861
      Torch_Blue_73: 5897143128020793576
      Torch_Blue_74: 4102384337957012463
      Torch_Blue_8: -7199191049690257617
      Torch_Blue_9: -3399481695822269215
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

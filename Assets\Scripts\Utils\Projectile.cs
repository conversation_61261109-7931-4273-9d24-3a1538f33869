using Unity.VisualScripting;
using UnityEngine;


public class Projectile : MonoBehaviour
{
    [SerializeField] private float m_Speed = 10f;
    [SerializeField] private bool m_EnabeDynametRotation = false;
    private int m_Damage = 10;


    private Unit m_Target;
    private Unit m_Owner;

    public void Initialize(Unit owner, Unit target, int damage)
    {
        m_Owner = owner;
        m_Target = target;
        m_Damage = damage;

    }


    void Update()
    {
        if (m_Target == null || m_Target.CurrentState == UnitState.Dead)
        {
            Destroy(gameObject);
            return;
        }

        Vector3 direction = (m_Target.transform.position - transform.position).normalized;
        float angel;
        if (m_EnabeDynametRotation)
        {
            float currentRotation = transform.eulerAngles.z;
            angel = currentRotation + 720 * Time.deltaTime;
        }
        else
        {

            angel = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        }


        transform.rotation = Quaternion.Euler(0, 0, angel);
        transform.position += m_Speed * Time.deltaTime * direction;

    }

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.TryGetComponent<Unit>(out var targetUnit))
        {
            if (targetUnit == m_Target)
            {
                targetUnit.TackDamage(m_Damage, m_Owner);
                Destroy(gameObject);
            }
        }
    }

}
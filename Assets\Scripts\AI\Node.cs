using UnityEngine;
/// <summary>
/// يمثل عقدة في شبكة البحث عن المسار، مع معلومات عن الموقع والحالة. يستخدم في خوارزمية البحث عن المسار لتحديد المسارات الممكنة.
/// </summary>
/// <remarks>
/// يرث من: None
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class Node
{

    public int x;
    public int y;
    public float centerX;
    public float centerY;
    public bool isWalkable;


    public float gCost;
    public float hCost;
    public float fCost;
    public Node parent;



    public Node(Vector3Int position, Vector3 cellSize, bool isWalkable)
    {
        x = position.x;
        y = position.y;

        /// <summary>
        /// متغير من نوع Vector3 يستخدم في كلاس Node لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        Vector3 halfCellSize = cellSize / 2;
        /// <summary>
        /// متغير من نوع var يستخدم في كلاس Node لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var nodeCenterPosition = position + halfCellSize;
        centerX = nodeCenterPosition.x;
        centerY = nodeCenterPosition.y;
        this.isWalkable = isWalkable;
    }


    /// <summary>
    /// تنفذ دالة ToString وظيفة محددة في كلاس Node.
    /// </summary>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    public override string ToString()
    {
        return $"({x}, {y})";
    }


}//end class
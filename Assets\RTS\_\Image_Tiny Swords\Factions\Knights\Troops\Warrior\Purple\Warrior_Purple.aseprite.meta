fileFormatVersion: 2
guid: 0b43f09eeb3f1674799f7daf61f4f6d1
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 809
      y: 479
      width: 78
      height: 91
    spriteID: 9ee7f15378f457d468c21716ded48bfc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 809, y: 479}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 895
      y: 479
      width: 78
      height: 91
    spriteID: 7264a732792011f4480e0be2a53f47ee
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 895, y: 479}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 241
      y: 590
      width: 78
      height: 91
    spriteID: 8a520b3a8a83d4b4599d97f774e5cce8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 241, y: 590}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.42500004, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 721
      y: 479
      width: 80
      height: 89
    spriteID: 92687fa315592e84f95b3ad79efbb6df
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 721, y: 479}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.42500004, y: -0.6436781}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 413
      y: 590
      width: 80
      height: 87
    spriteID: 8afcb4943465d074ba4861dcee2321ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 413, y: 590}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.42307693, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 501
      y: 590
      width: 78
      height: 88
    spriteID: d0fe8823f3f63be44bcb118ef9ccdd8b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 501, y: 590}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.42307693, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 327
      y: 590
      width: 78
      height: 91
    spriteID: c1e740266a126dd47b5d1c5c00ccfa7e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 327, y: 590}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.41666672, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 956
      y: 129
      width: 60
      height: 93
    spriteID: 3a2fdcb7cf000ff418da4a04824f44b8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 956, y: 129}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.40579712, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 661
      y: 590
      width: 69
      height: 94
    spriteID: 0b4c97e1f4ee8824cb1528ef8cae4ffb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 661, y: 590}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.41025642, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 825
      y: 590
      width: 78
      height: 80
    spriteID: 1d032bc71819da247844ae0dfded8881
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 825, y: 590}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44210526, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 538
      y: 274
      width: 95
      height: 93
    spriteID: c30dd2df706ed0d4b9ca48d908b746d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 538, y: 274}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.43820223, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 641
      y: 274
      width: 89
      height: 94
    spriteID: 2b2816a61825b3a479d9834cdf744e1f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 641, y: 274}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.4177215, y: -0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 590
      width: 79
      height: 80
    spriteID: 400ecd290bbcebc4a86e23e87ad3599f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 590}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5352112, y: -0.5185185}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 590
      width: 71
      height: 108
    spriteID: 00754199c2286344d91674ffb362f930
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 590}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 590
      width: 71
      height: 112
    spriteID: 67ec9e45e33ad4a4694da9f07dae162b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 590}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5492958, y: -0.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 83
      y: 590
      width: 71
      height: 112
    spriteID: eeaf11e234793bb41b8cdd68755304bf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 83, y: 590}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.18691586, y: -0.49107146}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 858
      y: 4
      width: 107
      height: 112
    spriteID: 405ade6c3646bb44485cacd313b01175
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 858, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.20560749, y: -0.57608694}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 841
      y: 129
      width: 107
      height: 92
    spriteID: 3bca12379e42df341ac97fc00190493c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 841, y: 129}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.28999996, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 430
      y: 274
      width: 100
      height: 89
    spriteID: 4497a531b4825394bb4263587420c010
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 430, y: 274}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.75609756, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 631
      y: 479
      width: 82
      height: 89
    spriteID: 4b6d033341409534c910576972e2075c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 631, y: 479}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 359
      y: 479
      width: 84
      height: 89
    spriteID: bf0fd8ef387325446b9347d581dfab00
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 359, y: 479}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.7619048, y: -0.62921345}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 451
      y: 479
      width: 84
      height: 89
    spriteID: f862d28dc356f4546a7b85dc05b4eeeb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 451, y: 479}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.30894306, y: -0.45999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 593
      y: 4
      width: 123
      height: 100
    spriteID: ccc898e9b2c72f0448245cf64f0135ab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 593, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.29508197, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 508
      y: 129
      width: 122
      height: 94
    spriteID: db7e1d4bbc434ba45b5e5a1e5dcfa746
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 508, y: 129}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.40000004, y: -0.6363636}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 916
      y: 274
      width: 85
      height: 88
    spriteID: 65bfef9d7d4a38b4ea100a4851a67314
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 916, y: 274}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.4871795, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 186
      y: 479
      width: 78
      height: 101
    spriteID: b0972ec4d559ac242987195f5703ffd3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 186, y: 479}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 479
      width: 83
      height: 103
    spriteID: 00440bb33e2e3d4448ee3a40f5b179fc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 479}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.49397585, y: -0.5436893}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 95
      y: 479
      width: 83
      height: 103
    spriteID: 8fe5416c2c24cd041ae09ff80d0695b3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 95, y: 479}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.42372882, y: -0.09489051}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 129
      width: 118
      height: 137
    spriteID: a4ee8a9561127c94da89e443fdc11ec3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 129}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.6179775, y: -0.20161289}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 638
      y: 129
      width: 89
      height: 124
    spriteID: afb0788dcfb867f42afeb1674c5fa388
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 638, y: 129}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.6363636, y: -0.5252526}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 587
      y: 590
      width: 66
      height: 99
    spriteID: 1f93d995d6b0a6e4880e79caf40273a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 587, y: 590}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.75, y: -0.6021505}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 543
      y: 479
      width: 80
      height: 93
    spriteID: 6fa282792e468fb458f8e13bff33b5d1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 543, y: 479}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 738
      y: 274
      width: 81
      height: 94
    spriteID: 974e9c4f0572e6549bd8aab0fe6a707a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 738, y: 274}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.7530864, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 827
      y: 274
      width: 81
      height: 94
    spriteID: c835efcdba5bb63409e2c89b8f85019d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 827, y: 274}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.39041096, y: -0.23931624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 146
      height: 117
    spriteID: 0789d1d5d6f322f47840f812f84b046f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.24137934, y: -0.24786326}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 469
      y: 4
      width: 116
      height: 117
    spriteID: 2aea81ed6bdd4d64695a41a46bd67791
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 469, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.34343436, y: -0.59574467}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 274
      width: 99
      height: 94
    spriteID: bc10b846ec4932b4db036fd0250204c9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 274}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.65254235, y: -0.56565654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 382
      y: 129
      width: 118
      height: 99
    spriteID: bc56f4f7af205c44a8af7928dd001217
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 382, y: 129}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 130
      y: 129
      width: 118
      height: 101
    spriteID: 01858e74d29d74a45aea8857a6baa9db
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 130, y: 129}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.6355932, y: -0.55445546}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 256
      y: 129
      width: 118
      height: 101
    spriteID: 73ad0be9074a954488202b4a8d9a1139
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 256, y: 129}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.5037038, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 326
      y: 4
      width: 135
      height: 107
    spriteID: 1f94ca1eb29ab374bb176150d0dae9dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 326, y: 4}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.26530612, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 735
      y: 129
      width: 98
      height: 107
    spriteID: 2ff36628af76166448080357842ea6f0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 735, y: 129}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.25316453, y: -0.5833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 272
      y: 479
      width: 79
      height: 96
    spriteID: d479d9ff9e9d0784fa6ee86f93b84662
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 272, y: 479}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.20618555, y: -0.6086956}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 325
      y: 274
      width: 97
      height: 92
    spriteID: e8676abbbb418334ebfa4f7a3cbe80d0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 325, y: 274}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 274
      width: 99
      height: 91
    spriteID: db33a43e2b8eccb47be7a028d069c8f4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 274}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.20202018, y: -0.61538464}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 218
      y: 274
      width: 99
      height: 91
    spriteID: 007a00b334a66a84eb82eee7792e4555
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 218, y: 274}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.54375005, y: -0.57}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 158
      y: 4
      width: 160
      height: 100
    spriteID: b5b7263af39de1a43a3fa0db5f6dab64
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 158, y: 4}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.72222227, y: -0.58762884}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 724
      y: 4
      width: 126
      height: 97
    spriteID: 85a1b36ae00f03341ad0319ec7a6e78f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 724, y: 4}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.59782606, y: -0.58947366}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 376
      width: 92
      height: 95
    spriteID: 6e104ef62e9341245bae1f53cc5f6c0c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 376}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 652271667
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Warrior_Purple
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 9ee7f15378f457d468c21716ded48bfc
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 7264a732792011f4480e0be2a53f47ee
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: 8a520b3a8a83d4b4599d97f774e5cce8
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 89
      spriteId: 92687fa315592e84f95b3ad79efbb6df
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 80
        height: 87
      spriteId: 8afcb4943465d074ba4861dcee2321ed
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 88
      spriteId: d0fe8823f3f63be44bcb118ef9ccdd8b
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 78
        height: 91
      spriteId: c1e740266a126dd47b5d1c5c00ccfa7e
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 71
        y: 56
        width: 60
        height: 93
      spriteId: 3a2fdcb7cf000ff418da4a04824f44b8
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 56
        width: 69
        height: 94
      spriteId: 0b4c97e1f4ee8824cb1528ef8cae4ffb
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 56
        width: 78
        height: 80
      spriteId: 1d032bc71819da247844ae0dfded8881
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 56
        width: 95
        height: 93
      spriteId: c30dd2df706ed0d4b9ca48d908b746d3
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 89
        height: 94
      spriteId: 2b2816a61825b3a479d9834cdf744e1f
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 56
        width: 79
        height: 80
      spriteId: 400ecd290bbcebc4a86e23e87ad3599f
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 71
        height: 108
      spriteId: 00754199c2286344d91674ffb362f930
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: 67ec9e45e33ad4a4694da9f07dae162b
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 56
        width: 71
        height: 112
      spriteId: eeaf11e234793bb41b8cdd68755304bf
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 55
        width: 107
        height: 112
      spriteId: 405ade6c3646bb44485cacd313b01175
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 74
        y: 53
        width: 107
        height: 92
      spriteId: 3bca12379e42df341ac97fc00190493c
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 56
        width: 100
        height: 89
      spriteId: 4497a531b4825394bb4263587420c010
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 34
        y: 56
        width: 82
        height: 89
      spriteId: 4b6d033341409534c910576972e2075c
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: bf0fd8ef387325446b9347d581dfab00
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 56
        width: 84
        height: 89
      spriteId: f862d28dc356f4546a7b85dc05b4eeeb
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 46
        width: 123
        height: 100
      spriteId: ccc898e9b2c72f0448245cf64f0135ab
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 56
        width: 122
        height: 94
      spriteId: db7e1d4bbc434ba45b5e5a1e5dcfa746
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 85
        height: 88
      spriteId: 65bfef9d7d4a38b4ea100a4851a67314
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 56
        width: 78
        height: 101
      spriteId: b0972ec4d559ac242987195f5703ffd3
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 00440bb33e2e3d4448ee3a40f5b179fc
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 56
        width: 83
        height: 103
      spriteId: 8fe5416c2c24cd041ae09ff80d0695b3
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 46
        y: 13
        width: 118
        height: 137
      spriteId: a4ee8a9561127c94da89e443fdc11ec3
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 25
        width: 89
        height: 124
      spriteId: afb0788dcfb867f42afeb1674c5fa388
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 52
        width: 66
        height: 99
      spriteId: 1f93d995d6b0a6e4880e79caf40273a8
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 36
        y: 56
        width: 80
        height: 93
      spriteId: 6fa282792e468fb458f8e13bff33b5d1
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: 974e9c4f0572e6549bd8aab0fe6a707a
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 56
        width: 81
        height: 94
      spriteId: c835efcdba5bb63409e2c89b8f85019d
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 28
        width: 146
        height: 117
      spriteId: 0789d1d5d6f322f47840f812f84b046f
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 29
        width: 116
        height: 117
      spriteId: 2aea81ed6bdd4d64695a41a46bd67791
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 62
        y: 56
        width: 99
        height: 94
      spriteId: bc10b846ec4932b4db036fd0250204c9
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 56
        width: 118
        height: 99
      spriteId: bc56f4f7af205c44a8af7928dd001217
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 01858e74d29d74a45aea8857a6baa9db
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 21
        y: 56
        width: 118
        height: 101
      spriteId: 73ad0be9074a954488202b4a8d9a1139
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 57
        width: 135
        height: 107
      spriteId: 1f94ca1eb29ab374bb176150d0dae9dc
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 57
        width: 98
        height: 107
      spriteId: 2ff36628af76166448080357842ea6f0
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 79
        height: 96
      spriteId: d479d9ff9e9d0784fa6ee86f93b84662
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 97
        height: 92
      spriteId: e8676abbbb418334ebfa4f7a3cbe80d0
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: db33a43e2b8eccb47be7a028d069c8f4
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 76
        y: 56
        width: 99
        height: 91
      spriteId: 007a00b334a66a84eb82eee7792e4555
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 9
        y: 57
        width: 160
        height: 100
      spriteId: b5b7263af39de1a43a3fa0db5f6dab64
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 5
        y: 57
        width: 126
        height: 97
      spriteId: 85a1b36ae00f03341ad0319ec7a6e78f
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 56
        width: 92
        height: 95
      spriteId: 6e104ef62e9341245bae1f53cc5f6c0c
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 1024, y: 1024}

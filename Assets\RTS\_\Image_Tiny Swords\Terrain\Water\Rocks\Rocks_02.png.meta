fileFormatVersion: 2
guid: 7f30fe534299f0b438ce8650d6196f34
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6972315418413338663
    second: Rocks_02_0
  - first:
      213: -8661709897740801471
    second: Rocks_02_1
  - first:
      213: 8824554328431356043
    second: Rocks_02_2
  - first:
      213: -6695211258704145448
    second: Rocks_02_3
  - first:
      213: -1820314030940062042
    second: Rocks_02_4
  - first:
      213: 3976394749612131340
    second: Rocks_02_5
  - first:
      213: -5443729802455617797
    second: Rocks_02_6
  - first:
      213: 5451828946050965503
    second: Rocks_02_7
  - first:
      213: 6111856450617385455
    second: Rocks_02_8
  - first:
      213: 8257728822493429651
    second: Rocks_02_9
  - first:
      213: 2538111090135613914
    second: Rocks_02_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Rocks_02_0
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7209180aee3a2c060800000000000000
      internalID: 6972315418413338663
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_1
      rect:
        serializedVersion: 2
        x: 160
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14aacad5dbc6bc780800000000000000
      internalID: -8661709897740801471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_2
      rect:
        serializedVersion: 2
        x: 288
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b88694f386d177a70800000000000000
      internalID: 8824554328431356043
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_3
      rect:
        serializedVersion: 2
        x: 416
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d35ebc35d4d513a0800000000000000
      internalID: -6695211258704145448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_4
      rect:
        serializedVersion: 2
        x: 544
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ae78268802fcb6e0800000000000000
      internalID: -1820314030940062042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_5
      rect:
        serializedVersion: 2
        x: 672
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c040c59f4fdfe2730800000000000000
      internalID: 3976394749612131340
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_6
      rect:
        serializedVersion: 2
        x: 800
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bfeec67369cf374b0800000000000000
      internalID: -5443729802455617797
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_02_7
      rect:
        serializedVersion: 2
        x: 928
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fff5f899a89c8ab40800000000000000
      internalID: 5451828946050965503
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 75ca4589d67182445884e22b1594e7b7
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Rocks_02_0: 6972315418413338663
      Rocks_02_1: -8661709897740801471
      Rocks_02_2: 8824554328431356043
      Rocks_02_3: -6695211258704145448
      Rocks_02_4: -1820314030940062042
      Rocks_02_5: 3976394749612131340
      Rocks_02_6: -5443729802455617797
      Rocks_02_7: 5451828946050965503
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

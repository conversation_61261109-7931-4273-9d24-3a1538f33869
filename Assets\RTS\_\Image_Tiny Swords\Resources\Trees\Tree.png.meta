fileFormatVersion: 2
guid: ad3a56f763c3325409179e935dbe4bf1
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6853316077166303748
    second: Tree_0
  - first:
      213: 4134985361545656292
    second: Tree_1
  - first:
      213: -1566730818833404899
    second: Tree_2
  - first:
      213: -7301469273254094464
    second: Tree_3
  - first:
      213: 4246777929600769233
    second: Tree_4
  - first:
      213: -1089773682390131457
    second: Tree_5
  - first:
      213: 3690649294209121615
    second: Tree_6
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Tree_0
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40a98f869aedb1f50800000000000000
      internalID: 6853316077166303748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_1
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e77e5a374b626930800000000000000
      internalID: 4134985361545656292
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_2
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1c27c40f9ad14ae0800000000000000
      internalID: -1566730818833404899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_3
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 089924d7168fbaa90800000000000000
      internalID: -7301469273254094464
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_4
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1dc92ddc6069fea30800000000000000
      internalID: 4246777929600769233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_5
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ffc11971c9850e0f0800000000000000
      internalID: -1089773682390131457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tree_6
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4d71ebaaf1d73330800000000000000
      internalID: 3690649294209121615
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 36458d4bb827c8e49ba8e2aeb67f5f04
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Tree_0: 6853316077166303748
      Tree_1: 4134985361545656292
      Tree_2: -1566730818833404899
      Tree_3: -7301469273254094464
      Tree_4: 4246777929600769233
      Tree_5: -1089773682390131457
      Tree_6: 3690649294209121615
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: fe5a871765e0ccb4e8db32a6a4479724
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7110933946802297915
    second: <PERSON>_Yellow_0
  - first:
      213: 7993793395516240808
    second: <PERSON>_Yellow_1
  - first:
      213: 6701249659865702185
    second: <PERSON>_Yellow_2
  - first:
      213: 8973363922609754363
    second: <PERSON>_Yellow_3
  - first:
      213: 7491787809491371905
    second: <PERSON>_Yellow_4
  - first:
      213: 4723905455403976280
    second: <PERSON>_Yellow_5
  - first:
      213: 6911566147952905907
    second: <PERSON>_Yellow_6
  - first:
      213: -1494015320982562888
    second: <PERSON>_Yellow_7
  - first:
      213: 5917056135602737107
    second: <PERSON>_Yellow_8
  - first:
      213: 8237390734558222465
    second: <PERSON>_Yellow_9
  - first:
      213: -7695933769915554131
    second: <PERSON>_Yellow_10
  - first:
      213: -4853747864534601877
    second: <PERSON>_Yellow_11
  - first:
      213: -5109338795862936319
    second: <PERSON>_Yellow_12
  - first:
      213: 2569213363613892740
    second: <PERSON>_Yellow_13
  - first:
      213: -3707545240357985591
    second: <PERSON>_Yellow_14
  - first:
      213: 8494299374802026767
    second: Archer_Yellow_15
  - first:
      213: -9038759321465431983
    second: Archer_<PERSON>_16
  - first:
      213: 2696206849810787605
    second: Archer_Yellow_17
  - first:
      213: -307851399436875265
    second: Archer_Yellow_18
  - first:
      213: -3514758529225216316
    second: Archer_Yellow_19
  - first:
      213: 4066995964542233789
    second: Archer_Yellow_20
  - first:
      213: -3132761242765511547
    second: Archer_Yellow_21
  - first:
      213: -5875456770159939319
    second: Archer_Yellow_22
  - first:
      213: 3229750905385415497
    second: Archer_Yellow_23
  - first:
      213: 1237912896205655161
    second: Archer_Yellow_24
  - first:
      213: -6490675203542068720
    second: Archer_Yellow_25
  - first:
      213: 3938623911419702919
    second: Archer_Yellow_26
  - first:
      213: 4384528658141597022
    second: Archer_Yellow_27
  - first:
      213: 8850437280527683589
    second: Archer_Yellow_28
  - first:
      213: -2173246018093920776
    second: Archer_Yellow_29
  - first:
      213: -1152418264538783807
    second: Archer_Yellow_30
  - first:
      213: -1491292914716182532
    second: Archer_Yellow_31
  - first:
      213: -8649063960831584296
    second: Archer_Yellow_32
  - first:
      213: 1270628483980271430
    second: Archer_Yellow_33
  - first:
      213: 1285069046831969053
    second: Archer_Yellow_34
  - first:
      213: 8953985582528129268
    second: Archer_Yellow_35
  - first:
      213: -6705703735867196182
    second: Archer_Yellow_36
  - first:
      213: -7494737690905067640
    second: Archer_Yellow_37
  - first:
      213: 7139343495825958540
    second: Archer_Yellow_38
  - first:
      213: -5377445512828496312
    second: Archer_Yellow_39
  - first:
      213: 6705118943379097311
    second: Archer_Yellow_40
  - first:
      213: -8004677733978844801
    second: Archer_Yellow_41
  - first:
      213: 2591832851865793619
    second: Archer_Yellow_42
  - first:
      213: 386750439086583552
    second: Archer_Yellow_43
  - first:
      213: -948917700013993028
    second: Archer_Yellow_44
  - first:
      213: 3179510280441670504
    second: Archer_Yellow_45
  - first:
      213: 3591864029751409788
    second: Archer_Yellow_46
  - first:
      213: 2331703536156955686
    second: Archer_Yellow_47
  - first:
      213: 3007795780308414016
    second: Archer_Yellow_48
  - first:
      213: -5384472969916630290
    second: Archer_Yellow_49
  - first:
      213: 578544410489869519
    second: Archer_Yellow_50
  - first:
      213: -6570628944003406875
    second: Archer_Yellow_51
  - first:
      213: 2160232353066956610
    second: Archer_Yellow_52
  - first:
      213: -4921499184622814985
    second: Archer_Yellow_53
  - first:
      213: 8262028396003275260
    second: Archer_Yellow_54
  - first:
      213: -1364211691925706857
    second: Archer_Yellow_55
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Yellow_0
      rect:
        serializedVersion: 2
        x: 67
        y: 1209
        width: 64
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c3259f2043e05d90800000000000000
      internalID: -7110933946802297915
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_1
      rect:
        serializedVersion: 2
        x: 259
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a3fb938db8afee60800000000000000
      internalID: 7993793395516240808
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_2
      rect:
        serializedVersion: 2
        x: 451
        y: 1209
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92f90879f0f9ffc50800000000000000
      internalID: 6701249659865702185
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_3
      rect:
        serializedVersion: 2
        x: 641
        y: 1209
        width: 69
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf0d577d1fac78c70800000000000000
      internalID: 8973363922609754363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_4
      rect:
        serializedVersion: 2
        x: 833
        y: 1209
        width: 67
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 187c55dc15d28f760800000000000000
      internalID: 7491787809491371905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_5
      rect:
        serializedVersion: 2
        x: 1026
        y: 1209
        width: 64
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85642edbbfeae8140800000000000000
      internalID: 4723905455403976280
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_6
      rect:
        serializedVersion: 2
        x: 65
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3be0952eac0daef50800000000000000
      internalID: 6911566147952905907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_7
      rect:
        serializedVersion: 2
        x: 258
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b7d7df2bf0344be0800000000000000
      internalID: -1494015320982562888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_8
      rect:
        serializedVersion: 2
        x: 451
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3df85a6943b9d1250800000000000000
      internalID: 5917056135602737107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_9
      rect:
        serializedVersion: 2
        x: 636
        y: 1035
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 188df61ca37115270800000000000000
      internalID: 8237390734558222465
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_10
      rect:
        serializedVersion: 2
        x: 641
        y: 1017
        width: 69
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da62045d20d823590800000000000000
      internalID: -7695933769915554131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_11
      rect:
        serializedVersion: 2
        x: 830
        y: 1030
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6faa11980604acb0800000000000000
      internalID: -4853747864534601877
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_12
      rect:
        serializedVersion: 2
        x: 834
        y: 1017
        width: 67
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 105b7738f6bf719b0800000000000000
      internalID: -5109338795862936319
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_13
      rect:
        serializedVersion: 2
        x: 1027
        y: 1017
        width: 64
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 480846221fda7a320800000000000000
      internalID: 2569213363613892740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_14
      rect:
        serializedVersion: 2
        x: 66
        y: 825
        width: 67
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c2a5cf4f372c8cc0800000000000000
      internalID: -3707545240357985591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_15
      rect:
        serializedVersion: 2
        x: 258
        y: 825
        width: 77
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f01c5e2d640d1e570800000000000000
      internalID: 8494299374802026767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_16
      rect:
        serializedVersion: 2
        x: 450
        y: 825
        width: 64
        height: 116
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1585dc95940ef8280800000000000000
      internalID: -9038759321465431983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_17
      rect:
        serializedVersion: 2
        x: 636
        y: 825
        width: 68
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51d62f348d9da6520800000000000000
      internalID: 2696206849810787605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_18
      rect:
        serializedVersion: 2
        x: 828
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff945f488ca4abbf0800000000000000
      internalID: -307851399436875265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_19
      rect:
        serializedVersion: 2
        x: 1020
        y: 825
        width: 68
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cea14a5db1193fc0800000000000000
      internalID: -3514758529225216316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_20
      rect:
        serializedVersion: 2
        x: 1210
        y: 825
        width: 62
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dbcac79284fd07830800000000000000
      internalID: 4066995964542233789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_21
      rect:
        serializedVersion: 2
        x: 1238
        y: 891
        width: 43
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 584b9de6f323684d0800000000000000
      internalID: -3132761242765511547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_22
      rect:
        serializedVersion: 2
        x: 1410
        y: 825
        width: 57
        height: 71
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 901f371723f267ea0800000000000000
      internalID: -5875456770159939319
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_23
      rect:
        serializedVersion: 2
        x: 1439
        y: 875
        width: 35
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9437e74265162dc20800000000000000
      internalID: 3229750905385415497
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_24
      rect:
        serializedVersion: 2
        x: 55
        y: 633
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97c86396833fd2110800000000000000
      internalID: 1237912896205655161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_25
      rect:
        serializedVersion: 2
        x: 256
        y: 633
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 012507c9a4d7ce5a0800000000000000
      internalID: -6490675203542068720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_26
      rect:
        serializedVersion: 2
        x: 448
        y: 633
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 78ee708649dc8a630800000000000000
      internalID: 3938623911419702919
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_27
      rect:
        serializedVersion: 2
        x: 634
        y: 633
        width: 74
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5dcc089599f8dc30800000000000000
      internalID: 4384528658141597022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_28
      rect:
        serializedVersion: 2
        x: 826
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5001ab4a0d113da70800000000000000
      internalID: 8850437280527683589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_29
      rect:
        serializedVersion: 2
        x: 1018
        y: 633
        width: 76
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8fdcaf9c24417d1e0800000000000000
      internalID: -2173246018093920776
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_30
      rect:
        serializedVersion: 2
        x: 1207
        y: 633
        width: 80
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1cf5e75b1b9c100f0800000000000000
      internalID: -1152418264538783807
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_31
      rect:
        serializedVersion: 2
        x: 1408
        y: 633
        width: 68
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf7f84acefcdd4be0800000000000000
      internalID: -1491292914716182532
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_32
      rect:
        serializedVersion: 2
        x: 55
        y: 441
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d7100ff62a58f780800000000000000
      internalID: -8649063960831584296
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_33
      rect:
        serializedVersion: 2
        x: 256
        y: 441
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64f1325cfdd22a110800000000000000
      internalID: 1270628483980271430
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_34
      rect:
        serializedVersion: 2
        x: 448
        y: 441
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d13f4405d7b75d110800000000000000
      internalID: 1285069046831969053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_35
      rect:
        serializedVersion: 2
        x: 634
        y: 441
        width: 81
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f8ee827272f24c70800000000000000
      internalID: 8953985582528129268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_36
      rect:
        serializedVersion: 2
        x: 826
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aec30a60bfd80f2a0800000000000000
      internalID: -6705703735867196182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_37
      rect:
        serializedVersion: 2
        x: 1018
        y: 441
        width: 75
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88f3fd867c75df790800000000000000
      internalID: -7494737690905067640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_38
      rect:
        serializedVersion: 2
        x: 1202
        y: 441
        width: 82
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c86a336651b041360800000000000000
      internalID: 7139343495825958540
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_39
      rect:
        serializedVersion: 2
        x: 1408
        y: 441
        width: 65
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 846d6d80cc97f55b0800000000000000
      internalID: -5377445512828496312
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_40
      rect:
        serializedVersion: 2
        x: 55
        y: 249
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd68adc572e5d0d50800000000000000
      internalID: 6705118943379097311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_41
      rect:
        serializedVersion: 2
        x: 256
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7156b4f20ca9e090800000000000000
      internalID: -8004677733978844801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_42
      rect:
        serializedVersion: 2
        x: 448
        y: 249
        width: 94
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 354f5a42f3a08f320800000000000000
      internalID: 2591832851865793619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_43
      rect:
        serializedVersion: 2
        x: 639
        y: 242
        width: 63
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00f6b7aa9730e5500800000000000000
      internalID: 386750439086583552
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_44
      rect:
        serializedVersion: 2
        x: 827
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb3d5912164c4d2f0800000000000000
      internalID: -948917700013993028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_45
      rect:
        serializedVersion: 2
        x: 1019
        y: 240
        width: 69
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86bad6911c3ef1c20800000000000000
      internalID: 3179510280441670504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_46
      rect:
        serializedVersion: 2
        x: 1205
        y: 239
        width: 76
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7cf5d6fd4dd8d130800000000000000
      internalID: 3591864029751409788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_47
      rect:
        serializedVersion: 2
        x: 1402
        y: 243
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62458222300eb5020800000000000000
      internalID: 2331703536156955686
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_48
      rect:
        serializedVersion: 2
        x: 55
        y: 57
        width: 66
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04ea3328356ddb920800000000000000
      internalID: 3007795780308414016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_49
      rect:
        serializedVersion: 2
        x: 256
        y: 57
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eea3d6dac528645b0800000000000000
      internalID: -5384472969916630290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_50
      rect:
        serializedVersion: 2
        x: 448
        y: 54
        width: 86
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc4590a7317670800800000000000000
      internalID: 578544410489869519
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_51
      rect:
        serializedVersion: 2
        x: 639
        y: 42
        width: 57
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e3e6c1c9cf60d4a0800000000000000
      internalID: -6570628944003406875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_52
      rect:
        serializedVersion: 2
        x: 831
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 247bcd431efaafd10800000000000000
      internalID: 2160232353066956610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_53
      rect:
        serializedVersion: 2
        x: 1023
        y: 47
        width: 57
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f8be2a119253bbb0800000000000000
      internalID: -4921499184622814985
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_54
      rect:
        serializedVersion: 2
        x: 1212
        y: 47
        width: 61
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf137f35d0f98a270800000000000000
      internalID: 8262028396003275260
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Yellow_55
      rect:
        serializedVersion: 2
        x: 1402
        y: 51
        width: 71
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7976550b1b8511de0800000000000000
      internalID: -1364211691925706857
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Yellow_0: -7110933946802297915
      Archer_Yellow_1: 7993793395516240808
      Archer_Yellow_10: -7695933769915554131
      Archer_Yellow_11: -4853747864534601877
      Archer_Yellow_12: -5109338795862936319
      Archer_Yellow_13: 2569213363613892740
      Archer_Yellow_14: -3707545240357985591
      Archer_Yellow_15: 8494299374802026767
      Archer_Yellow_16: -9038759321465431983
      Archer_Yellow_17: 2696206849810787605
      Archer_Yellow_18: -307851399436875265
      Archer_Yellow_19: -3514758529225216316
      Archer_Yellow_2: 6701249659865702185
      Archer_Yellow_20: 4066995964542233789
      Archer_Yellow_21: -3132761242765511547
      Archer_Yellow_22: -5875456770159939319
      Archer_Yellow_23: 3229750905385415497
      Archer_Yellow_24: 1237912896205655161
      Archer_Yellow_25: -6490675203542068720
      Archer_Yellow_26: 3938623911419702919
      Archer_Yellow_27: 4384528658141597022
      Archer_Yellow_28: 8850437280527683589
      Archer_Yellow_29: -2173246018093920776
      Archer_Yellow_3: 8973363922609754363
      Archer_Yellow_30: -1152418264538783807
      Archer_Yellow_31: -1491292914716182532
      Archer_Yellow_32: -8649063960831584296
      Archer_Yellow_33: 1270628483980271430
      Archer_Yellow_34: 1285069046831969053
      Archer_Yellow_35: 8953985582528129268
      Archer_Yellow_36: -6705703735867196182
      Archer_Yellow_37: -7494737690905067640
      Archer_Yellow_38: 7139343495825958540
      Archer_Yellow_39: -5377445512828496312
      Archer_Yellow_4: 7491787809491371905
      Archer_Yellow_40: 6705118943379097311
      Archer_Yellow_41: -8004677733978844801
      Archer_Yellow_42: 2591832851865793619
      Archer_Yellow_43: 386750439086583552
      Archer_Yellow_44: -948917700013993028
      Archer_Yellow_45: 3179510280441670504
      Archer_Yellow_46: 3591864029751409788
      Archer_Yellow_47: 2331703536156955686
      Archer_Yellow_48: 3007795780308414016
      Archer_Yellow_49: -5384472969916630290
      Archer_Yellow_5: 4723905455403976280
      Archer_Yellow_50: 578544410489869519
      Archer_Yellow_51: -6570628944003406875
      Archer_Yellow_52: 2160232353066956610
      Archer_Yellow_53: -4921499184622814985
      Archer_Yellow_54: 8262028396003275260
      Archer_Yellow_55: -1364211691925706857
      Archer_Yellow_6: 6911566147952905907
      Archer_Yellow_7: -1494015320982562888
      Archer_Yellow_8: 5917056135602737107
      Archer_Yellow_9: 8237390734558222465
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

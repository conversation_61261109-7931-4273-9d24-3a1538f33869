fileFormatVersion: 2
guid: b178ee77709b0124693a75ed69204a04
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8777388641641696378
    second: Torch_<PERSON>_0
  - first:
      213: 7289106625145917395
    second: Torch_Purple_1
  - first:
      213: 6563495107105060230
    second: Torch_Purple_2
  - first:
      213: -2718780732279233981
    second: Torch_Purple_3
  - first:
      213: -3563705805885922832
    second: Torch_Purple_4
  - first:
      213: 8757872799773580616
    second: Torch_Purple_5
  - first:
      213: 6806364753432413300
    second: Torch_Purple_6
  - first:
      213: -8676763838232251490
    second: Torch_Purple_7
  - first:
      213: 6587575489424863480
    second: Torch_Purple_8
  - first:
      213: -4649307110469801079
    second: Tor<PERSON>_<PERSON>_9
  - first:
      213: -5350870462702088880
    second: Torch_Purple_10
  - first:
      213: -743100397886644504
    second: Torch_Purple_11
  - first:
      213: 6366584411652875701
    second: Torch_Purple_12
  - first:
      213: 6650235719180979789
    second: Torch_Purple_13
  - first:
      213: -2292804030775991083
    second: Torch_Purple_14
  - first:
      213: 8444722779903732438
    second: Torch_Purple_15
  - first:
      213: 5308740645727054198
    second: Torch_Purple_16
  - first:
      213: -8175694481049523811
    second: Torch_Purple_17
  - first:
      213: -5552232036246018689
    second: Torch_Purple_18
  - first:
      213: -7214844403715748582
    second: Torch_Purple_19
  - first:
      213: 4338798451457133823
    second: Torch_Purple_20
  - first:
      213: 157586050839780054
    second: Torch_Purple_21
  - first:
      213: 3963220145696015614
    second: Torch_Purple_22
  - first:
      213: -7369646498216302835
    second: Torch_Purple_23
  - first:
      213: -308179443233466684
    second: Torch_Purple_24
  - first:
      213: -8912942771199552999
    second: Torch_Purple_25
  - first:
      213: 829214079118854761
    second: Torch_Purple_26
  - first:
      213: 6587287232380290893
    second: Torch_Purple_27
  - first:
      213: -3613194450452872922
    second: Torch_Purple_28
  - first:
      213: 4254220321314900722
    second: Torch_Purple_29
  - first:
      213: -3636304627072001104
    second: Torch_Purple_30
  - first:
      213: 2526182072521003030
    second: Torch_Purple_31
  - first:
      213: -534309051310876368
    second: Torch_Purple_32
  - first:
      213: -1031857605635688553
    second: Torch_Purple_33
  - first:
      213: -4039123883211088733
    second: Torch_Purple_34
  - first:
      213: -204375668282837498
    second: Torch_Purple_35
  - first:
      213: -806418910718427689
    second: Torch_Purple_36
  - first:
      213: 6971597377520573325
    second: Torch_Purple_37
  - first:
      213: -5806124124225188590
    second: Torch_Purple_38
  - first:
      213: -6786669139989514500
    second: Torch_Purple_39
  - first:
      213: 9139717770830630858
    second: Torch_Purple_40
  - first:
      213: 6472037063354771283
    second: Torch_Purple_41
  - first:
      213: -5201262000825232503
    second: Torch_Purple_42
  - first:
      213: -8884292802392011540
    second: Torch_Purple_43
  - first:
      213: -5376117467778600094
    second: Torch_Purple_44
  - first:
      213: 5630286481573221149
    second: Torch_Purple_45
  - first:
      213: 8373742750572626711
    second: Torch_Purple_46
  - first:
      213: 4524142071194896893
    second: Torch_Purple_47
  - first:
      213: 7231459763197342479
    second: Torch_Purple_48
  - first:
      213: -1882189263611289453
    second: Torch_Purple_49
  - first:
      213: 8937611363904212237
    second: Torch_Purple_50
  - first:
      213: -4167974887603447450
    second: Torch_Purple_51
  - first:
      213: 4147723058122188050
    second: Torch_Purple_52
  - first:
      213: 4539507612114334818
    second: Torch_Purple_53
  - first:
      213: 3769643294560197153
    second: Torch_Purple_54
  - first:
      213: 904309756318845567
    second: Torch_Purple_55
  - first:
      213: -3179630900763951533
    second: Torch_Purple_56
  - first:
      213: 8122145324355914720
    second: Torch_Purple_57
  - first:
      213: 5771819029894492542
    second: Torch_Purple_58
  - first:
      213: 2966182216872429557
    second: Torch_Purple_59
  - first:
      213: -3159153860480877542
    second: Torch_Purple_60
  - first:
      213: 7800583797527961178
    second: Torch_Purple_61
  - first:
      213: -5569173360199599610
    second: Torch_Purple_62
  - first:
      213: -5690465260101204808
    second: Torch_Purple_63
  - first:
      213: 1073706693892016841
    second: Torch_Purple_64
  - first:
      213: -4964974728139563750
    second: Torch_Purple_65
  - first:
      213: -3785200113508070910
    second: Torch_Purple_66
  - first:
      213: -697494652585742524
    second: Torch_Purple_67
  - first:
      213: -5442006533641337661
    second: Torch_Purple_68
  - first:
      213: -1025322795725769496
    second: Torch_Purple_69
  - first:
      213: -3255691038874216582
    second: Torch_Purple_70
  - first:
      213: 3955675992198382607
    second: Torch_Purple_71
  - first:
      213: -3196865167981344529
    second: Torch_Purple_72
  - first:
      213: 2529677035573766443
    second: Torch_Purple_73
  - first:
      213: -5139444600743104429
    second: Torch_Purple_74
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Torch_Purple_0
      rect:
        serializedVersion: 2
        x: 52
        y: 826
        width: 76
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7c0a98077c8fc970800000000000000
      internalID: 8777388641641696378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_1
      rect:
        serializedVersion: 2
        x: 242
        y: 826
        width: 77
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3dfc4c13bdb182560800000000000000
      internalID: 7289106625145917395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_2
      rect:
        serializedVersion: 2
        x: 435
        y: 826
        width: 76
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 685f2086608361b50800000000000000
      internalID: 6563495107105060230
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_3
      rect:
        serializedVersion: 2
        x: 450
        y: 904
        width: 10
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 342e1562453f44ad0800000000000000
      internalID: -2718780732279233981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_4
      rect:
        serializedVersion: 2
        x: 629
        y: 826
        width: 74
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f12c2c637c2b8ec0800000000000000
      internalID: -3563705805885922832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_5
      rect:
        serializedVersion: 2
        x: 630
        y: 902
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84d5f8209e63a8970800000000000000
      internalID: 8757872799773580616
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_6
      rect:
        serializedVersion: 2
        x: 822
        y: 897
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 474041c7ea0157e50800000000000000
      internalID: 6806364753432413300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_7
      rect:
        serializedVersion: 2
        x: 828
        y: 826
        width: 67
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e933636b241f59780800000000000000
      internalID: -8676763838232251490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_8
      rect:
        serializedVersion: 2
        x: 1014
        y: 903
        width: 8
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f03d325105cb6b50800000000000000
      internalID: 6587575489424863480
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_9
      rect:
        serializedVersion: 2
        x: 1018
        y: 826
        width: 69
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98befbce0d75a7fb0800000000000000
      internalID: -4649307110469801079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_10
      rect:
        serializedVersion: 2
        x: 1207
        y: 826
        width: 73
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05d038f0aa3edb5b0800000000000000
      internalID: -5350870462702088880
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_11
      rect:
        serializedVersion: 2
        x: 18
        y: 697
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8eed38f002affa5f0800000000000000
      internalID: -743100397886644504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_12
      rect:
        serializedVersion: 2
        x: 27
        y: 634
        width: 101
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b5eb117ac6aa5850800000000000000
      internalID: 6366584411652875701
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_13
      rect:
        serializedVersion: 2
        x: 219
        y: 696
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d4e8562f6226a4c50800000000000000
      internalID: 6650235719180979789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_14
      rect:
        serializedVersion: 2
        x: 224
        y: 634
        width: 96
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d4630eb1e25e20e0800000000000000
      internalID: -2292804030775991083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_15
      rect:
        serializedVersion: 2
        x: 403
        y: 694
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6daafc250aea13570800000000000000
      internalID: 8444722779903732438
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_16
      rect:
        serializedVersion: 2
        x: 416
        y: 634
        width: 94
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6792d398d7f6ca940800000000000000
      internalID: 5308740645727054198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_17
      rect:
        serializedVersion: 2
        x: 594
        y: 697
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d99ebe8af291a8e80800000000000000
      internalID: -8175694481049523811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_18
      rect:
        serializedVersion: 2
        x: 603
        y: 634
        width: 104
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f752309416282f2b0800000000000000
      internalID: -5552232036246018689
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_19
      rect:
        serializedVersion: 2
        x: 795
        y: 696
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1dcece9d39bfdb90800000000000000
      internalID: -7214844403715748582
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_20
      rect:
        serializedVersion: 2
        x: 800
        y: 634
        width: 97
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ffc0e132432863c30800000000000000
      internalID: 4338798451457133823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_21
      rect:
        serializedVersion: 2
        x: 979
        y: 694
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6da9cd5adabdf2200800000000000000
      internalID: 157586050839780054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_22
      rect:
        serializedVersion: 2
        x: 992
        y: 634
        width: 94
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef402e679bf200730800000000000000
      internalID: 3963220145696015614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_23
      rect:
        serializedVersion: 2
        x: 230
        y: 442
        width: 88
        height: 104
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d07ad915e81c9b990800000000000000
      internalID: -7369646498216302835
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_24
      rect:
        serializedVersion: 2
        x: 418
        y: 442
        width: 92
        height: 111
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c6ddfedd6029bbf0800000000000000
      internalID: -308179443233466684
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_25
      rect:
        serializedVersion: 2
        x: 614
        y: 532
        width: 17
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 912d34bb5cdde4480800000000000000
      internalID: -8912942771199552999
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_26
      rect:
        serializedVersion: 2
        x: 807
        y: 541
        width: 12
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96ac1a4cfc5f18b00800000000000000
      internalID: 829214079118854761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_27
      rect:
        serializedVersion: 2
        x: 1002
        y: 546
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d474e7f36deba6b50800000000000000
      internalID: 6587287232380290893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_28
      rect:
        serializedVersion: 2
        x: 43
        y: 442
        width: 82
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62108547aca5bddc0800000000000000
      internalID: -3613194450452872922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_29
      rect:
        serializedVersion: 2
        x: 62
        y: 532
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2fa0938a7d60a0b30800000000000000
      internalID: 4254220321314900722
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_30
      rect:
        serializedVersion: 2
        x: 623
        y: 521
        width: 26
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bb93482530498dc0800000000000000
      internalID: -3636304627072001104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_31
      rect:
        serializedVersion: 2
        x: 630
        y: 441
        width: 123
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 610139ee53dce0320800000000000000
      internalID: 2526182072521003030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_32
      rect:
        serializedVersion: 2
        x: 818
        y: 527
        width: 12
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 039d65cafb0c598f0800000000000000
      internalID: -534309051310876368
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_33
      rect:
        serializedVersion: 2
        x: 837
        y: 529
        width: 27
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 797bc4589fa1ea1f0800000000000000
      internalID: -1031857605635688553
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_34
      rect:
        serializedVersion: 2
        x: 865
        y: 525
        width: 39
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ac18697a3622f7c0800000000000000
      internalID: -4039123883211088733
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_35
      rect:
        serializedVersion: 2
        x: 1032
        y: 530
        width: 20
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 606b44b9969e92df0800000000000000
      internalID: -204375668282837498
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_36
      rect:
        serializedVersion: 2
        x: 1070
        y: 529
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7dd7876f5460fc4f0800000000000000
      internalID: -806418910718427689
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_37
      rect:
        serializedVersion: 2
        x: 904
        y: 514
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8b9824b0e610c060800000000000000
      internalID: 6971597377520573325
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_38
      rect:
        serializedVersion: 2
        x: 839
        y: 440
        width: 105
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21d4bb50fd08c6fa0800000000000000
      internalID: -5806124124225188590
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_39
      rect:
        serializedVersion: 2
        x: 1030
        y: 442
        width: 83
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf6b1dedf58e0d1a0800000000000000
      internalID: -6786669139989514500
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_40
      rect:
        serializedVersion: 2
        x: 1119
        y: 478
        width: 7
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acf4fa563ecc6de70800000000000000
      internalID: 9139717770830630858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_41
      rect:
        serializedVersion: 2
        x: 1103
        y: 444
        width: 21
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3576c663b6b41d950800000000000000
      internalID: 6472037063354771283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_42
      rect:
        serializedVersion: 2
        x: 1118
        y: 458
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 983131984c761d7b0800000000000000
      internalID: -5201262000825232503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_43
      rect:
        serializedVersion: 2
        x: 46
        y: 250
        width: 81
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cec4dd864c6a4b480800000000000000
      internalID: -8884292802392011540
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_44
      rect:
        serializedVersion: 2
        x: 232
        y: 250
        width: 88
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 263b84e95a13465b0800000000000000
      internalID: -5376117467778600094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_45
      rect:
        serializedVersion: 2
        x: 421
        y: 250
        width: 91
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d131bd579bbc22e40800000000000000
      internalID: 5630286481573221149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_46
      rect:
        serializedVersion: 2
        x: 617
        y: 323
        width: 11
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71342686aa2853470800000000000000
      internalID: 8373742750572626711
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_47
      rect:
        serializedVersion: 2
        x: 620
        y: 319
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df9c8fa6e3bf8ce30800000000000000
      internalID: 4524142071194896893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_48
      rect:
        serializedVersion: 2
        x: 810
        y: 325
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0378e8375e4b5460800000000000000
      internalID: 7231459763197342479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_49
      rect:
        serializedVersion: 2
        x: 612
        y: 268
        width: 15
        height: 51
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 390256646de11e5e0800000000000000
      internalID: -1882189263611289453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_50
      rect:
        serializedVersion: 2
        x: 617
        y: 220
        width: 113
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0d41069e26c80c70800000000000000
      internalID: 8937611363904212237
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_51
      rect:
        serializedVersion: 2
        x: 807
        y: 301
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 669d5af2ce06826c0800000000000000
      internalID: -4167974887603447450
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_52
      rect:
        serializedVersion: 2
        x: 819
        y: 214
        width: 107
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 215e112552caf8930800000000000000
      internalID: 4147723058122188050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_53
      rect:
        serializedVersion: 2
        x: 1026
        y: 244
        width: 93
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 264b1433f129ffe30800000000000000
      internalID: 4539507612114334818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_54
      rect:
        serializedVersion: 2
        x: 805
        y: 280
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12a913e9a96705430800000000000000
      internalID: 3769643294560197153
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_55
      rect:
        serializedVersion: 2
        x: 816
        y: 259
        width: 6
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7a0e40aee0cc8c00800000000000000
      internalID: 904309756318845567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_56
      rect:
        serializedVersion: 2
        x: 1015
        y: 241
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35efb1bca8eafd3d0800000000000000
      internalID: -3179630900763951533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_57
      rect:
        serializedVersion: 2
        x: 1098
        y: 236
        width: 15
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0eb8558b328a7b070800000000000000
      internalID: 8122145324355914720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_58
      rect:
        serializedVersion: 2
        x: 1026
        y: 224
        width: 17
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7173b8c3de991050800000000000000
      internalID: 5771819029894492542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_59
      rect:
        serializedVersion: 2
        x: 1053
        y: 218
        width: 24
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f703cfb20ff92920800000000000000
      internalID: 2966182216872429557
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_60
      rect:
        serializedVersion: 2
        x: 1087
        y: 224
        width: 14
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1cbb334d4e6824d0800000000000000
      internalID: -3159153860480877542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_61
      rect:
        serializedVersion: 2
        x: 588
        y: 60
        width: 141
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a5e525472ad314c60800000000000000
      internalID: 7800583797527961178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_62
      rect:
        serializedVersion: 2
        x: 780
        y: 59
        width: 109
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60afbbce55256b2b0800000000000000
      internalID: -5569173360199599610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_63
      rect:
        serializedVersion: 2
        x: 1003
        y: 141
        width: 18
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b487fccef76701b0800000000000000
      internalID: -5690465260101204808
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_64
      rect:
        serializedVersion: 2
        x: 1020
        y: 145
        width: 26
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ca8462ac8296ee00800000000000000
      internalID: 1073706693892016841
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_65
      rect:
        serializedVersion: 2
        x: 1055
        y: 142
        width: 12
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1d13725acdd81bb0800000000000000
      internalID: -4964974728139563750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_66
      rect:
        serializedVersion: 2
        x: 67
        y: 58
        width: 93
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20a45963d84487bc0800000000000000
      internalID: -3785200113508070910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_67
      rect:
        serializedVersion: 2
        x: 256
        y: 43
        width: 96
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44f64e71f400256f0800000000000000
      internalID: -697494652585742524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_68
      rect:
        serializedVersion: 2
        x: 448
        y: 40
        width: 93
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c404f004eb1a74b0800000000000000
      internalID: -5442006533641337661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_69
      rect:
        serializedVersion: 2
        x: 872
        y: 119
        width: 25
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e4b7a4895255c1f0800000000000000
      internalID: -1025322795725769496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_70
      rect:
        serializedVersion: 2
        x: 974
        y: 58
        width: 118
        height: 84
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a77da71ef3671d2d0800000000000000
      internalID: -3255691038874216582
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_71
      rect:
        serializedVersion: 2
        x: 902
        y: 107
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0486895b5265e630800000000000000
      internalID: 3955675992198382607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_72
      rect:
        serializedVersion: 2
        x: 912
        y: 71
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe807a3411472a3d0800000000000000
      internalID: -3196865167981344529
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_73
      rect:
        serializedVersion: 2
        x: 713
        y: 46
        width: 17
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2966dd6cd73b1320800000000000000
      internalID: 2529677035573766443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Torch_Purple_74
      rect:
        serializedVersion: 2
        x: 911
        y: 50
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35078e59d560da8b0800000000000000
      internalID: -5139444600743104429
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Torch_Purple_0: 8777388641641696378
      Torch_Purple_1: 7289106625145917395
      Torch_Purple_10: -5350870462702088880
      Torch_Purple_11: -743100397886644504
      Torch_Purple_12: 6366584411652875701
      Torch_Purple_13: 6650235719180979789
      Torch_Purple_14: -2292804030775991083
      Torch_Purple_15: 8444722779903732438
      Torch_Purple_16: 5308740645727054198
      Torch_Purple_17: -8175694481049523811
      Torch_Purple_18: -5552232036246018689
      Torch_Purple_19: -7214844403715748582
      Torch_Purple_2: 6563495107105060230
      Torch_Purple_20: 4338798451457133823
      Torch_Purple_21: 157586050839780054
      Torch_Purple_22: 3963220145696015614
      Torch_Purple_23: -7369646498216302835
      Torch_Purple_24: -308179443233466684
      Torch_Purple_25: -8912942771199552999
      Torch_Purple_26: 829214079118854761
      Torch_Purple_27: 6587287232380290893
      Torch_Purple_28: -3613194450452872922
      Torch_Purple_29: 4254220321314900722
      Torch_Purple_3: -2718780732279233981
      Torch_Purple_30: -3636304627072001104
      Torch_Purple_31: 2526182072521003030
      Torch_Purple_32: -534309051310876368
      Torch_Purple_33: -1031857605635688553
      Torch_Purple_34: -4039123883211088733
      Torch_Purple_35: -204375668282837498
      Torch_Purple_36: -806418910718427689
      Torch_Purple_37: 6971597377520573325
      Torch_Purple_38: -5806124124225188590
      Torch_Purple_39: -6786669139989514500
      Torch_Purple_4: -3563705805885922832
      Torch_Purple_40: 9139717770830630858
      Torch_Purple_41: 6472037063354771283
      Torch_Purple_42: -5201262000825232503
      Torch_Purple_43: -8884292802392011540
      Torch_Purple_44: -5376117467778600094
      Torch_Purple_45: 5630286481573221149
      Torch_Purple_46: 8373742750572626711
      Torch_Purple_47: 4524142071194896893
      Torch_Purple_48: 7231459763197342479
      Torch_Purple_49: -1882189263611289453
      Torch_Purple_5: 8757872799773580616
      Torch_Purple_50: 8937611363904212237
      Torch_Purple_51: -4167974887603447450
      Torch_Purple_52: 4147723058122188050
      Torch_Purple_53: 4539507612114334818
      Torch_Purple_54: 3769643294560197153
      Torch_Purple_55: 904309756318845567
      Torch_Purple_56: -3179630900763951533
      Torch_Purple_57: 8122145324355914720
      Torch_Purple_58: 5771819029894492542
      Torch_Purple_59: 2966182216872429557
      Torch_Purple_6: 6806364753432413300
      Torch_Purple_60: -3159153860480877542
      Torch_Purple_61: 7800583797527961178
      Torch_Purple_62: -5569173360199599610
      Torch_Purple_63: -5690465260101204808
      Torch_Purple_64: 1073706693892016841
      Torch_Purple_65: -4964974728139563750
      Torch_Purple_66: -3785200113508070910
      Torch_Purple_67: -697494652585742524
      Torch_Purple_68: -5442006533641337661
      Torch_Purple_69: -1025322795725769496
      Torch_Purple_7: -8676763838232251490
      Torch_Purple_70: -3255691038874216582
      Torch_Purple_71: 3955675992198382607
      Torch_Purple_72: -3196865167981344529
      Torch_Purple_73: 2529677035573766443
      Torch_Purple_74: -5139444600743104429
      Torch_Purple_8: 6587575489424863480
      Torch_Purple_9: -4649307110469801079
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

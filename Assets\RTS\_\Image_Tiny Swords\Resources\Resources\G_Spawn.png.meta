fileFormatVersion: 2
guid: 9aa2deba795a7484bbee5f09850722bd
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3250757501160694036
    second: G_Spawn_0
  - first:
      213: 9100265939887264376
    second: G_Spawn_1
  - first:
      213: 8919987342377123801
    second: G_Spawn_2
  - first:
      213: -7542521601097302501
    second: G_Spawn_3
  - first:
      213: -5752948877553487394
    second: G_Spawn_4
  - first:
      213: -4229310860450682457
    second: G_Spawn_5
  - first:
      213: -718658282037140498
    second: G_Spawn_6
  - first:
      213: -971262325599756190
    second: G_Spawn_7
  - first:
      213: -7040113038715260338
    second: G_Spawn_8
  - first:
      213: 4893413281714820766
    second: G_Spawn_9
  - first:
      213: -3462993040076851850
    second: G_Spawn_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: G_Spawn_0
      rect:
        serializedVersion: 2
        x: 42
        y: 42
        width: 46
        height: 46
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cee5164a64df2e2d0800000000000000
      internalID: -3250757501160694036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_1
      rect:
        serializedVersion: 2
        x: 164
        y: 28
        width: 58
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 872119438a3aa4e70800000000000000
      internalID: 9100265939887264376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_2
      rect:
        serializedVersion: 2
        x: 290
        y: 44
        width: 11
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9df7e5f4a392acb70800000000000000
      internalID: 8919987342377123801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_3
      rect:
        serializedVersion: 2
        x: 295
        y: 46
        width: 47
        height: 51
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1e72e7d094935790800000000000000
      internalID: -7542521601097302501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_4
      rect:
        serializedVersion: 2
        x: 335
        y: 54
        width: 17
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed1317ee97b6920b0800000000000000
      internalID: -5752948877553487394
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_5
      rect:
        serializedVersion: 2
        x: 429
        y: 46
        width: 46
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a9a2262e287e45c0800000000000000
      internalID: -4229310860450682457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_6
      rect:
        serializedVersion: 2
        x: 549
        y: 28
        width: 60
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee3a840a910d606f0800000000000000
      internalID: -718658282037140498
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_7
      rect:
        serializedVersion: 2
        x: 688
        y: 28
        width: 39
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26028e29f026582f0800000000000000
      internalID: -971262325599756190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_8
      rect:
        serializedVersion: 2
        x: 815
        y: 28
        width: 45
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e42f7cccd7e7c4e90800000000000000
      internalID: -7040113038715260338
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_9
      rect:
        serializedVersion: 2
        x: 303
        y: 28
        width: 37
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e965acb0475e8e340800000000000000
      internalID: 4893413281714820766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: G_Spawn_10
      rect:
        serializedVersion: 2
        x: 436
        y: 28
        width: 33
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 675b1928d2af0ffc0800000000000000
      internalID: -3462993040076851850
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      G_Spawn_0: -3250757501160694036
      G_Spawn_1: 9100265939887264376
      G_Spawn_10: -3462993040076851850
      G_Spawn_2: 8919987342377123801
      G_Spawn_3: -7542521601097302501
      G_Spawn_4: -5752948877553487394
      G_Spawn_5: -4229310860450682457
      G_Spawn_6: -718658282037140498
      G_Spawn_7: -971262325599756190
      G_Spawn_8: -7040113038715260338
      G_Spawn_9: 4893413281714820766
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

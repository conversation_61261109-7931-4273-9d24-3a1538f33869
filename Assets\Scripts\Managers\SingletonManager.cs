
using UnityEngine;

/// <summary>
/// كلاس أساسي لتنفيذ نمط Singleton للمديرين في اللعبة. يضمن وجود نسخة واحدة فقط من الكلاس في وقت التشغيل.
/// </summary>
/// <remarks>
/// يرث من: None
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public abstract class SingletonManager<T> : MonoBehaviour where T : MonoBehaviour
{

    /// <summary>
    /// تُستدعى هذه الدالة عند تحميل السكريبت، وتستخدم للتهيئة المبكرة قبل Start وإعداد المكونات الأساسية.
    /// </summary>
    protected virtual void Awake()
    {
        T[] managers = FindObjectsByType<T>(FindObjectsSortMode.None);

        if (managers.Length > 1)
        {
            Destroy(gameObject);
            return;
        }
    }



    /// <summary>
    /// تنفذ دالة Get وظيفة محددة في كلاس SingletonManager.
    /// </summary>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    public static T Get()
    {

        /// <summary>
        /// متغير من نوع var يستخدم في كلاس SingletonManager لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var tag = typeof(T).Name;
        /// <summary>
        /// متغير من نوع GameObject يستخدم في كلاس SingletonManager لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        GameObject managerObject = GameObject.FindWithTag(tag);

        if (managerObject != null)
        {
            return managerObject.GetComponent<T>();
        }

        /// <summary>
        /// متغير من نوع GameObject يستخدم في كلاس SingletonManager لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        GameObject go = new(tag);
        go.tag = tag;
        return go.AddComponent<T>();
    }



}// end class


fileFormatVersion: 2
guid: 7c84c0a638f3eb7488008a0faf779c25
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 57
      y: 92
      width: 43
      height: 38
    spriteID: a98b78c9d2717e34e9d7e77a606b2053
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 57, y: 92}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 57
      y: 138
      width: 43
      height: 38
    spriteID: 9d323c12da74d7e489aeaa21d706e6c6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 57, y: 138}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 57
      y: 184
      width: 43
      height: 38
    spriteID: 68efb065a54ceb943ad9535f5fd1e570
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 57, y: 184}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 4
      width: 43
      height: 38
    spriteID: 460e2c67207816f4782b915b52ef8748
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 4}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.4883721, y: -1.1351352}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 142
      width: 43
      height: 37
    spriteID: 86f339e7a35601f42aad363680cb77d9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 142}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.47826087, y: -1.1666666}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 57
      y: 4
      width: 46
      height: 36
    spriteID: 752bcf3a2210ea04f8a70593becdc971
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 57, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.47826087, y: -1.1666666}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 57
      y: 48
      width: 46
      height: 36
    spriteID: bf839df6ad0b8074d9b03da0e7e6ab36
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 57, y: 48}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.47727275, y: -1.1351352}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 4
      width: 44
      height: 37
    spriteID: b4872e54239cc134c8d842600017396b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 4}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 50
      width: 43
      height: 38
    spriteID: 8437b1a7f88fc0144bd95ff79ca84c3e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 50}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.4883721, y: -1.1052631}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 96
      width: 43
      height: 38
    spriteID: 9c8e1e29e2db4ce498716b56f5c03452
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 96}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: -0.875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 174
      width: 38
      height: 48
    spriteID: 1fda04dbba8e03c428c11994c95671f8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 174}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.46666667, y: -0.84}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 63
      width: 45
      height: 50
    spriteID: 618117f5c5c515044be1a1e5e35bb6c6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 63}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.46666667, y: -0.8235294}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 45
      height: 51
    spriteID: b6ccc77f655d58349b169bcd1055d8a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.46666667, y: -0.93333334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 121
      width: 45
      height: 45
    spriteID: a5c17ee92b61aae4a91b0d91440bb92e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 121}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.4814815, y: -1.4000001}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 162
      y: 49
      width: 54
      height: 30
    spriteID: e55714a5eb16d084288ca8acb5037363
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 162, y: 49}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3946008704
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Happy_Sheep
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: a98b78c9d2717e34e9d7e77a606b2053
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: 9d323c12da74d7e489aeaa21d706e6c6
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: 68efb065a54ceb943ad9535f5fd1e570
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: 460e2c67207816f4782b915b52ef8748
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 37
      spriteId: 86f339e7a35601f42aad363680cb77d9
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 42
        width: 46
        height: 36
      spriteId: 752bcf3a2210ea04f8a70593becdc971
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 42
        width: 46
        height: 36
      spriteId: bf839df6ad0b8074d9b03da0e7e6ab36
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 44
        height: 37
      spriteId: b4872e54239cc134c8d842600017396b
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: 8437b1a7f88fc0144bd95ff79ca84c3e
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 43
        height: 38
      spriteId: 9c8e1e29e2db4ce498716b56f5c03452
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 45
        y: 42
        width: 38
        height: 48
      spriteId: 1fda04dbba8e03c428c11994c95671f8
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 45
        height: 50
      spriteId: 618117f5c5c515044be1a1e5e35bb6c6
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 45
        height: 51
      spriteId: b6ccc77f655d58349b169bcd1055d8a8
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 42
        width: 45
        height: 45
      spriteId: a5c17ee92b61aae4a91b0d91440bb92e
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 42
        width: 54
        height: 30
      spriteId: e55714a5eb16d084288ca8acb5037363
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 256, y: 256}

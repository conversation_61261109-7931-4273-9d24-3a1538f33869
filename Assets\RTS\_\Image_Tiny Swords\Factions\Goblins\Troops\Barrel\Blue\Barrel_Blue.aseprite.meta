fileFormatVersion: 2
guid: 8ef407f1e99e19342b05098d5a50b896
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 321
      width: 50
      height: 70
    spriteID: 620d20398f1355c4ba613a0142ee183c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 321}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 399
      width: 50
      height: 70
    spriteID: 31e9305ac7d0bbc4a916e7c8a148988a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 399}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 4
      width: 58
      height: 62
    spriteID: c2139eeb11778484f9ef6d0e6469b530
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 229
      width: 42
      height: 84
    spriteID: eb5c6833ad1b91341a27006c969b9dcf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 229}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: -0.26881722}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 50
      height: 93
    spriteID: 37ab8a6961728ca4783a6bd07c8eb686
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 4
      width: 54
      height: 67
    spriteID: 81dd6528ffc482345af98e030905325f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: -0.32500002}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 181
      width: 50
      height: 80
    spriteID: c36f5e8432e357642aef390ce9c8d44c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 181}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 294
      width: 50
      height: 83
    spriteID: b412508b09478ae47b551f5490b97e84
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 294}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: -0.313253}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 385
      width: 50
      height: 83
    spriteID: dacef8f90b8f72a49bb44d63d9cfd287
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 385}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 79
      width: 54
      height: 67
    spriteID: 1258c944e1c871247b6deee714fd364b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 79}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: -0.28089887}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 105
      width: 50
      height: 89
    spriteID: 973bc6ec6b76b8348ab738e5920f86ef
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 105}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: -0.2631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 269
      width: 42
      height: 95
    spriteID: c2eab056653040f4f8097a316c7e324b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 269}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: -0.4677419}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 74
      width: 58
      height: 62
    spriteID: 2ce3afcedbed83746a1c092ef89cff10
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 74}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.51111114, y: -0.38157895}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 222
      width: 45
      height: 76
    spriteID: 6f8e0a68ba9beed43aec0e799409d358
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 222}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: -0.41428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 144
      width: 50
      height: 70
    spriteID: 0992d5ac48557494880306eed0edc7a8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 144}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: -0.3880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 181
      y: 154
      width: 54
      height: 67
    spriteID: eaac2e785bce03a4c8d036c687274a7c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 181, y: 154}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: -0.29761904}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 202
      width: 50
      height: 84
    spriteID: 9fab9910a9a505046820718edf674a82
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 202}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: -0.29069766}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 87
      width: 48
      height: 86
    spriteID: 90418205194833e4eb68405da06bc445
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 87}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.50877196, y: -0.3866667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 4
      width: 57
      height: 75
    spriteID: db3b2f99aa2b4ed47a32f11c9b0636e8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 4}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 127
      y: 4
      width: 46
      height: 79
    spriteID: d6e1aeaa9f883d24ca97795e360287b3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 127, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: -0.36708862}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 62
      y: 372
      width: 50
      height: 79
    spriteID: 1e8414a080e10424f85b2eaa7c5f5253
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 62, y: 372}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 62451815
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Barrel_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 620d20398f1355c4ba613a0142ee183c
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 31e9305ac7d0bbc4a916e7c8a148988a
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: c2139eeb11778484f9ef6d0e6469b530
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 84
      spriteId: eb5c6833ad1b91341a27006c969b9dcf
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 93
      spriteId: 37ab8a6961728ca4783a6bd07c8eb686
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 81dd6528ffc482345af98e030905325f
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 80
      spriteId: c36f5e8432e357642aef390ce9c8d44c
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: b412508b09478ae47b551f5490b97e84
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 26
        width: 50
        height: 83
      spriteId: dacef8f90b8f72a49bb44d63d9cfd287
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: 1258c944e1c871247b6deee714fd364b
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 89
      spriteId: 973bc6ec6b76b8348ab738e5920f86ef
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 25
        width: 42
        height: 95
      spriteId: c2eab056653040f4f8097a316c7e324b
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 58
        height: 62
      spriteId: 2ce3afcedbed83746a1c092ef89cff10
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 45
        height: 76
      spriteId: 6f8e0a68ba9beed43aec0e799409d358
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 70
      spriteId: 0992d5ac48557494880306eed0edc7a8
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 26
        width: 54
        height: 67
      spriteId: eaac2e785bce03a4c8d036c687274a7c
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 25
        width: 50
        height: 84
      spriteId: 9fab9910a9a505046820718edf674a82
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 25
        width: 48
        height: 86
      spriteId: 90418205194833e4eb68405da06bc445
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 29
        width: 57
        height: 75
      spriteId: db3b2f99aa2b4ed47a32f11c9b0636e8
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 29
        width: 46
        height: 79
      spriteId: d6e1aeaa9f883d24ca97795e360287b3
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 29
        width: 50
        height: 79
      spriteId: 1e8414a080e10424f85b2eaa7c5f5253
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 512, y: 512}

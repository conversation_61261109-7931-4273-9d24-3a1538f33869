fileFormatVersion: 2
guid: 3264fb0473e71fe4d9edd60796ecda0f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 2272326806082863785
    second: TNT_Purple_0
  - first:
      213: 8442763851800837645
    second: TNT_Purple_1
  - first:
      213: 7706131729204765975
    second: TNT_Purple_2
  - first:
      213: -1619773477979350560
    second: TNT_Purple_3
  - first:
      213: -6278177768651563014
    second: TNT_Purple_4
  - first:
      213: -5108154062218082071
    second: TNT_Purple_5
  - first:
      213: -4763544064708895905
    second: TNT_Purple_6
  - first:
      213: 921504102844415446
    second: TNT_Purple_7
  - first:
      213: -6639616793864583186
    second: TNT_Purple_8
  - first:
      213: 3810428144131946723
    second: TNT_Purple_9
  - first:
      213: -3688204175246324344
    second: TNT_Purple_10
  - first:
      213: -695571609631734428
    second: TNT_Purple_11
  - first:
      213: -8013072183419656653
    second: TNT_Purple_12
  - first:
      213: -8601153216086564882
    second: TNT_Purple_13
  - first:
      213: 789716195841237662
    second: TNT_Purple_14
  - first:
      213: 5075904528681374101
    second: TNT_Purple_15
  - first:
      213: 1712233574537496869
    second: TNT_Purple_16
  - first:
      213: 1389436389304967610
    second: TNT_Purple_17
  - first:
      213: -8859585433952215538
    second: TNT_Purple_18
  - first:
      213: 4509232332568973262
    second: TNT_Purple_19
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: TNT_Purple_0
      rect:
        serializedVersion: 2
        x: 57
        y: 440
        width: 88
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a2ca2ad03de88f10800000000000000
      internalID: 2272326806082863785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_1
      rect:
        serializedVersion: 2
        x: 250
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0ef815ddf8ba2570800000000000000
      internalID: 8442763851800837645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_2
      rect:
        serializedVersion: 2
        x: 443
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 715ed979afda1fa60800000000000000
      internalID: 7706131729204765975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_3
      rect:
        serializedVersion: 2
        x: 634
        y: 440
        width: 88
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0e9117c8a986589e0800000000000000
      internalID: -1619773477979350560
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_4
      rect:
        serializedVersion: 2
        x: 824
        y: 440
        width: 88
        height: 67
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: affd4c6259e6fd8a0800000000000000
      internalID: -6278177768651563014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_5
      rect:
        serializedVersion: 2
        x: 1016
        y: 440
        width: 84
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e8c58fc1f03c19b0800000000000000
      internalID: -5108154062218082071
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_6
      rect:
        serializedVersion: 2
        x: 1099
        y: 487
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5b1b1759ed74edb0800000000000000
      internalID: -4763544064708895905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_7
      rect:
        serializedVersion: 2
        x: 59
        y: 250
        width: 82
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6dd3b156917d9cc00800000000000000
      internalID: 921504102844415446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_8
      rect:
        serializedVersion: 2
        x: 252
        y: 250
        width: 82
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee7cbf444b75bd3a0800000000000000
      internalID: -6639616793864583186
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_9
      rect:
        serializedVersion: 2
        x: 443
        y: 250
        width: 85
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e8b225653c51e430800000000000000
      internalID: 3810428144131946723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_10
      rect:
        serializedVersion: 2
        x: 633
        y: 250
        width: 87
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88d8453f7ddd0dcc0800000000000000
      internalID: -3688204175246324344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_11
      rect:
        serializedVersion: 2
        x: 824
        y: 250
        width: 84
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 465cd0c6e45d856f0800000000000000
      internalID: -695571609631734428
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_12
      rect:
        serializedVersion: 2
        x: 1017
        y: 250
        width: 82
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33625b3ed49dbc090800000000000000
      internalID: -8013072183419656653
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_13
      rect:
        serializedVersion: 2
        x: 54
        y: 56
        width: 79
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eef937888b092a880800000000000000
      internalID: -8601153216086564882
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_14
      rect:
        serializedVersion: 2
        x: 242
        y: 56
        width: 78
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e926d7e22b2a5fa00800000000000000
      internalID: 789716195841237662
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_15
      rect:
        serializedVersion: 2
        x: 439
        y: 56
        width: 77
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59199d9864c317640800000000000000
      internalID: 5075904528681374101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_16
      rect:
        serializedVersion: 2
        x: 633
        y: 56
        width: 76
        height: 65
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5290ece3d5313c710800000000000000
      internalID: 1712233574537496869
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_17
      rect:
        serializedVersion: 2
        x: 824
        y: 56
        width: 75
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab12686d805484310800000000000000
      internalID: 1389436389304967610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_18
      rect:
        serializedVersion: 2
        x: 1014
        y: 56
        width: 66
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0e9d08ecfd6c0580800000000000000
      internalID: -8859585433952215538
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Purple_19
      rect:
        serializedVersion: 2
        x: 1210
        y: 56
        width: 68
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ecfca706ae2049e30800000000000000
      internalID: 4509232332568973262
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      TNT_Purple_0: 2272326806082863785
      TNT_Purple_1: 8442763851800837645
      TNT_Purple_10: -3688204175246324344
      TNT_Purple_11: -695571609631734428
      TNT_Purple_12: -8013072183419656653
      TNT_Purple_13: -8601153216086564882
      TNT_Purple_14: 789716195841237662
      TNT_Purple_15: 5075904528681374101
      TNT_Purple_16: 1712233574537496869
      TNT_Purple_17: 1389436389304967610
      TNT_Purple_18: -8859585433952215538
      TNT_Purple_19: 4509232332568973262
      TNT_Purple_2: 7706131729204765975
      TNT_Purple_3: -1619773477979350560
      TNT_Purple_4: -6278177768651563014
      TNT_Purple_5: -5108154062218082071
      TNT_Purple_6: -4763544064708895905
      TNT_Purple_7: 921504102844415446
      TNT_Purple_8: -6639616793864583186
      TNT_Purple_9: 3810428144131946723
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

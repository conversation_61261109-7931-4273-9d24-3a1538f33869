fileFormatVersion: 2
guid: 5a36092ed010b654d8b098f2fc03147f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3223729164338028631
    second: <PERSON><PERSON><PERSON>_(NoArms)_0
  - first:
      213: -96567412191507156
    second: <PERSON>_<PERSON>_(NoArms)_1
  - first:
      213: -8845085561697592675
    second: <PERSON>_<PERSON>_(NoArms)_2
  - first:
      213: 4062074198472582187
    second: <PERSON><PERSON><PERSON>_(NoArms)_3
  - first:
      213: 4290142356988600153
    second: <PERSON>_<PERSON>_(NoArms)_4
  - first:
      213: -2034798930078823670
    second: <PERSON>_<PERSON>_(NoArms)_5
  - first:
      213: 6566507175620057024
    second: <PERSON>_<PERSON>_(NoArms)_6
  - first:
      213: -4497728056676307805
    second: <PERSON>_<PERSON>_(NoArms)_7
  - first:
      213: -7381731547038081680
    second: <PERSON>_<PERSON>_(NoArms)_8
  - first:
      213: -4775831137254372169
    second: <PERSON>_<PERSON>_(NoArms)_9
  - first:
      213: 1477306327935521385
    second: Archer_Blue_(NoArms)_10
  - first:
      213: -2481950781888711879
    second: Archer_Blue_(NoArms)_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_0
      rect:
        serializedVersion: 2
        x: 67
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9af9093b7630343d0800000000000000
      internalID: -3223729164338028631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_1
      rect:
        serializedVersion: 2
        x: 259
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2d4273437ce8aef0800000000000000
      internalID: -96567412191507156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_2
      rect:
        serializedVersion: 2
        x: 451
        y: 249
        width: 57
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9a141f7b81ff3580800000000000000
      internalID: -8845085561697592675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_3
      rect:
        serializedVersion: 2
        x: 644
        y: 249
        width: 57
        height: 76
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b24ec3336f26f5830800000000000000
      internalID: 4062074198472582187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_4
      rect:
        serializedVersion: 2
        x: 836
        y: 249
        width: 57
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95f99285eb5a98b30800000000000000
      internalID: 4290142356988600153
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_5
      rect:
        serializedVersion: 2
        x: 1026
        y: 249
        width: 57
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0b8e476721f2c3e0800000000000000
      internalID: -2034798930078823670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_6
      rect:
        serializedVersion: 2
        x: 65
        y: 57
        width: 57
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c713e14c7be02b50800000000000000
      internalID: 6566507175620057024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_7
      rect:
        serializedVersion: 2
        x: 258
        y: 57
        width: 57
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ac2085992cd491c0800000000000000
      internalID: -4497728056676307805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_8
      rect:
        serializedVersion: 2
        x: 451
        y: 57
        width: 57
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07d108f9442de8990800000000000000
      internalID: -7381731547038081680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_9
      rect:
        serializedVersion: 2
        x: 641
        y: 57
        width: 57
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7bc04b452e6d8bdb0800000000000000
      internalID: -4775831137254372169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_10
      rect:
        serializedVersion: 2
        x: 834
        y: 57
        width: 57
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96298053742708410800000000000000
      internalID: 1477306327935521385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_(NoArms)_11
      rect:
        serializedVersion: 2
        x: 1027
        y: 57
        width: 57
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 933358fe9e65e8dd0800000000000000
      internalID: -2481950781888711879
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Blue_(NoArms)_0: -3223729164338028631
      Archer_Blue_(NoArms)_1: -96567412191507156
      Archer_Blue_(NoArms)_10: 1477306327935521385
      Archer_Blue_(NoArms)_11: -2481950781888711879
      Archer_Blue_(NoArms)_2: -8845085561697592675
      Archer_Blue_(NoArms)_3: 4062074198472582187
      Archer_Blue_(NoArms)_4: 4290142356988600153
      Archer_Blue_(NoArms)_5: -2034798930078823670
      Archer_Blue_(NoArms)_6: 6566507175620057024
      Archer_Blue_(NoArms)_7: -4497728056676307805
      Archer_Blue_(NoArms)_8: -7381731547038081680
      Archer_Blue_(NoArms)_9: -4775831137254372169
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

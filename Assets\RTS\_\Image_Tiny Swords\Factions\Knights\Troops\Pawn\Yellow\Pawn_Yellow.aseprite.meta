fileFormatVersion: 2
guid: b2d22750c264d104c92833a137b790ff
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 295
      width: 58
      height: 59
    spriteID: 3eee3cf422fb85d40801f03061dce27b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 295}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 295
      width: 58
      height: 59
    spriteID: ab969079d80714c45a49df723b02d885
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 295}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5172414, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 363
      width: 58
      height: 57
    spriteID: 31690a3f5020f9e48bf139846a787f65
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 363}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5172414, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 363
      width: 58
      height: 56
    spriteID: f390e67667cb8754eb0cf72fa5bdde6b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 363}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 363
      width: 58
      height: 57
    spriteID: d3fc3618259c3eb4ab9b111d2c4fafb1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 363}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 363
      width: 58
      height: 58
    spriteID: aded89a5913cdfc459c197d5fda3c80e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 363}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 295
      width: 58
      height: 59
    spriteID: b805ca4b26438494682782eabc7812dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 295}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 141
      y: 227
      width: 58
      height: 60
    spriteID: 0cad590ea4fd2f541a026755d5b49e26
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 141, y: 227}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 295
      width: 58
      height: 59
    spriteID: 4f689d7162e8e9e478246949717c29c7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 295}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 78
      width: 58
      height: 55
    spriteID: 4156c48de30713747b4e0b38f1f214b9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 78}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48387095, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 227
      width: 62
      height: 60
    spriteID: 2e40539f6cfb3804996953ce24a7e933
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 227}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.49152538, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 227
      width: 59
      height: 59
    spriteID: 5e5557eb45878b4408de4aa99708f54d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 227}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 309
      y: 78
      width: 58
      height: 55
    spriteID: 79433d6136bb5164aa6bc78ac958be44
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 309, y: 78}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.49230766, y: -0.85333335}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 150
      y: 142
      width: 65
      height: 75
    spriteID: 1a10c4e76502c4648bf7c9351e3dd674
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 150, y: 142}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 142
      width: 65
      height: 77
    spriteID: 4bb5d3a477a1e5e49bad58e39a9e0219
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 142}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 77
      y: 142
      width: 65
      height: 77
    spriteID: 2d7e5b6db816957459fcded84c377e3e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 77, y: 142}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.34782606, y: -0.969697}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 92
      height: 66
    spriteID: a0d0b9b3d609bc84ba6a4a5a8497a584
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.3483146, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 414
      y: 4
      width: 89
      height: 56
    spriteID: 9733f470d1b446443a58fea8d9967653
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 414, y: 4}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.3421052, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 389
      y: 142
      width: 76
      height: 58
    spriteID: 84961a8935c005349b05ce24e210abc5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 389, y: 142}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.654321, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 223
      y: 142
      width: 81
      height: 60
    spriteID: 3a6fe4db44c5edf41b55b37405292545
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 223, y: 142}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 222
      y: 4
      width: 88
      height: 64
    spriteID: 9a202214a378343438719b8e96963bbe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 222, y: 4}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 318
      y: 4
      width: 88
      height: 64
    spriteID: 50e86d013f1504a46b57a9747c8220e0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 318, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.3909091, y: -1.1851852}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 104
      y: 4
      width: 110
      height: 54
    spriteID: 7a83ce740121f9d4cabe1fa67142be8c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 104, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33333334, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 78
      width: 99
      height: 56
    spriteID: bb9e4d35dccd40c489d7c1f1ecde1cf5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 78}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.53623194, y: -0.8484849}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 312
      y: 142
      width: 69
      height: 66
    spriteID: 6690e7aace44673438e087106931a9d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 312, y: 142}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.49999997, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 207
      y: 227
      width: 58
      height: 60
    spriteID: 6221ef1db8863fb4f95566489fb974aa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 207, y: 227}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 295
      width: 58
      height: 59
    spriteID: 7ca334037680c754491c1b80cf9aa652
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 295}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.5172414, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 363
      width: 58
      height: 58
    spriteID: 03f9f8c263958994ebff240d7d1a8e3c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 363}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 363
      width: 58
      height: 57
    spriteID: 4015e6a7d3995a640bd2efde2b257c71
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 363}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 363
      width: 58
      height: 58
    spriteID: 487360d577e43fb469b85744c1212dbd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 363}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 295
      width: 58
      height: 59
    spriteID: 948f65b44cc55dc4488315ee78ee1f8f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 295}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 227
      width: 58
      height: 60
    spriteID: b3d5e4d16eb534248b61c1ff3a4d4c9c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 227}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.5172414, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 339
      y: 227
      width: 58
      height: 60
    spriteID: 33a29ef952d798145b8561987abea70a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 339, y: 227}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 78
      width: 58
      height: 56
    spriteID: ec5eda1e8d9c3274585010aab61f6871
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 78}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.46551725, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 405
      y: 227
      width: 58
      height: 60
    spriteID: 9b7dd9c7e633a1345b1f27eb008c370f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 405, y: 227}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.48275867, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 295
      width: 58
      height: 60
    spriteID: 6b0ca83276050a34e80d05f928809d24
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 295}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 177
      y: 78
      width: 58
      height: 56
    spriteID: 934a8a098fd46224e81373fc5e9b1e01
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 177, y: 78}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1640555859
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Pawn_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: 3eee3cf422fb85d40801f03061dce27b
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: ab969079d80714c45a49df723b02d885
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 57
      spriteId: 31690a3f5020f9e48bf139846a787f65
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 56
      spriteId: f390e67667cb8754eb0cf72fa5bdde6b
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: d3fc3618259c3eb4ab9b111d2c4fafb1
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: aded89a5913cdfc459c197d5fda3c80e
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: b805ca4b26438494682782eabc7812dc
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 0cad590ea4fd2f541a026755d5b49e26
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: 4f689d7162e8e9e478246949717c29c7
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: 4156c48de30713747b4e0b38f1f214b9
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 62
        height: 60
      spriteId: 2e40539f6cfb3804996953ce24a7e933
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 59
        height: 59
      spriteId: 5e5557eb45878b4408de4aa99708f54d
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: 79433d6136bb5164aa6bc78ac958be44
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 65
        height: 75
      spriteId: 1a10c4e76502c4648bf7c9351e3dd674
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 4bb5d3a477a1e5e49bad58e39a9e0219
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 2d7e5b6db816957459fcded84c377e3e
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 92
        height: 66
      spriteId: a0d0b9b3d609bc84ba6a4a5a8497a584
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 89
        height: 56
      spriteId: 9733f470d1b446443a58fea8d9967653
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 64
        width: 76
        height: 58
      spriteId: 84961a8935c005349b05ce24e210abc5
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 64
        width: 81
        height: 60
      spriteId: 3a6fe4db44c5edf41b55b37405292545
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: 9a202214a378343438719b8e96963bbe
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: 50e86d013f1504a46b57a9747c8220e0
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 64
        width: 110
        height: 54
      spriteId: 7a83ce740121f9d4cabe1fa67142be8c
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 99
        height: 56
      spriteId: bb9e4d35dccd40c489d7c1f1ecde1cf5
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 56
        width: 69
        height: 66
      spriteId: 6690e7aace44673438e087106931a9d2
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 60
      spriteId: 6221ef1db8863fb4f95566489fb974aa
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: 7ca334037680c754491c1b80cf9aa652
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 58
      spriteId: 03f9f8c263958994ebff240d7d1a8e3c
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: 4015e6a7d3995a640bd2efde2b257c71
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 487360d577e43fb469b85744c1212dbd
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: 948f65b44cc55dc4488315ee78ee1f8f
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: b3d5e4d16eb534248b61c1ff3a4d4c9c
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 60
      spriteId: 33a29ef952d798145b8561987abea70a
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: ec5eda1e8d9c3274585010aab61f6871
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 64
        width: 58
        height: 60
      spriteId: 9b7dd9c7e633a1345b1f27eb008c370f
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 60
      spriteId: 6b0ca83276050a34e80d05f928809d24
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 934a8a098fd46224e81373fc5e9b1e01
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

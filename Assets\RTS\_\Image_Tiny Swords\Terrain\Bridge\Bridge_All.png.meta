fileFormatVersion: 2
guid: e84746b79d1e1784cac751a295731650
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4502377647665524882
    second: Bridge_All_0
  - first:
      213: 2737001916041072953
    second: Bridge_All_1
  - first:
      213: -8825614285363067832
    second: Bridge_All_2
  - first:
      213: -2395568014301713462
    second: Bridge_All_3
  - first:
      213: -7021994726675330748
    second: Bridge_All_4
  - first:
      213: 1624370720205371860
    second: Bridge_All_5
  - first:
      213: -6301087618333149756
    second: Bridge_All_6
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Bridge_All_0
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29033cdad98ab7e30800000000000000
      internalID: 4502377647665524882
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_1
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 931113bcdb8cbf520800000000000000
      internalID: 2737001916041072953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_2
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 844425c419e158580800000000000000
      internalID: -8825614285363067832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_3
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acb2240559b31ced0800000000000000
      internalID: -2395568014301713462
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_4
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 449c9dd000ddc8e90800000000000000
      internalID: -7021994726675330748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_5
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d9f261509cea8610800000000000000
      internalID: 1624370720205371860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_6
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c5f3aa123a0e88a0800000000000000
      internalID: -6301087618333149756
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_7
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6db96d614e7f0334c9ef802396598ecd
      internalID: 1415558143
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_8
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 276d850fbe42d484bba7332c49fd3dd4
      internalID: -968846326
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Bridge_All_9
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7d028b0c33a175c41b6d4e94731a41cf
      internalID: 1569495426
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: b7fe20d97800ac641989cbcfd53e34fb
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Bridge_All_0: 4502377647665524882
      Bridge_All_1: 2737001916041072953
      Bridge_All_2: -8825614285363067832
      Bridge_All_3: -2395568014301713462
      Bridge_All_4: -7021994726675330748
      Bridge_All_5: 1624370720205371860
      Bridge_All_6: -6301087618333149756
      Bridge_All_7: 1415558143
      Bridge_All_8: -968846326
      Bridge_All_9: 1569495426
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

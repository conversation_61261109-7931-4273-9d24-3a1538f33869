
using UnityEngine;



public enum UnitStance { Defensive, Offensive }

[CreateAssetMenu(fileName = "StanceAction", menuName = "RTS/Actions/StanceAction")]

public class UnitStanceActionSO : ActionSO
{

    [SerializeField] private UnitStance m_UnitStance;

    public UnitStance UnitStance => m_UnitStance;




    public override void Execute(GameManager gameManager)
    {
        if (gameManager.ActiveUnit != null)
        {


            gameManager.ActiveUnit.SetUnitStance(this);

        }
    }



}// end class
fileFormatVersion: 2
guid: a0689621ca6ae0542b3448cf9d796df6
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1448252480329919801
    second: <PERSON>_<PERSON>_0
  - first:
      213: -8838482275237848885
    second: Warrior_<PERSON>_1
  - first:
      213: 5406782203547414040
    second: <PERSON>_Blue_2
  - first:
      213: 1463704688382458982
    second: <PERSON>_Blue_3
  - first:
      213: -9152939422376490735
    second: <PERSON>_Blue_4
  - first:
      213: 5013799040997709749
    second: Warrior_Blue_5
  - first:
      213: -1672519898550114602
    second: Warrior_Blue_6
  - first:
      213: -7791425953029368767
    second: <PERSON>_<PERSON>_7
  - first:
      213: 1484279566179589967
    second: <PERSON>_<PERSON>_8
  - first:
      213: -3403234807515756179
    second: <PERSON>_Blue_9
  - first:
      213: -7028422606531620881
    second: <PERSON>_<PERSON>_10
  - first:
      213: -8599349895570148482
    second: <PERSON>_<PERSON>_11
  - first:
      213: 4887455543641441687
    second: <PERSON>_<PERSON>_12
  - first:
      213: 1617535587422869692
    second: <PERSON>_Blue_13
  - first:
      213: -6733473425934966021
    second: Warrior_Blue_14
  - first:
      213: 8057981515537251145
    second: Warrior_Blue_15
  - first:
      213: -2295091820497786881
    second: <PERSON>_<PERSON>_16
  - first:
      213: -4070611951465040981
    second: Warrior_Blue_17
  - first:
      213: -6020158371915913484
    second: Warrior_Blue_18
  - first:
      213: 6852195566242143941
    second: Warrior_Blue_19
  - first:
      213: 8623725918659698721
    second: Warrior_Blue_20
  - first:
      213: -7630331419334528658
    second: Warrior_Blue_21
  - first:
      213: 8250042118999599827
    second: Warrior_Blue_22
  - first:
      213: 1831561948527194828
    second: Warrior_Blue_23
  - first:
      213: 5737398224882651483
    second: Warrior_Blue_24
  - first:
      213: 686275002757285879
    second: Warrior_Blue_25
  - first:
      213: -6820794401073396575
    second: Warrior_Blue_26
  - first:
      213: -8322565654407655767
    second: Warrior_Blue_27
  - first:
      213: 999363956346284212
    second: Warrior_Blue_28
  - first:
      213: 920778665628228539
    second: Warrior_Blue_29
  - first:
      213: -1862056862079330845
    second: Warrior_Blue_30
  - first:
      213: -175862040222990888
    second: Warrior_Blue_31
  - first:
      213: 2103004666898697155
    second: Warrior_Blue_32
  - first:
      213: -4121134383873880558
    second: Warrior_Blue_33
  - first:
      213: -1349581481503386264
    second: Warrior_Blue_34
  - first:
      213: -2586977061673965500
    second: Warrior_Blue_35
  - first:
      213: -3057998330571279762
    second: Warrior_Blue_36
  - first:
      213: 4015574649506354893
    second: Warrior_Blue_37
  - first:
      213: -232515625000527906
    second: Warrior_Blue_38
  - first:
      213: 807218313078530972
    second: Warrior_Blue_39
  - first:
      213: 675785488431832003
    second: Warrior_Blue_40
  - first:
      213: 8958472748343319348
    second: Warrior_Blue_41
  - first:
      213: 8782361303006435773
    second: Warrior_Blue_42
  - first:
      213: 5704183421965384451
    second: Warrior_Blue_43
  - first:
      213: -2183790841163557842
    second: Warrior_Blue_44
  - first:
      213: 9051283987780761484
    second: Warrior_Blue_45
  - first:
      213: -4985013248184194703
    second: Warrior_Blue_46
  - first:
      213: -2110690741743447410
    second: Warrior_Blue_47
  - first:
      213: 5597869990218008791
    second: Warrior_Blue_48
  - first:
      213: -7490071170321743814
    second: Warrior_Blue_49
  - first:
      213: -3107757338139221465
    second: Warrior_Blue_50
  - first:
      213: 5047536160546663753
    second: Warrior_Blue_51
  - first:
      213: -4148541266878104647
    second: Warrior_Blue_52
  - first:
      213: 429336043104486409
    second: Warrior_Blue_53
  - first:
      213: -3187246170801061930
    second: Warrior_Blue_54
  - first:
      213: 1547359339963549388
    second: Warrior_Blue_55
  - first:
      213: -6021040159146955871
    second: Warrior_Blue_56
  - first:
      213: 5150042056499323221
    second: Warrior_Blue_57
  - first:
      213: -3150262331105059838
    second: Warrior_Blue_58
  - first:
      213: 2551082361249137551
    second: Warrior_Blue_59
  - first:
      213: -8876832668839517277
    second: Warrior_Blue_60
  - first:
      213: -8033281151075340229
    second: Warrior_Blue_61
  - first:
      213: 5177879902043176195
    second: Warrior_Blue_62
  - first:
      213: -2496806188035233067
    second: Warrior_Blue_63
  - first:
      213: 6412799187542958021
    second: Warrior_Blue_64
  - first:
      213: 6942968119378836813
    second: Warrior_Blue_65
  - first:
      213: -7023176546439364684
    second: Warrior_Blue_66
  - first:
      213: -3115017436996339825
    second: Warrior_Blue_67
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_Blue_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 93da42b25f9391410800000000000000
      internalID: 1448252480329919801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_1
      rect:
        serializedVersion: 2
        x: 192
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bc89cdee237675580800000000000000
      internalID: -8838482275237848885
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_2
      rect:
        serializedVersion: 2
        x: 384
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 81ee85c84cfb80b40800000000000000
      internalID: 5406782203547414040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_3
      rect:
        serializedVersion: 2
        x: 576
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66c3c17b8af105410800000000000000
      internalID: 1463704688382458982
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_4
      rect:
        serializedVersion: 2
        x: 768
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1198496f61a3af080800000000000000
      internalID: -9152939422376490735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_5
      rect:
        serializedVersion: 2
        x: 960
        y: 1344
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b7dcddd9a7949540800000000000000
      internalID: 5013799040997709749
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_6
      rect:
        serializedVersion: 2
        x: 0
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d6e52f73040ac8e0800000000000000
      internalID: -1672519898550114602
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_7
      rect:
        serializedVersion: 2
        x: 192
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 144dcef506b4fd390800000000000000
      internalID: -7791425953029368767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_8
      rect:
        serializedVersion: 2
        x: 384
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4f5181c668399410800000000000000
      internalID: 1484279566179589967
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_9
      rect:
        serializedVersion: 2
        x: 576
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6947c249f745c0d0800000000000000
      internalID: -3403234807515756179
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_10
      rect:
        serializedVersion: 2
        x: 768
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fef8b56a0e6067e90800000000000000
      internalID: -7028422606531620881
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_11
      rect:
        serializedVersion: 2
        x: 960
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e73bf3fc4d8f8a880800000000000000
      internalID: -8599349895570148482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_12
      rect:
        serializedVersion: 2
        x: 0
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 795034e0ceab3d340800000000000000
      internalID: 4887455543641441687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_13
      rect:
        serializedVersion: 2
        x: 192
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb894e3fb04a27610800000000000000
      internalID: 1617535587422869692
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_14
      rect:
        serializedVersion: 2
        x: 384
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf22d963895ed82a0800000000000000
      internalID: -6733473425934966021
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_15
      rect:
        serializedVersion: 2
        x: 576
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 94bf240de73b3df60800000000000000
      internalID: 8057981515537251145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_16
      rect:
        serializedVersion: 2
        x: 768
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff7235036223620e0800000000000000
      internalID: -2295091820497786881
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_17
      rect:
        serializedVersion: 2
        x: 960
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba377764ff74287c0800000000000000
      internalID: -4070611951465040981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_18
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f6ab5e5bd9147ca0800000000000000
      internalID: -6020158371915913484
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_19
      rect:
        serializedVersion: 2
        x: 192
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ce9e512093e71f50800000000000000
      internalID: 6852195566242143941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_20
      rect:
        serializedVersion: 2
        x: 384
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1206a065801ada770800000000000000
      internalID: 8623725918659698721
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_21
      rect:
        serializedVersion: 2
        x: 576
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e613018500e9b1690800000000000000
      internalID: -7630331419334528658
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_22
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3da6bfcb8990e7270800000000000000
      internalID: 8250042118999599827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_23
      rect:
        serializedVersion: 2
        x: 960
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc2f51e53e30b6910800000000000000
      internalID: 1831561948527194828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_24
      rect:
        serializedVersion: 2
        x: 0
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b51561999455f9f40800000000000000
      internalID: 5737398224882651483
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_25
      rect:
        serializedVersion: 2
        x: 192
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f7049d9a73268900800000000000000
      internalID: 686275002757285879
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_26
      rect:
        serializedVersion: 2
        x: 384
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a40c0e51aba751a0800000000000000
      internalID: -6820794401073396575
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_27
      rect:
        serializedVersion: 2
        x: 576
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ae4a25d1ae408c80800000000000000
      internalID: -8322565654407655767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_28
      rect:
        serializedVersion: 2
        x: 768
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b8695339347edd00800000000000000
      internalID: 999363956346284212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_29
      rect:
        serializedVersion: 2
        x: 960
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bbb5781615347cc00800000000000000
      internalID: 920778665628228539
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_30
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e5de284625a826e0800000000000000
      internalID: -1862056862079330845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_31
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dd780bf7663f8df0800000000000000
      internalID: -175862040222990888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_32
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3cb4c733a9f5f2d10800000000000000
      internalID: 2103004666898697155
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_33
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21e8c81dc1acec6c0800000000000000
      internalID: -4121134383873880558
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_34
      rect:
        serializedVersion: 2
        x: 768
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8659b8ffac2554de0800000000000000
      internalID: -1349581481503386264
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_35
      rect:
        serializedVersion: 2
        x: 960
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 448e6dc9116391cd0800000000000000
      internalID: -2586977061673965500
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_36
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e62bf8668becf85d0800000000000000
      internalID: -3057998330571279762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_37
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc6d3e84edf2ab730800000000000000
      internalID: 4015574649506354893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_38
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed79d1f5540f5ccf0800000000000000
      internalID: -232515625000527906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_39
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c936c3767c0d33b00800000000000000
      internalID: 807218313078530972
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_40
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3cbd75e325fd06900800000000000000
      internalID: 675785488431832003
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_41
      rect:
        serializedVersion: 2
        x: 960
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43b55072083e25c70800000000000000
      internalID: 8958472748343319348
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_42
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db9d5daf21731e970800000000000000
      internalID: 8782361303006435773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_43
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30b3be7a994592f40800000000000000
      internalID: 5704183421965384451
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_44
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e24d1b8dccd91b1e0800000000000000
      internalID: -2183790841163557842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_45
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c877ad4c4de9c9d70800000000000000
      internalID: 9051283987780761484
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_46
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1796d5dccdca1dab0800000000000000
      internalID: -4985013248184194703
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Blue_47
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e821dab04f155b2e0800000000000000
      internalID: -2110690741743447410
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 89eeedb49cbab634ebfde835cf90b009
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Warrior_Blue_0: 1448252480329919801
      Warrior_Blue_1: -8838482275237848885
      Warrior_Blue_10: -7028422606531620881
      Warrior_Blue_11: -8599349895570148482
      Warrior_Blue_12: 4887455543641441687
      Warrior_Blue_13: 1617535587422869692
      Warrior_Blue_14: -6733473425934966021
      Warrior_Blue_15: 8057981515537251145
      Warrior_Blue_16: -2295091820497786881
      Warrior_Blue_17: -4070611951465040981
      Warrior_Blue_18: -6020158371915913484
      Warrior_Blue_19: 6852195566242143941
      Warrior_Blue_2: 5406782203547414040
      Warrior_Blue_20: 8623725918659698721
      Warrior_Blue_21: -7630331419334528658
      Warrior_Blue_22: 8250042118999599827
      Warrior_Blue_23: 1831561948527194828
      Warrior_Blue_24: 5737398224882651483
      Warrior_Blue_25: 686275002757285879
      Warrior_Blue_26: -6820794401073396575
      Warrior_Blue_27: -8322565654407655767
      Warrior_Blue_28: 999363956346284212
      Warrior_Blue_29: 920778665628228539
      Warrior_Blue_3: 1463704688382458982
      Warrior_Blue_30: -1862056862079330845
      Warrior_Blue_31: -175862040222990888
      Warrior_Blue_32: 2103004666898697155
      Warrior_Blue_33: -4121134383873880558
      Warrior_Blue_34: -1349581481503386264
      Warrior_Blue_35: -2586977061673965500
      Warrior_Blue_36: -3057998330571279762
      Warrior_Blue_37: 4015574649506354893
      Warrior_Blue_38: -232515625000527906
      Warrior_Blue_39: 807218313078530972
      Warrior_Blue_4: -9152939422376490735
      Warrior_Blue_40: 675785488431832003
      Warrior_Blue_41: 8958472748343319348
      Warrior_Blue_42: 8782361303006435773
      Warrior_Blue_43: 5704183421965384451
      Warrior_Blue_44: -2183790841163557842
      Warrior_Blue_45: 9051283987780761484
      Warrior_Blue_46: -4985013248184194703
      Warrior_Blue_47: -2110690741743447410
      Warrior_Blue_5: 5013799040997709749
      Warrior_Blue_6: -1672519898550114602
      Warrior_Blue_7: -7791425953029368767
      Warrior_Blue_8: 1484279566179589967
      Warrior_Blue_9: -3403234807515756179
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: 3cedf98c409f97844b73ba192adabfbf
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.059561126, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 638
      height: 482
    spriteID: 4c367111a604e444ca5eb03cf2b24801
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.053627763, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 648
      y: 984
      width: 634
      height: 482
    spriteID: 1f5f8f535e660fe48a460fb4d041efe4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 648, y: 984}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.05660377, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 984
      width: 636
      height: 482
    spriteID: 571bec465289f37409ba1d3b106d2f98
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 984}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.058084775, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 650
      y: 494
      width: 637
      height: 482
    spriteID: 67ee035a1d19ea544a0cdbd84483a490
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 650, y: 494}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.058084775, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1295
      y: 494
      width: 637
      height: 482
    spriteID: acaced856c2566c478dcdd16105b1de4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1295, y: 494}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.059561126, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 650
      y: 4
      width: 638
      height: 482
    spriteID: a43e674c629e713498f21c1ad4d4709c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 650, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.059561126, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1296
      y: 4
      width: 638
      height: 482
    spriteID: be8a9270d1f1a174ca345040def17e81
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1296, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.059561126, y: 0.81535274}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 494
      width: 638
      height: 482
    spriteID: 09572de49dc9c5b44bc37878d2f45d1f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 494}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5121951, y: -1.4193548}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1579
      y: 984
      width: 41
      height: 31
    spriteID: f39d818c23b42504994d47da64aea54f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1579, y: 984}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.53658533, y: -1.5000001}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1628
      y: 984
      width: 41
      height: 30
    spriteID: 9937485a422930740a3d6e84b57c607d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1628, y: 984}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.54545456, y: -1.4193548}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1527
      y: 984
      width: 44
      height: 31
    spriteID: 223b20f8d285ef644bb87e05b0a448df
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1527, y: 984}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.54347825, y: -1.34375}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 943
      width: 46
      height: 32
    spriteID: fa53e721e84c0ad408f76dcbac7b0b7f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 943}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5319149, y: -1.2058823}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1366
      y: 984
      width: 47
      height: 34
    spriteID: 28253b7ad70b561458f522f1475f3633
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1366, y: 984}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5217391, y: -1.2058823}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1421
      y: 984
      width: 46
      height: 34
    spriteID: 7ed0a984d14b64f47922241a9447564d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1421, y: 984}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5227273, y: -1.2058823}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1475
      y: 984
      width: 44
      height: 34
    spriteID: 860fd03865092bd4a92a63ed0b45764d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1475, y: 984}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.53488374, y: -1.2727273}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1994
      y: 943
      width: 43
      height: 33
    spriteID: 629bb496e7396ec44b0ee2bfe2e34bf7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1994, y: 943}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.46153846, y: -0.78846157}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1290
      y: 1043
      width: 65
      height: 52
    spriteID: 6ad38fa29aba12f49b3617d4980aa861
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1290, y: 1043}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.45588234, y: -0.8235294}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1290
      y: 984
      width: 68
      height: 51
    spriteID: 297ce8058b240b04b90805db33c61f25
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1290, y: 984}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.45070422, y: -0.754717}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 821
      width: 71
      height: 53
    spriteID: b8a61e68b2e04e14b9297ac429a71f9f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 821}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.46575344, y: -0.6909091}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 696
      width: 73
      height: 55
    spriteID: 33d7894afc96541498edcbeffd102f02
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 696}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.46666667, y: -0.6607143}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 632
      width: 75
      height: 56
    spriteID: 98465f6eadde2144dbd1b98598295cbf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 632}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.46666667, y: -0.6909091}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 431
      width: 75
      height: 55
    spriteID: 851aedbbae92ebd46a638872c680e8d3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 431}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.47945204, y: -0.7222222}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 759
      width: 73
      height: 54
    spriteID: 106d69394432cba47a679561b5f68361
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 759}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.47887322, y: -0.754717}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 882
      width: 71
      height: 53
    spriteID: 4a62559332935d4499f2ca6c96e3f660
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 882}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.4819277, y: -0.516129}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 562
      width: 83
      height: 62
    spriteID: bde3af42875fd5046b1e7ded757df70a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 562}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.47674417, y: -0.5666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 363
      width: 86
      height: 60
    spriteID: 09a631fe3308a3a4b9366b68572100f8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 363}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.47674417, y: -0.5666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1940
      y: 494
      width: 86
      height: 60
    spriteID: bfd3087935bb81d4dbdaf9f766dd3d0e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1940, y: 494}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.4888889, y: -0.49206352}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 221
      width: 90
      height: 63
    spriteID: 872d37b6500b75444b728392a9d0af16
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 221}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.48351648, y: -0.44615385}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 4
      width: 91
      height: 65
    spriteID: a79c69c51344b7346b25cbf24eeffe81
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 4}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.48351648, y: -0.46875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 77
      width: 91
      height: 64
    spriteID: 656e16d1139030a429ba49c999cfad74
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 77}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.48351648, y: -0.46875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 149
      width: 91
      height: 64
    spriteID: 7784131601b06f94dbfb5384aa89ed4b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 149}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.4888889, y: -0.49206352}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 1942
      y: 292
      width: 90
      height: 63
    spriteID: 1f74d21cb36b33d48a4cf9b88b9f6c14
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1942, y: 292}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 1151510412
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Rocks
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 26
        y: -393
        width: 638
        height: 482
      spriteId: 4c367111a604e444ca5eb03cf2b24801
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 30
        y: -393
        width: 634
        height: 482
      spriteId: 1f5f8f535e660fe48a460fb4d041efe4
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: -393
        width: 636
        height: 482
      spriteId: 571bec465289f37409ba1d3b106d2f98
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 27
        y: -393
        width: 637
        height: 482
      spriteId: 67ee035a1d19ea544a0cdbd84483a490
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 27
        y: -393
        width: 637
        height: 482
      spriteId: acaced856c2566c478dcdd16105b1de4
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 26
        y: -393
        width: 638
        height: 482
      spriteId: a43e674c629e713498f21c1ad4d4709c
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 26
        y: -393
        width: 638
        height: 482
      spriteId: be8a9270d1f1a174ca345040def17e81
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 26
        y: -393
        width: 638
        height: 482
      spriteId: 09572de49dc9c5b44bc37878d2f45d1f
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 44
        width: 41
        height: 31
      spriteId: f39d818c23b42504994d47da64aea54f
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 42
        y: 45
        width: 41
        height: 30
      spriteId: 9937485a422930740a3d6e84b57c607d
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 44
        width: 44
        height: 31
      spriteId: 223b20f8d285ef644bb87e05b0a448df
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 43
        width: 46
        height: 32
      spriteId: fa53e721e84c0ad408f76dcbac7b0b7f
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 41
        width: 47
        height: 34
      spriteId: 28253b7ad70b561458f522f1475f3633
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 41
        width: 46
        height: 34
      spriteId: 7ed0a984d14b64f47922241a9447564d
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 41
        width: 44
        height: 34
      spriteId: 860fd03865092bd4a92a63ed0b45764d
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 42
        width: 43
        height: 33
      spriteId: 629bb496e7396ec44b0ee2bfe2e34bf7
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 34
        y: 41
        width: 65
        height: 52
      spriteId: 6ad38fa29aba12f49b3617d4980aa861
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 33
        y: 42
        width: 68
        height: 51
      spriteId: 297ce8058b240b04b90805db33c61f25
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 32
        y: 40
        width: 71
        height: 53
      spriteId: b8a61e68b2e04e14b9297ac429a71f9f
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 30
        y: 38
        width: 73
        height: 55
      spriteId: 33d7894afc96541498edcbeffd102f02
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 29
        y: 37
        width: 75
        height: 56
      spriteId: 98465f6eadde2144dbd1b98598295cbf
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 29
        y: 38
        width: 75
        height: 55
      spriteId: 851aedbbae92ebd46a638872c680e8d3
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 29
        y: 39
        width: 73
        height: 54
      spriteId: 106d69394432cba47a679561b5f68361
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 30
        y: 40
        width: 71
        height: 53
      spriteId: 4a62559332935d4499f2ca6c96e3f660
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 24
        y: 32
        width: 83
        height: 62
      spriteId: bde3af42875fd5046b1e7ded757df70a
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 23
        y: 34
        width: 86
        height: 60
      spriteId: 09a631fe3308a3a4b9366b68572100f8
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 23
        y: 34
        width: 86
        height: 60
      spriteId: bfd3087935bb81d4dbdaf9f766dd3d0e
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 31
        width: 90
        height: 63
      spriteId: 872d37b6500b75444b728392a9d0af16
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 29
        width: 91
        height: 65
      spriteId: a79c69c51344b7346b25cbf24eeffe81
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 30
        width: 91
        height: 64
      spriteId: 656e16d1139030a429ba49c999cfad74
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 30
        width: 91
        height: 64
      spriteId: 7784131601b06f94dbfb5384aa89ed4b
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 31
        width: 90
        height: 63
      spriteId: 1f74d21cb36b33d48a4cf9b88b9f6c14
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 128, y: 128}
  previousTextureSize: {x: 2048, y: 2048}

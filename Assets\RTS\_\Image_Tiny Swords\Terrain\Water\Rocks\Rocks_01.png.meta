fileFormatVersion: 2
guid: 379107d3e6dca674f8c15fa15e61b012
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -6591760578097145755
    second: Rocks_01_0
  - first:
      213: 9101035669822395816
    second: Rocks_01_1
  - first:
      213: 3234294262057147555
    second: Rocks_01_2
  - first:
      213: -5566335258800779854
    second: Rocks_01_3
  - first:
      213: -7628692014010301821
    second: Rocks_01_4
  - first:
      213: -3089720072195947750
    second: Rocks_01_5
  - first:
      213: 6910502441185169791
    second: Rocks_01_6
  - first:
      213: -8412000465415408234
    second: Rocks_01_7
  - first:
      213: -4944851302227151574
    second: Rocks_01_8
  - first:
      213: -856173245036380311
    second: Rocks_01_9
  - first:
      213: -4884028467403954897
    second: Rocks_01_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Rocks_01_0
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 568981acdac5584a0800000000000000
      internalID: -6591760578097145755
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_1
      rect:
        serializedVersion: 2
        x: 160
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a55cebe8bf5d4e70800000000000000
      internalID: 9101035669822395816
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_2
      rect:
        serializedVersion: 2
        x: 288
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ac100dce7582ec20800000000000000
      internalID: 3234294262057147555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_3
      rect:
        serializedVersion: 2
        x: 416
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2bd8276e29760c2b0800000000000000
      internalID: -5566335258800779854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_4
      rect:
        serializedVersion: 2
        x: 544
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3828e042801712690800000000000000
      internalID: -7628692014010301821
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_5
      rect:
        serializedVersion: 2
        x: 672
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a13cd51e6fb1f15d0800000000000000
      internalID: -3089720072195947750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_6
      rect:
        serializedVersion: 2
        x: 800
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f71df4a5b5907ef50800000000000000
      internalID: 6910502441185169791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_01_7
      rect:
        serializedVersion: 2
        x: 928
        y: 32
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6957e783522924b80800000000000000
      internalID: -8412000465415408234
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 4be06bc0f0815814eb62f0aed8e8dd25
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Rocks_01_0: -6591760578097145755
      Rocks_01_1: 9101035669822395816
      Rocks_01_2: 3234294262057147555
      Rocks_01_3: -5566335258800779854
      Rocks_01_4: -7628692014010301821
      Rocks_01_5: -3089720072195947750
      Rocks_01_6: 6910502441185169791
      Rocks_01_7: -8412000465415408234
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

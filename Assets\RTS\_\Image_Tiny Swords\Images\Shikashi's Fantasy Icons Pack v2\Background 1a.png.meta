fileFormatVersion: 2
guid: 8a4de69255025c8459d9eaca18601e63
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4777139479081097320
    second: Background 1a_0
  - first:
      213: -3001454977488062393
    second: Background 1a_1
  - first:
      213: -9132438470920069432
    second: Background 1a_2
  - first:
      213: 6040291787318398315
    second: Background 1a_3
  - first:
      213: -3542338772232854491
    second: Background 1a_4
  - first:
      213: 2162988314126513528
    second: Background 1a_5
  - first:
      213: -1791528467234374295
    second: Background 1a_6
  - first:
      213: -8954994259776694782
    second: Background 1a_7
  - first:
      213: 635368839026624239
    second: Background 1a_8
  - first:
      213: -1430972443547264564
    second: Background 1a_9
  - first:
      213: 1265451692112853251
    second: Background 1a_10
  - first:
      213: -4434835250553466741
    second: Background 1a_11
  - first:
      213: 2584824593603967123
    second: Background 1a_12
  - first:
      213: -1124456393829885133
    second: Background 1a_13
  - first:
      213: 2749232997372960354
    second: Background 1a_14
  - first:
      213: -6712131571563584640
    second: Background 1a_15
  - first:
      213: 4821079458638970964
    second: Background 1a_16
  - first:
      213: -7981501131738443282
    second: Background 1a_17
  - first:
      213: -8059301080224986152
    second: Background 1a_18
  - first:
      213: -4319769552852868156
    second: Background 1a_19
  - first:
      213: 7629405576195301068
    second: Background 1a_20
  - first:
      213: -3311565783158525644
    second: Background 1a_21
  - first:
      213: -5596520761604300051
    second: Background 1a_22
  - first:
      213: 2848687695814860646
    second: Background 1a_23
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Background 1a_0
      rect:
        serializedVersion: 2
        x: 0
        y: 837
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89760e244f034bdb0800000000000000
      internalID: -4777139479081097320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_1
      rect:
        serializedVersion: 2
        x: 0
        y: 805
        width: 512
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 748fe71b990b856d0800000000000000
      internalID: -3001454977488062393
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_2
      rect:
        serializedVersion: 2
        x: 0
        y: 773
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ce92bfa89f034180800000000000000
      internalID: -9132438470920069432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_3
      rect:
        serializedVersion: 2
        x: 0
        y: 741
        width: 512
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6d82d2b06d63d350800000000000000
      internalID: 6040291787318398315
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_4
      rect:
        serializedVersion: 2
        x: 0
        y: 709
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 524a7fe97a517dec0800000000000000
      internalID: -3542338772232854491
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_5
      rect:
        serializedVersion: 2
        x: 0
        y: 676
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 875f4d4696a740e10800000000000000
      internalID: 2162988314126513528
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_6
      rect:
        serializedVersion: 2
        x: 0
        y: 645
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96dbe79d9563327e0800000000000000
      internalID: -1791528467234374295
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_7
      rect:
        serializedVersion: 2
        x: 0
        y: 613
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20619549a2879b380800000000000000
      internalID: -8954994259776694782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_8
      rect:
        serializedVersion: 2
        x: 0
        y: 581
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe6c88fb79841d800800000000000000
      internalID: 635368839026624239
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_9
      rect:
        serializedVersion: 2
        x: 0
        y: 549
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc91efae42a242ce0800000000000000
      internalID: -1430972443547264564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_10
      rect:
        serializedVersion: 2
        x: 0
        y: 517
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 309bdccfb99cf8110800000000000000
      internalID: 1265451692112853251
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_11
      rect:
        serializedVersion: 2
        x: 0
        y: 485
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b80516d16dc4472c0800000000000000
      internalID: -4434835250553466741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_12
      rect:
        serializedVersion: 2
        x: 0
        y: 453
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 398357cd5442fd320800000000000000
      internalID: 2584824593603967123
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_13
      rect:
        serializedVersion: 2
        x: 0
        y: 420
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33b5a501ed02560f0800000000000000
      internalID: -1124456393829885133
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_14
      rect:
        serializedVersion: 2
        x: 0
        y: 388
        width: 512
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26e923558dc372620800000000000000
      internalID: 2749232997372960354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_15
      rect:
        serializedVersion: 2
        x: 0
        y: 356
        width: 512
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 083a447e5e7b9d2a0800000000000000
      internalID: -6712131571563584640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_16
      rect:
        serializedVersion: 2
        x: 0
        y: 325
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 450cb16793ae7e240800000000000000
      internalID: 4821079458638970964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_17
      rect:
        serializedVersion: 2
        x: 0
        y: 293
        width: 512
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee11a5c22030c3190800000000000000
      internalID: -7981501131738443282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_18
      rect:
        serializedVersion: 2
        x: 0
        y: 262
        width: 512
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8db84471e5c972090800000000000000
      internalID: -8059301080224986152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_19
      rect:
        serializedVersion: 2
        x: 0
        y: 227
        width: 512
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c328a29a781d04c0800000000000000
      internalID: -4319769552852868156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_20
      rect:
        serializedVersion: 2
        x: 0
        y: 195
        width: 512
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cce518003f711e960800000000000000
      internalID: 7629405576195301068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_21
      rect:
        serializedVersion: 2
        x: 0
        y: 164
        width: 512
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4317f743774fa02d0800000000000000
      internalID: -3311565783158525644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_22
      rect:
        serializedVersion: 2
        x: 0
        y: 136
        width: 512
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de22a7ad40a2552b0800000000000000
      internalID: -5596520761604300051
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background 1a_23
      rect:
        serializedVersion: 2
        x: 0
        y: 104
        width: 512
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66f1219cd52988720800000000000000
      internalID: 2848687695814860646
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable: {}
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

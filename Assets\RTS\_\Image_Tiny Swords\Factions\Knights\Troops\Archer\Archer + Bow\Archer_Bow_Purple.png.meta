fileFormatVersion: 2
guid: ba883a06a360eb947b9e14ab6c2a5c33
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5954818305509195136
    second: <PERSON><PERSON><PERSON>_Purple_0
  - first:
      213: -6094693995654448287
    second: <PERSON><PERSON><PERSON>_Purple_1
  - first:
      213: -1071587763025745460
    second: <PERSON>_<PERSON>_Purple_2
  - first:
      213: 5804507461753366988
    second: <PERSON><PERSON><PERSON>_Purple_3
  - first:
      213: 8554559796906011788
    second: <PERSON><PERSON><PERSON>_Purple_4
  - first:
      213: 7358375238793224795
    second: <PERSON>_<PERSON>_Purple_5
  - first:
      213: -5974213680705263600
    second: <PERSON>_<PERSON>_Purple_6
  - first:
      213: -5371627237854584977
    second: <PERSON>_<PERSON>_Purple_7
  - first:
      213: -6284173197472853419
    second: <PERSON><PERSON><PERSON>_Purple_8
  - first:
      213: 9119340381799465656
    second: <PERSON><PERSON><PERSON>_Purple_9
  - first:
      213: 145811921840516268
    second: <PERSON><PERSON><PERSON>_<PERSON>_10
  - first:
      213: -3943285572084411785
    second: <PERSON>_<PERSON>_Purple_11
  - first:
      213: 5735297153672200939
    second: Archer_Bow_Purple_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Bow_Purple_0
      rect:
        serializedVersion: 2
        x: 68
        y: 259
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08281261d4c3c5da0800000000000000
      internalID: -5954818305509195136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_1
      rect:
        serializedVersion: 2
        x: 112
        y: 251
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16b7f4e1a1c4b6ba0800000000000000
      internalID: -6094693995654448287
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_2
      rect:
        serializedVersion: 2
        x: 46
        y: 62
        width: 48
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc9e1376b94f021f0800000000000000
      internalID: -1071587763025745460
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_3
      rect:
        serializedVersion: 2
        x: 96
        y: 63
        width: 20
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc9e9e968c0cd8050800000000000000
      internalID: 5804507461753366988
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_4
      rect:
        serializedVersion: 2
        x: 256
        y: 62
        width: 55
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8cab60bfc6e7b670800000000000000
      internalID: 8554559796906011788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_5
      rect:
        serializedVersion: 2
        x: 475
        y: 62
        width: 67
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b52ec481b433e1660800000000000000
      internalID: 7358375238793224795
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_6
      rect:
        serializedVersion: 2
        x: 641
        y: 59
        width: 74
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 018a8456f44571da0800000000000000
      internalID: -5974213680705263600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_7
      rect:
        serializedVersion: 2
        x: 828
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6f5dddbc752475b0800000000000000
      internalID: -5371627237854584977
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_8
      rect:
        serializedVersion: 2
        x: 1020
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 552999995c12ac8a0800000000000000
      internalID: -6284173197472853419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_9
      rect:
        serializedVersion: 2
        x: 1202
        y: 73
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b2480bc2c76e8e70800000000000000
      internalID: 9119340381799465656
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_10
      rect:
        serializedVersion: 2
        x: 1265
        y: 59
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cac225d9b27060200800000000000000
      internalID: 145811921840516268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_11
      rect:
        serializedVersion: 2
        x: 1409
        y: 67
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7725cf32aa2a649c0800000000000000
      internalID: -3943285572084411785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Purple_12
      rect:
        serializedVersion: 2
        x: 1454
        y: 57
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be2c0f5df5ed79f40800000000000000
      internalID: 5735297153672200939
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Bow_Purple_0: -5954818305509195136
      Archer_Bow_Purple_1: -6094693995654448287
      Archer_Bow_Purple_10: 145811921840516268
      Archer_Bow_Purple_11: -3943285572084411785
      Archer_Bow_Purple_12: 5735297153672200939
      Archer_Bow_Purple_2: -1071587763025745460
      Archer_Bow_Purple_3: 5804507461753366988
      Archer_Bow_Purple_4: 8554559796906011788
      Archer_Bow_Purple_5: 7358375238793224795
      Archer_Bow_Purple_6: -5974213680705263600
      Archer_Bow_Purple_7: -5371627237854584977
      Archer_Bow_Purple_8: -6284173197472853419
      Archer_Bow_Purple_9: 9119340381799465656
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

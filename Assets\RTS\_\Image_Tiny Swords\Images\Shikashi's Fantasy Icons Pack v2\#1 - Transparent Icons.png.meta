fileFormatVersion: 2
guid: 83c30975c93fc9f42a8f240c6b508504
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1660957820977104790
    second: '#1 - Transparent Icons_0'
  - first:
      213: -5063555655813669412
    second: '#1 - Transparent Icons_1'
  - first:
      213: 4292991442675503664
    second: '#1 - Transparent Icons_2'
  - first:
      213: 5559745257100799005
    second: '#1 - Transparent Icons_3'
  - first:
      213: -5239188096445404863
    second: '#1 - Transparent Icons_4'
  - first:
      213: -5517639455708617040
    second: '#1 - Transparent Icons_5'
  - first:
      213: -5743514602376443267
    second: '#1 - Transparent Icons_6'
  - first:
      213: 4266283910081353605
    second: '#1 - Transparent Icons_7'
  - first:
      213: -3785442492447779331
    second: '#1 - Transparent Icons_8'
  - first:
      213: 6671531605914933198
    second: '#1 - Transparent Icons_9'
  - first:
      213: -675955477173538400
    second: '#1 - Transparent Icons_10'
  - first:
      213: -8845260698105690896
    second: '#1 - Transparent Icons_11'
  - first:
      213: -430992898358896206
    second: '#1 - Transparent Icons_12'
  - first:
      213: 644604014539159327
    second: '#1 - Transparent Icons_13'
  - first:
      213: -4819437691104115080
    second: '#1 - Transparent Icons_14'
  - first:
      213: 5000730549932156952
    second: '#1 - Transparent Icons_15'
  - first:
      213: 4949717761833437216
    second: '#1 - Transparent Icons_16'
  - first:
      213: 443923434204993615
    second: '#1 - Transparent Icons_17'
  - first:
      213: 4025860936990054234
    second: '#1 - Transparent Icons_18'
  - first:
      213: -358945157454639822
    second: '#1 - Transparent Icons_19'
  - first:
      213: 8471522056027257144
    second: '#1 - Transparent Icons_20'
  - first:
      213: 3967432113722436129
    second: '#1 - Transparent Icons_21'
  - first:
      213: 6789135733701371458
    second: '#1 - Transparent Icons_22'
  - first:
      213: 1585300944779240631
    second: '#1 - Transparent Icons_23'
  - first:
      213: -3609913459329384379
    second: '#1 - Transparent Icons_24'
  - first:
      213: -1830589896716378220
    second: '#1 - Transparent Icons_25'
  - first:
      213: -6739525477993420851
    second: '#1 - Transparent Icons_26'
  - first:
      213: -8573801853638594318
    second: '#1 - Transparent Icons_27'
  - first:
      213: 2185167392590505104
    second: '#1 - Transparent Icons_28'
  - first:
      213: 9029165208296049957
    second: '#1 - Transparent Icons_29'
  - first:
      213: 7456935766170266316
    second: '#1 - Transparent Icons_30'
  - first:
      213: -3741002040469782287
    second: '#1 - Transparent Icons_31'
  - first:
      213: 2503938280021517210
    second: '#1 - Transparent Icons_32'
  - first:
      213: 2072017630249256147
    second: '#1 - Transparent Icons_33'
  - first:
      213: 6592680923324011761
    second: '#1 - Transparent Icons_34'
  - first:
      213: -5278310724755141886
    second: '#1 - Transparent Icons_35'
  - first:
      213: -7668790182515900673
    second: '#1 - Transparent Icons_36'
  - first:
      213: -2128185250591344747
    second: '#1 - Transparent Icons_37'
  - first:
      213: -1289143293111609116
    second: '#1 - Transparent Icons_38'
  - first:
      213: 1170213305474642798
    second: '#1 - Transparent Icons_39'
  - first:
      213: 3342801549201571654
    second: '#1 - Transparent Icons_40'
  - first:
      213: 4968300173270398009
    second: '#1 - Transparent Icons_41'
  - first:
      213: -5526506366134229238
    second: '#1 - Transparent Icons_42'
  - first:
      213: 8281908700173224916
    second: '#1 - Transparent Icons_43'
  - first:
      213: -526226018273890160
    second: '#1 - Transparent Icons_44'
  - first:
      213: 2918301305313514144
    second: '#1 - Transparent Icons_45'
  - first:
      213: -8328611839876763277
    second: '#1 - Transparent Icons_46'
  - first:
      213: -1922065559841573907
    second: '#1 - Transparent Icons_47'
  - first:
      213: -5365385695610608141
    second: '#1 - Transparent Icons_48'
  - first:
      213: -6447425667752118579
    second: '#1 - Transparent Icons_49'
  - first:
      213: 3495713834309950518
    second: '#1 - Transparent Icons_50'
  - first:
      213: 871815484178006130
    second: '#1 - Transparent Icons_51'
  - first:
      213: 4694007364678901417
    second: '#1 - Transparent Icons_52'
  - first:
      213: -6763620941933801878
    second: '#1 - Transparent Icons_53'
  - first:
      213: -3099154585137993449
    second: '#1 - Transparent Icons_54'
  - first:
      213: 8796641721362945431
    second: '#1 - Transparent Icons_55'
  - first:
      213: -1299688068531442217
    second: '#1 - Transparent Icons_56'
  - first:
      213: 1555435139837261901
    second: '#1 - Transparent Icons_57'
  - first:
      213: 4807586124992550770
    second: '#1 - Transparent Icons_58'
  - first:
      213: 3536865724434752150
    second: '#1 - Transparent Icons_59'
  - first:
      213: -3796326768004014218
    second: '#1 - Transparent Icons_60'
  - first:
      213: 4050698590179933922
    second: '#1 - Transparent Icons_61'
  - first:
      213: -176241297495130211
    second: '#1 - Transparent Icons_62'
  - first:
      213: -2318997842248720732
    second: '#1 - Transparent Icons_63'
  - first:
      213: -3301332275463831685
    second: '#1 - Transparent Icons_64'
  - first:
      213: -2974785909338221766
    second: '#1 - Transparent Icons_65'
  - first:
      213: 4551601812578344196
    second: '#1 - Transparent Icons_66'
  - first:
      213: 7774829997883845066
    second: '#1 - Transparent Icons_67'
  - first:
      213: 7929750135599643969
    second: '#1 - Transparent Icons_68'
  - first:
      213: -7929317182677814311
    second: '#1 - Transparent Icons_69'
  - first:
      213: -1283294107202189201
    second: '#1 - Transparent Icons_70'
  - first:
      213: -6407407995499387161
    second: '#1 - Transparent Icons_71'
  - first:
      213: -6051306012696016783
    second: '#1 - Transparent Icons_72'
  - first:
      213: -8000257237967149311
    second: '#1 - Transparent Icons_73'
  - first:
      213: -5421027211986494622
    second: '#1 - Transparent Icons_74'
  - first:
      213: -4330735260778472016
    second: '#1 - Transparent Icons_75'
  - first:
      213: 2877298791648403826
    second: '#1 - Transparent Icons_76'
  - first:
      213: -7296337056529428220
    second: '#1 - Transparent Icons_77'
  - first:
      213: 92988425954246417
    second: '#1 - Transparent Icons_78'
  - first:
      213: 5608727682196630789
    second: '#1 - Transparent Icons_79'
  - first:
      213: 5784196140317008990
    second: '#1 - Transparent Icons_80'
  - first:
      213: 625480778395568090
    second: '#1 - Transparent Icons_81'
  - first:
      213: 3475101186164888376
    second: '#1 - Transparent Icons_82'
  - first:
      213: 2242318624617250911
    second: '#1 - Transparent Icons_83'
  - first:
      213: -3548245318124798596
    second: '#1 - Transparent Icons_84'
  - first:
      213: 2985538392435456276
    second: '#1 - Transparent Icons_85'
  - first:
      213: 7594132129598752404
    second: '#1 - Transparent Icons_86'
  - first:
      213: -3666863504743473129
    second: '#1 - Transparent Icons_87'
  - first:
      213: -2367795693369098819
    second: '#1 - Transparent Icons_88'
  - first:
      213: -3218289417291907054
    second: '#1 - Transparent Icons_89'
  - first:
      213: -8407567668883137275
    second: '#1 - Transparent Icons_90'
  - first:
      213: 7003715593917701308
    second: '#1 - Transparent Icons_91'
  - first:
      213: 4496002825605101114
    second: '#1 - Transparent Icons_92'
  - first:
      213: 2568506949165742456
    second: '#1 - Transparent Icons_93'
  - first:
      213: 7431443166627799094
    second: '#1 - Transparent Icons_94'
  - first:
      213: 6471586766639389086
    second: '#1 - Transparent Icons_95'
  - first:
      213: 4960324034317517629
    second: '#1 - Transparent Icons_96'
  - first:
      213: -5893210615866333270
    second: '#1 - Transparent Icons_97'
  - first:
      213: -5470013036652095632
    second: '#1 - Transparent Icons_98'
  - first:
      213: -3474997026479423400
    second: '#1 - Transparent Icons_99'
  - first:
      213: -5155006954947433709
    second: '#1 - Transparent Icons_100'
  - first:
      213: -3806671376464348307
    second: '#1 - Transparent Icons_101'
  - first:
      213: -3237844493445452581
    second: '#1 - Transparent Icons_102'
  - first:
      213: 7771185243529891863
    second: '#1 - Transparent Icons_103'
  - first:
      213: -4057896849271182281
    second: '#1 - Transparent Icons_104'
  - first:
      213: 7204933574180001916
    second: '#1 - Transparent Icons_105'
  - first:
      213: -7345978181991845724
    second: '#1 - Transparent Icons_106'
  - first:
      213: -5417832148816377048
    second: '#1 - Transparent Icons_107'
  - first:
      213: -2717747088714839899
    second: '#1 - Transparent Icons_108'
  - first:
      213: 5555359082901882236
    second: '#1 - Transparent Icons_109'
  - first:
      213: 5236785144643497644
    second: '#1 - Transparent Icons_110'
  - first:
      213: -6464482783528114672
    second: '#1 - Transparent Icons_111'
  - first:
      213: 5476340157027685121
    second: '#1 - Transparent Icons_112'
  - first:
      213: -7045139472144817879
    second: '#1 - Transparent Icons_113'
  - first:
      213: -5084268029357557464
    second: '#1 - Transparent Icons_114'
  - first:
      213: -1086105481712370298
    second: '#1 - Transparent Icons_115'
  - first:
      213: -1062421431194723624
    second: '#1 - Transparent Icons_116'
  - first:
      213: 1108196985403501963
    second: '#1 - Transparent Icons_117'
  - first:
      213: 1949537107891184483
    second: '#1 - Transparent Icons_118'
  - first:
      213: -5025861119697972363
    second: '#1 - Transparent Icons_119'
  - first:
      213: -1814139012690985970
    second: '#1 - Transparent Icons_120'
  - first:
      213: -8600381837547592335
    second: '#1 - Transparent Icons_121'
  - first:
      213: 824011574517451391
    second: '#1 - Transparent Icons_122'
  - first:
      213: -7999296703488811233
    second: '#1 - Transparent Icons_123'
  - first:
      213: 3903232453129097103
    second: '#1 - Transparent Icons_124'
  - first:
      213: 8808591696831724770
    second: '#1 - Transparent Icons_125'
  - first:
      213: -1452950795555126347
    second: '#1 - Transparent Icons_126'
  - first:
      213: 2934422873357841234
    second: '#1 - Transparent Icons_127'
  - first:
      213: -3641768545749785796
    second: '#1 - Transparent Icons_128'
  - first:
      213: -5643652430381483761
    second: '#1 - Transparent Icons_129'
  - first:
      213: 1018383391595562092
    second: '#1 - Transparent Icons_130'
  - first:
      213: 7763498119798118100
    second: '#1 - Transparent Icons_131'
  - first:
      213: 6711139061982437163
    second: '#1 - Transparent Icons_132'
  - first:
      213: -2047667483185799951
    second: '#1 - Transparent Icons_133'
  - first:
      213: 4902729985760503732
    second: '#1 - Transparent Icons_134'
  - first:
      213: -1965184042450674564
    second: '#1 - Transparent Icons_135'
  - first:
      213: -8515625820778290877
    second: '#1 - Transparent Icons_136'
  - first:
      213: 4836490520773336786
    second: '#1 - Transparent Icons_137'
  - first:
      213: -2974677680205405963
    second: '#1 - Transparent Icons_138'
  - first:
      213: 920795045950121754
    second: '#1 - Transparent Icons_139'
  - first:
      213: -7995602025262933321
    second: '#1 - Transparent Icons_140'
  - first:
      213: 4736250125506913846
    second: '#1 - Transparent Icons_141'
  - first:
      213: 2765781605514270321
    second: '#1 - Transparent Icons_142'
  - first:
      213: 6848829298122412017
    second: '#1 - Transparent Icons_143'
  - first:
      213: 6242831123289951215
    second: '#1 - Transparent Icons_144'
  - first:
      213: 4383219170463900452
    second: '#1 - Transparent Icons_145'
  - first:
      213: 1606293185638498573
    second: '#1 - Transparent Icons_146'
  - first:
      213: -2715250291835184389
    second: '#1 - Transparent Icons_147'
  - first:
      213: -7226552096444442829
    second: '#1 - Transparent Icons_148'
  - first:
      213: -8208833853547818238
    second: '#1 - Transparent Icons_149'
  - first:
      213: 338561766869642129
    second: '#1 - Transparent Icons_150'
  - first:
      213: -4790915992033387703
    second: '#1 - Transparent Icons_151'
  - first:
      213: -3613535936517280698
    second: '#1 - Transparent Icons_152'
  - first:
      213: 4131315670145780908
    second: '#1 - Transparent Icons_153'
  - first:
      213: -5306104090653642225
    second: '#1 - Transparent Icons_154'
  - first:
      213: -7888986177424831467
    second: '#1 - Transparent Icons_155'
  - first:
      213: 5160054113365867577
    second: '#1 - Transparent Icons_156'
  - first:
      213: 194336859228450555
    second: '#1 - Transparent Icons_157'
  - first:
      213: -5434660369741667656
    second: '#1 - Transparent Icons_158'
  - first:
      213: 3475265077385779436
    second: '#1 - Transparent Icons_159'
  - first:
      213: 2141563591757456033
    second: '#1 - Transparent Icons_160'
  - first:
      213: -2867462654027373793
    second: '#1 - Transparent Icons_161'
  - first:
      213: -7896697801700847205
    second: '#1 - Transparent Icons_162'
  - first:
      213: -7830082870309883387
    second: '#1 - Transparent Icons_163'
  - first:
      213: 7381686839793280247
    second: '#1 - Transparent Icons_164'
  - first:
      213: 6553418477402450293
    second: '#1 - Transparent Icons_165'
  - first:
      213: 5995613530046249124
    second: '#1 - Transparent Icons_166'
  - first:
      213: -6077058438018896574
    second: '#1 - Transparent Icons_167'
  - first:
      213: -5011645975034163585
    second: '#1 - Transparent Icons_168'
  - first:
      213: -577862105121322638
    second: '#1 - Transparent Icons_169'
  - first:
      213: 5497963645003432635
    second: '#1 - Transparent Icons_170'
  - first:
      213: -7775863617726925083
    second: '#1 - Transparent Icons_171'
  - first:
      213: -8959270741478536579
    second: '#1 - Transparent Icons_172'
  - first:
      213: 3653258361786527596
    second: '#1 - Transparent Icons_173'
  - first:
      213: 9091771896991365568
    second: '#1 - Transparent Icons_174'
  - first:
      213: -8143364377226028434
    second: '#1 - Transparent Icons_175'
  - first:
      213: -4751428667779472978
    second: '#1 - Transparent Icons_176'
  - first:
      213: 7613968077380885141
    second: '#1 - Transparent Icons_177'
  - first:
      213: 6027675857580262171
    second: '#1 - Transparent Icons_178'
  - first:
      213: 5549134151593316301
    second: '#1 - Transparent Icons_179'
  - first:
      213: 6734600491287510883
    second: '#1 - Transparent Icons_180'
  - first:
      213: -623831946854553623
    second: '#1 - Transparent Icons_181'
  - first:
      213: -2319894896770420943
    second: '#1 - Transparent Icons_182'
  - first:
      213: -4547034833304584646
    second: '#1 - Transparent Icons_183'
  - first:
      213: 3659294944907357278
    second: '#1 - Transparent Icons_184'
  - first:
      213: 8107852699194483037
    second: '#1 - Transparent Icons_185'
  - first:
      213: 6546946139427291276
    second: '#1 - Transparent Icons_186'
  - first:
      213: -5690979520692100696
    second: '#1 - Transparent Icons_187'
  - first:
      213: -3943828925128700860
    second: '#1 - Transparent Icons_188'
  - first:
      213: -646163359410700191
    second: '#1 - Transparent Icons_189'
  - first:
      213: -63420638121548482
    second: '#1 - Transparent Icons_190'
  - first:
      213: 2443381508606744931
    second: '#1 - Transparent Icons_191'
  - first:
      213: -8061314009376636576
    second: '#1 - Transparent Icons_192'
  - first:
      213: -2180288992860003260
    second: '#1 - Transparent Icons_193'
  - first:
      213: -4614682972187989533
    second: '#1 - Transparent Icons_194'
  - first:
      213: 4488543362745833415
    second: '#1 - Transparent Icons_195'
  - first:
      213: 9162415883458505674
    second: '#1 - Transparent Icons_196'
  - first:
      213: -4645872885414918786
    second: '#1 - Transparent Icons_197'
  - first:
      213: 2273385847601384844
    second: '#1 - Transparent Icons_198'
  - first:
      213: -4680778589659479543
    second: '#1 - Transparent Icons_199'
  - first:
      213: 8046220510369768166
    second: '#1 - Transparent Icons_200'
  - first:
      213: 3724088746029840092
    second: '#1 - Transparent Icons_201'
  - first:
      213: -1017397855643254206
    second: '#1 - Transparent Icons_202'
  - first:
      213: -1071875905420615909
    second: '#1 - Transparent Icons_203'
  - first:
      213: -5183978504928954564
    second: '#1 - Transparent Icons_204'
  - first:
      213: 5304556553261870222
    second: '#1 - Transparent Icons_205'
  - first:
      213: -5448086938416646657
    second: '#1 - Transparent Icons_206'
  - first:
      213: 8347063961680083240
    second: '#1 - Transparent Icons_207'
  - first:
      213: 3007502273959510769
    second: '#1 - Transparent Icons_208'
  - first:
      213: 2774649560465684364
    second: '#1 - Transparent Icons_209'
  - first:
      213: 3362789639699432236
    second: '#1 - Transparent Icons_210'
  - first:
      213: 1929469511134880159
    second: '#1 - Transparent Icons_211'
  - first:
      213: -6327009497801404868
    second: '#1 - Transparent Icons_212'
  - first:
      213: -1544719591585537677
    second: '#1 - Transparent Icons_213'
  - first:
      213: 2728695151711614599
    second: '#1 - Transparent Icons_214'
  - first:
      213: -6196064377505024834
    second: '#1 - Transparent Icons_215'
  - first:
      213: 5610096918170748405
    second: '#1 - Transparent Icons_216'
  - first:
      213: 5801650414593611148
    second: '#1 - Transparent Icons_217'
  - first:
      213: 6613959360067823768
    second: '#1 - Transparent Icons_218'
  - first:
      213: -1409656846536078634
    second: '#1 - Transparent Icons_219'
  - first:
      213: -4212146986876241664
    second: '#1 - Transparent Icons_220'
  - first:
      213: -4214299665097453505
    second: '#1 - Transparent Icons_221'
  - first:
      213: 641801631387732010
    second: '#1 - Transparent Icons_222'
  - first:
      213: 2120130115821151688
    second: '#1 - Transparent Icons_223'
  - first:
      213: 2363076987334289773
    second: '#1 - Transparent Icons_224'
  - first:
      213: 8014162215811757358
    second: '#1 - Transparent Icons_225'
  - first:
      213: 8618861061775620381
    second: '#1 - Transparent Icons_226'
  - first:
      213: 4936558142665202443
    second: '#1 - Transparent Icons_227'
  - first:
      213: -1610602217286185177
    second: '#1 - Transparent Icons_228'
  - first:
      213: -5855690042751787076
    second: '#1 - Transparent Icons_229'
  - first:
      213: -1294292074843870104
    second: '#1 - Transparent Icons_230'
  - first:
      213: -2268882470940828041
    second: '#1 - Transparent Icons_231'
  - first:
      213: -1964973379043560768
    second: '#1 - Transparent Icons_232'
  - first:
      213: 3161528407320996213
    second: '#1 - Transparent Icons_233'
  - first:
      213: 6338326063260517915
    second: '#1 - Transparent Icons_234'
  - first:
      213: 5326726212536991409
    second: '#1 - Transparent Icons_235'
  - first:
      213: -7913775737931201815
    second: '#1 - Transparent Icons_236'
  - first:
      213: 5690984252577737782
    second: '#1 - Transparent Icons_237'
  - first:
      213: 415927729924886353
    second: '#1 - Transparent Icons_238'
  - first:
      213: -1880897976475227799
    second: '#1 - Transparent Icons_239'
  - first:
      213: 6943229999192725836
    second: '#1 - Transparent Icons_240'
  - first:
      213: -7507653759127745917
    second: '#1 - Transparent Icons_241'
  - first:
      213: -1516065280842772324
    second: '#1 - Transparent Icons_242'
  - first:
      213: 6198878768060006677
    second: '#1 - Transparent Icons_243'
  - first:
      213: 8609530406991562568
    second: '#1 - Transparent Icons_244'
  - first:
      213: 8999329588363508368
    second: '#1 - Transparent Icons_245'
  - first:
      213: 2100886949824278655
    second: '#1 - Transparent Icons_246'
  - first:
      213: 1490140054102047531
    second: '#1 - Transparent Icons_247'
  - first:
      213: -3605180235212209959
    second: '#1 - Transparent Icons_248'
  - first:
      213: 7602885990086401125
    second: '#1 - Transparent Icons_249'
  - first:
      213: 4515654773696883388
    second: '#1 - Transparent Icons_250'
  - first:
      213: 2096228065079779712
    second: '#1 - Transparent Icons_251'
  - first:
      213: 2332438382685261230
    second: '#1 - Transparent Icons_252'
  - first:
      213: 6416730700686628713
    second: '#1 - Transparent Icons_253'
  - first:
      213: 2891183210974028370
    second: '#1 - Transparent Icons_254'
  - first:
      213: 3843447133471380455
    second: '#1 - Transparent Icons_255'
  - first:
      213: 7084260402273260374
    second: '#1 - Transparent Icons_256'
  - first:
      213: -2656649436271588639
    second: '#1 - Transparent Icons_257'
  - first:
      213: -2156566711286475259
    second: '#1 - Transparent Icons_258'
  - first:
      213: -2637263055317522524
    second: '#1 - Transparent Icons_259'
  - first:
      213: 1688811969625011521
    second: '#1 - Transparent Icons_260'
  - first:
      213: 2377437269096428414
    second: '#1 - Transparent Icons_261'
  - first:
      213: -1591622488365461047
    second: '#1 - Transparent Icons_262'
  - first:
      213: -8340076296898816527
    second: '#1 - Transparent Icons_263'
  - first:
      213: -2640195610763654411
    second: '#1 - Transparent Icons_264'
  - first:
      213: 2094347899734262227
    second: '#1 - Transparent Icons_265'
  - first:
      213: -7222279969239902193
    second: '#1 - Transparent Icons_266'
  - first:
      213: -869349366459266957
    second: '#1 - Transparent Icons_267'
  - first:
      213: -5496399486977621808
    second: '#1 - Transparent Icons_268'
  - first:
      213: 5100052011271466107
    second: '#1 - Transparent Icons_269'
  - first:
      213: -5805582862310612142
    second: '#1 - Transparent Icons_270'
  - first:
      213: -6994427597859844246
    second: '#1 - Transparent Icons_271'
  - first:
      213: -7165076937192758615
    second: '#1 - Transparent Icons_272'
  - first:
      213: -4374075586693976007
    second: '#1 - Transparent Icons_273'
  - first:
      213: -5045906426337819385
    second: '#1 - Transparent Icons_274'
  - first:
      213: 8242306717869885254
    second: '#1 - Transparent Icons_275'
  - first:
      213: 7782233633226555941
    second: '#1 - Transparent Icons_276'
  - first:
      213: 305129507956900863
    second: '#1 - Transparent Icons_277'
  - first:
      213: -5092543255874090108
    second: '#1 - Transparent Icons_278'
  - first:
      213: -4811419681882681157
    second: '#1 - Transparent Icons_279'
  - first:
      213: 6434297881002808141
    second: '#1 - Transparent Icons_280'
  - first:
      213: -6068236643760676199
    second: '#1 - Transparent Icons_281'
  - first:
      213: 4575428321015257167
    second: '#1 - Transparent Icons_282'
  - first:
      213: 7429457448032749387
    second: '#1 - Transparent Icons_283'
  - first:
      213: 4932189977665061895
    second: '#1 - Transparent Icons_284'
  - first:
      213: -7921225423712148282
    second: '#1 - Transparent Icons_285'
  - first:
      213: -2398646341611770130
    second: '#1 - Transparent Icons_286'
  - first:
      213: -2995158427926355077
    second: '#1 - Transparent Icons_287'
  - first:
      213: 5793084992459526587
    second: '#1 - Transparent Icons_288'
  - first:
      213: -7523092805047809605
    second: '#1 - Transparent Icons_289'
  - first:
      213: 3919991872782948577
    second: '#1 - Transparent Icons_290'
  - first:
      213: 703913150396956066
    second: '#1 - Transparent Icons_291'
  - first:
      213: 6637464678985396907
    second: '#1 - Transparent Icons_292'
  - first:
      213: 4084329803132827641
    second: '#1 - Transparent Icons_293'
  - first:
      213: -9040379542294886161
    second: '#1 - Transparent Icons_294'
  - first:
      213: -7034991397336372983
    second: '#1 - Transparent Icons_295'
  - first:
      213: -7308149423735398121
    second: '#1 - Transparent Icons_296'
  - first:
      213: 3981409151829811708
    second: '#1 - Transparent Icons_297'
  - first:
      213: -4209174860669379406
    second: '#1 - Transparent Icons_298'
  - first:
      213: -6190561168543346724
    second: '#1 - Transparent Icons_299'
  - first:
      213: -6459033737710966338
    second: '#1 - Transparent Icons_300'
  - first:
      213: 1547687970728497341
    second: '#1 - Transparent Icons_301'
  - first:
      213: -5805196505890676585
    second: '#1 - Transparent Icons_302'
  - first:
      213: 1897637183941480077
    second: '#1 - Transparent Icons_303'
  - first:
      213: -9030493554655297559
    second: '#1 - Transparent Icons_304'
  - first:
      213: -6446775828425028083
    second: '#1 - Transparent Icons_305'
  - first:
      213: 5866392798299897224
    second: '#1 - Transparent Icons_306'
  - first:
      213: -7446882657432463668
    second: '#1 - Transparent Icons_307'
  - first:
      213: 7247120947072533235
    second: '#1 - Transparent Icons_308'
  - first:
      213: 7142111599129729038
    second: '#1 - Transparent Icons_309'
  - first:
      213: 1465020382370108940
    second: '#1 - Transparent Icons_310'
  - first:
      213: -4876532956349903609
    second: '#1 - Transparent Icons_311'
  - first:
      213: 207018971663540257
    second: '#1 - Transparent Icons_312'
  - first:
      213: 5705663078253063651
    second: '#1 - Transparent Icons_313'
  - first:
      213: 3421609232544836419
    second: '#1 - Transparent Icons_314'
  - first:
      213: -6973316087015928847
    second: '#1 - Transparent Icons_315'
  - first:
      213: -1919620839435568723
    second: '#1 - Transparent Icons_316'
  - first:
      213: -6097090503891488866
    second: '#1 - Transparent Icons_317'
  - first:
      213: 8177249570776675880
    second: '#1 - Transparent Icons_318'
  - first:
      213: 2418888529867181687
    second: '#1 - Transparent Icons_319'
  - first:
      213: 2507862107624154168
    second: '#1 - Transparent Icons_320'
  - first:
      213: 2104404852378037978
    second: '#1 - Transparent Icons_321'
  - first:
      213: -7528434824246943652
    second: '#1 - Transparent Icons_322'
  - first:
      213: 4995341392421661681
    second: '#1 - Transparent Icons_323'
  - first:
      213: -9181022972592417838
    second: '#1 - Transparent Icons_324'
  - first:
      213: 6007955504034014496
    second: '#1 - Transparent Icons_325'
  - first:
      213: -3872852002895254602
    second: '#1 - Transparent Icons_326'
  - first:
      213: -3861059058974289097
    second: '#1 - Transparent Icons_327'
  - first:
      213: 4138188092250273580
    second: '#1 - Transparent Icons_328'
  - first:
      213: -928730579375275487
    second: '#1 - Transparent Icons_329'
  - first:
      213: -3258309918560218418
    second: '#1 - Transparent Icons_330'
  - first:
      213: 2781487029450458242
    second: '#1 - Transparent Icons_331'
  - first:
      213: -309247154544276052
    second: '#1 - Transparent Icons_332'
  - first:
      213: -5572050165253805289
    second: '#1 - Transparent Icons_333'
  - first:
      213: -5632111865915401290
    second: '#1 - Transparent Icons_334'
  - first:
      213: -6260742003076988836
    second: '#1 - Transparent Icons_335'
  - first:
      213: -5028129352425028905
    second: '#1 - Transparent Icons_336'
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: '#1 - Transparent Icons_0'
      rect:
        serializedVersion: 2
        x: 2
        y: 837
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6974f3a8658ec0710800000000000000
      internalID: 1660957820977104790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_1'
      rect:
        serializedVersion: 2
        x: 34
        y: 837
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cdd5eca75f2aab9b0800000000000000
      internalID: -5063555655813669412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_2'
      rect:
        serializedVersion: 2
        x: 68
        y: 853
        width: 24
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 032177cc8f4c39b30800000000000000
      internalID: 4292991442675503664
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_3'
      rect:
        serializedVersion: 2
        x: 99
        y: 838
        width: 26
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1ce723cade282d40800000000000000
      internalID: 5559745257100799005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_4'
      rect:
        serializedVersion: 2
        x: 130
        y: 839
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 149aae74f2aaa47b0800000000000000
      internalID: -5239188096445404863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_5'
      rect:
        serializedVersion: 2
        x: 134
        y: 851
        width: 9
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b6eec316286d63b0800000000000000
      internalID: -5517639455708617040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_6'
      rect:
        serializedVersion: 2
        x: 145
        y: 851
        width: 9
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7a451b46efea40b0800000000000000
      internalID: -5743514602376443267
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_7'
      rect:
        serializedVersion: 2
        x: 164
        y: 855
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5871d5bcc92e43b30800000000000000
      internalID: 4266283910081353605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_8'
      rect:
        serializedVersion: 2
        x: 177
        y: 850
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df51bf7fb18677bc0800000000000000
      internalID: -3785442492447779331
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_9'
      rect:
        serializedVersion: 2
        x: 180
        y: 857
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec7d956f5aa069c50800000000000000
      internalID: 6671531605914933198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_10'
      rect:
        serializedVersion: 2
        x: 194
        y: 853
        width: 12
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a1771b23168e96f0800000000000000
      internalID: -675955477173538400
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_11'
      rect:
        serializedVersion: 2
        x: 201
        y: 837
        width: 21
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f4cfaf52425f3580800000000000000
      internalID: -8845260698105690896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_12'
      rect:
        serializedVersion: 2
        x: 225
        y: 849
        width: 13
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b91a901e3ec40af0800000000000000
      internalID: -430992898358896206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_13'
      rect:
        serializedVersion: 2
        x: 259
        y: 837
        width: 9
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f17b56b9fe712f800800000000000000
      internalID: 644604014539159327
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_14'
      rect:
        serializedVersion: 2
        x: 260
        y: 838
        width: 26
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8764c9454faed1db0800000000000000
      internalID: -4819437691104115080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_15'
      rect:
        serializedVersion: 2
        x: 292
        y: 837
        width: 24
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 818662ea0f9266540800000000000000
      internalID: 5000730549932156952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_16'
      rect:
        serializedVersion: 2
        x: 324
        y: 846
        width: 11
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0280684641ee0b440800000000000000
      internalID: 4949717761833437216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_17'
      rect:
        serializedVersion: 2
        x: 335
        y: 837
        width: 13
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f48924be202292600800000000000000
      internalID: 443923434204993615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_18'
      rect:
        serializedVersion: 2
        x: 66
        y: 839
        width: 28
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a57d968313bbed730800000000000000
      internalID: 4025860936990054234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_19'
      rect:
        serializedVersion: 2
        x: 163
        y: 840
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23dc114f745c40bf0800000000000000
      internalID: -358945157454639822
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_20'
      rect:
        serializedVersion: 2
        x: 169
        y: 846
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83934680d64e09570800000000000000
      internalID: 8471522056027257144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_21'
      rect:
        serializedVersion: 2
        x: 179
        y: 839
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12a27e0bc762f0730800000000000000
      internalID: 3967432113722436129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_22'
      rect:
        serializedVersion: 2
        x: 236
        y: 845
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2466e2bbafad73e50800000000000000
      internalID: 6789135733701371458
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_23'
      rect:
        serializedVersion: 2
        x: 244
        y: 840
        width: 11
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b468ab5fce100610800000000000000
      internalID: 1585300944779240631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_24'
      rect:
        serializedVersion: 2
        x: 3
        y: 806
        width: 25
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 54045cab5d207edc0800000000000000
      internalID: -3609913459329384379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_25'
      rect:
        serializedVersion: 2
        x: 35
        y: 807
        width: 26
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49f1dc410307896e0800000000000000
      internalID: -1830589896716378220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_26'
      rect:
        serializedVersion: 2
        x: 68
        y: 806
        width: 24
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc7e18909456872a0800000000000000
      internalID: -6739525477993420851
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_27'
      rect:
        serializedVersion: 2
        x: 98
        y: 806
        width: 28
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f0394bd3acb30980800000000000000
      internalID: -8573801853638594318
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_28'
      rect:
        serializedVersion: 2
        x: 130
        y: 805
        width: 29
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 094af128a26435e10800000000000000
      internalID: 2185167392590505104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_29'
      rect:
        serializedVersion: 2
        x: 4
        y: 774
        width: 24
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 52d4dca1be90e4d70800000000000000
      internalID: 9029165208296049957
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_30'
      rect:
        serializedVersion: 2
        x: 36
        y: 774
        width: 24
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ccaeb7f129b5c7760800000000000000
      internalID: 7456935766170266316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_31'
      rect:
        serializedVersion: 2
        x: 66
        y: 773
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f089c1077a451cc0800000000000000
      internalID: -3741002040469782287
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_32'
      rect:
        serializedVersion: 2
        x: 98
        y: 774
        width: 27
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a936f286996cfb220800000000000000
      internalID: 2503938280021517210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_33'
      rect:
        serializedVersion: 2
        x: 130
        y: 773
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d48a538e0941cc10800000000000000
      internalID: 2072017630249256147
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_34'
      rect:
        serializedVersion: 2
        x: 162
        y: 773
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f4d838ce58ed7b50800000000000000
      internalID: 6592680923324011761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_35'
      rect:
        serializedVersion: 2
        x: 194
        y: 773
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20bc5dc8c5cafb6b0800000000000000
      internalID: -5278310724755141886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_36'
      rect:
        serializedVersion: 2
        x: 2
        y: 741
        width: 29
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ffef222c5fbf29590800000000000000
      internalID: -7668790182515900673
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_37'
      rect:
        serializedVersion: 2
        x: 34
        y: 742
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 597f64a4aca2772e0800000000000000
      internalID: -2128185250591344747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_38'
      rect:
        serializedVersion: 2
        x: 66
        y: 741
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e8518d410b0c1ee0800000000000000
      internalID: -1289143293111609116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_39'
      rect:
        serializedVersion: 2
        x: 98
        y: 741
        width: 28
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6370402dce6d3010800000000000000
      internalID: 1170213305474642798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_40'
      rect:
        serializedVersion: 2
        x: 130
        y: 741
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64ff8ce4c44046e20800000000000000
      internalID: 3342801549201571654
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_41'
      rect:
        serializedVersion: 2
        x: 162
        y: 757
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9343b453fa2f2f440800000000000000
      internalID: 4968300173270398009
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_42'
      rect:
        serializedVersion: 2
        x: 169
        y: 741
        width: 14
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0762ad9db7ed43b0800000000000000
      internalID: -5526506366134229238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_43'
      rect:
        serializedVersion: 2
        x: 180
        y: 759
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4dbca0855104fe270800000000000000
      internalID: 8281908700173224916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_44'
      rect:
        serializedVersion: 2
        x: 195
        y: 741
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0907735793872b8f0800000000000000
      internalID: -526226018273890160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_45'
      rect:
        serializedVersion: 2
        x: 228
        y: 742
        width: 11
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a255ca4393ef7820800000000000000
      internalID: 2918301305313514144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_46'
      rect:
        serializedVersion: 2
        x: 236
        y: 757
        width: 11
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 371780498a3da6c80800000000000000
      internalID: -8328611839876763277
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_47'
      rect:
        serializedVersion: 2
        x: 259
        y: 741
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de371ae8e837355e0800000000000000
      internalID: -1922065559841573907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_48'
      rect:
        serializedVersion: 2
        x: 290
        y: 741
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f5ea2a03225a85b0800000000000000
      internalID: -5365385695610608141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_49'
      rect:
        serializedVersion: 2
        x: 322
        y: 741
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc636edd3842686a0800000000000000
      internalID: -6447425667752118579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_50'
      rect:
        serializedVersion: 2
        x: 354
        y: 741
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63c96721635438030800000000000000
      internalID: 3495713834309950518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_51'
      rect:
        serializedVersion: 2
        x: 386
        y: 742
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2708d14509f491c00800000000000000
      internalID: 871815484178006130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_52'
      rect:
        serializedVersion: 2
        x: 418
        y: 741
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9aed0c804d6742140800000000000000
      internalID: 4694007364678901417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_53'
      rect:
        serializedVersion: 2
        x: 451
        y: 741
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6e84a8a69ac222a0800000000000000
      internalID: -6763620941933801878
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_54'
      rect:
        serializedVersion: 2
        x: 482
        y: 741
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 719a3d823579df4d0800000000000000
      internalID: -3099154585137993449
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_55'
      rect:
        serializedVersion: 2
        x: 162
        y: 749
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 795183af903f31a70800000000000000
      internalID: 8796641721362945431
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_56'
      rect:
        serializedVersion: 2
        x: 180
        y: 741
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ddae54769496fde0800000000000000
      internalID: -1299688068531442217
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_57'
      rect:
        serializedVersion: 2
        x: 183
        y: 750
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d48f825c404069510800000000000000
      internalID: 1555435139837261901
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_58'
      rect:
        serializedVersion: 2
        x: 237
        y: 742
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2771747eb1af7b240800000000000000
      internalID: 4807586124992550770
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_59'
      rect:
        serializedVersion: 2
        x: 165
        y: 742
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 692203323a8751130800000000000000
      internalID: 3536865724434752150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_60'
      rect:
        serializedVersion: 2
        x: 2
        y: 713
        width: 28
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67f50d51becb05bc0800000000000000
      internalID: -3796326768004014218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_61'
      rect:
        serializedVersion: 2
        x: 34
        y: 710
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2eeb9dff7e8f63830800000000000000
      internalID: 4050698590179933922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_62'
      rect:
        serializedVersion: 2
        x: 68
        y: 709
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9f7c04497ddd8df0800000000000000
      internalID: -176241297495130211
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_63'
      rect:
        serializedVersion: 2
        x: 98
        y: 711
        width: 28
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ae1ed1cfb341dfd0800000000000000
      internalID: -2318997842248720732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_64'
      rect:
        serializedVersion: 2
        x: 130
        y: 709
        width: 27
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7f327369cf4f22d0800000000000000
      internalID: -3301332275463831685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_65'
      rect:
        serializedVersion: 2
        x: 146
        y: 723
        width: 11
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a33de0ff9ff67b6d0800000000000000
      internalID: -2974785909338221766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_66'
      rect:
        serializedVersion: 2
        x: 162
        y: 717
        width: 23
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40d91cdabb98a2f30800000000000000
      internalID: 4551601812578344196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_67'
      rect:
        serializedVersion: 2
        x: 182
        y: 709
        width: 8
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac9eae4b0beb5eb60800000000000000
      internalID: 7774829997883845066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_68'
      rect:
        serializedVersion: 2
        x: 194
        y: 709
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14db5061cb12c0e60800000000000000
      internalID: 7929750135599643969
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_69'
      rect:
        serializedVersion: 2
        x: 226
        y: 709
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d3761f980865f190800000000000000
      internalID: -7929317182677814311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_70'
      rect:
        serializedVersion: 2
        x: 232
        y: 726
        width: 17
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f688cd50fc2d03ee0800000000000000
      internalID: -1283294107202189201
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_71'
      rect:
        serializedVersion: 2
        x: 258
        y: 711
        width: 28
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e6e24140605417a0800000000000000
      internalID: -6407407995499387161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_72'
      rect:
        serializedVersion: 2
        x: 164
        y: 709
        width: 21
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 178811e1e31750ca0800000000000000
      internalID: -6051306012696016783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_73'
      rect:
        serializedVersion: 2
        x: 4
        y: 679
        width: 24
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1076833ed6069f090800000000000000
      internalID: -8000257237967149311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_74'
      rect:
        serializedVersion: 2
        x: 34
        y: 677
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26b91d7f874a4c4b0800000000000000
      internalID: -5421027211986494622
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_75'
      rect:
        serializedVersion: 2
        x: 66
        y: 677
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bd5a0db93326e3c0800000000000000
      internalID: -4330735260778472016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_76'
      rect:
        serializedVersion: 2
        x: 99
        y: 678
        width: 26
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2712dd4a1083ee720800000000000000
      internalID: 2877298791648403826
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_77'
      rect:
        serializedVersion: 2
        x: 130
        y: 677
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4015b00da143eba90800000000000000
      internalID: -7296337056529428220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_78'
      rect:
        serializedVersion: 2
        x: 161
        y: 676
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 11fb9311b7c5a4100800000000000000
      internalID: 92988425954246417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_79'
      rect:
        serializedVersion: 2
        x: 193
        y: 676
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50dea335c1436dd40800000000000000
      internalID: 5608727682196630789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_80'
      rect:
        serializedVersion: 2
        x: 225
        y: 676
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e54e7ce5eb7954050800000000000000
      internalID: 5784196140317008990
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_81'
      rect:
        serializedVersion: 2
        x: 257
        y: 677
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad74a1a34772ea800800000000000000
      internalID: 625480778395568090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_82'
      rect:
        serializedVersion: 2
        x: 289
        y: 676
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83395efed1a0a3030800000000000000
      internalID: 3475101186164888376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_83'
      rect:
        serializedVersion: 2
        x: 321
        y: 676
        width: 27
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f50cd85a8e05e1f10800000000000000
      internalID: 2242318624617250911
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_84'
      rect:
        serializedVersion: 2
        x: 353
        y: 676
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7d7a3dbea912cec0800000000000000
      internalID: -3548245318124798596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_85'
      rect:
        serializedVersion: 2
        x: 386
        y: 676
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 419f7609953ce6920800000000000000
      internalID: 2985538392435456276
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_86'
      rect:
        serializedVersion: 2
        x: 417
        y: 676
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4964d1abfe6c36960800000000000000
      internalID: 7594132129598752404
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_87'
      rect:
        serializedVersion: 2
        x: 449
        y: 676
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 710ebeff11fac1dc0800000000000000
      internalID: -3666863504743473129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_88'
      rect:
        serializedVersion: 2
        x: 483
        y: 680
        width: 27
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dbdf80cac56e32fd0800000000000000
      internalID: -2367795693369098819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_89'
      rect:
        serializedVersion: 2
        x: 2
        y: 646
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 210cae183d65653d0800000000000000
      internalID: -3218289417291907054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_90'
      rect:
        serializedVersion: 2
        x: 36
        y: 645
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50d592710c1525b80800000000000000
      internalID: -8407567668883137275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_91'
      rect:
        serializedVersion: 2
        x: 67
        y: 645
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbc1453b932323160800000000000000
      internalID: 7003715593917701308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_92'
      rect:
        serializedVersion: 2
        x: 102
        y: 645
        width: 9
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a363851cfb2056e30800000000000000
      internalID: 4496002825605101114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_93'
      rect:
        serializedVersion: 2
        x: 112
        y: 645
        width: 11
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8797524367b25a320800000000000000
      internalID: 2568506949165742456
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_94'
      rect:
        serializedVersion: 2
        x: 130
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 630aa53cf2ac12760800000000000000
      internalID: 7431443166627799094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_95'
      rect:
        serializedVersion: 2
        x: 163
        y: 645
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e99d8a650e1bfc950800000000000000
      internalID: 6471586766639389086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_96'
      rect:
        serializedVersion: 2
        x: 196
        y: 646
        width: 25
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d330454ad6c96d440800000000000000
      internalID: 4960324034317517629
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_97'
      rect:
        serializedVersion: 2
        x: 226
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa7e9be1b2c173ea0800000000000000
      internalID: -5893210615866333270
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_98'
      rect:
        serializedVersion: 2
        x: 258
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0779891ef1c9614b0800000000000000
      internalID: -5470013036652095632
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_99'
      rect:
        serializedVersion: 2
        x: 290
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 850c721ad9456cfc0800000000000000
      internalID: -3474997026479423400
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_100'
      rect:
        serializedVersion: 2
        x: 322
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31ba0b3ac7cb578b0800000000000000
      internalID: -5155006954947433709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_101'
      rect:
        serializedVersion: 2
        x: 354
        y: 645
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6bf13d3d8cfb2bc0800000000000000
      internalID: -3806671376464348307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_102'
      rect:
        serializedVersion: 2
        x: 2
        y: 615
        width: 28
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bdcaa3a869dd013d0800000000000000
      internalID: -3237844493445452581
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_103'
      rect:
        serializedVersion: 2
        x: 37
        y: 613
        width: 22
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7105b543ecbc8db60800000000000000
      internalID: 7771185243529891863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_104'
      rect:
        serializedVersion: 2
        x: 68
        y: 613
        width: 24
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73ca304b0547fa7c0800000000000000
      internalID: -4057896849271182281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_105'
      rect:
        serializedVersion: 2
        x: 100
        y: 613
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c78d84f89e01df360800000000000000
      internalID: 7204933574180001916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_106'
      rect:
        serializedVersion: 2
        x: 134
        y: 613
        width: 21
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a03b9e93c7dd0a90800000000000000
      internalID: -7345978181991845724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_107'
      rect:
        serializedVersion: 2
        x: 163
        y: 613
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82bcb159d5effc4b0800000000000000
      internalID: -5417832148816377048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_108'
      rect:
        serializedVersion: 2
        x: 198
        y: 613
        width: 20
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a8cccb0c6f984ad0800000000000000
      internalID: -2717747088714839899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_109'
      rect:
        serializedVersion: 2
        x: 227
        y: 615
        width: 26
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c754b1007a9981d40800000000000000
      internalID: 5555359082901882236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_110'
      rect:
        serializedVersion: 2
        x: 258
        y: 613
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: caea487e75ccca840800000000000000
      internalID: 5236785144643497644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_111'
      rect:
        serializedVersion: 2
        x: 290
        y: 613
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 016ad2f982b8946a0800000000000000
      internalID: -6464482783528114672
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_112'
      rect:
        serializedVersion: 2
        x: 325
        y: 613
        width: 23
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10397d0ab5edffb40800000000000000
      internalID: 5476340157027685121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_113'
      rect:
        serializedVersion: 2
        x: 354
        y: 615
        width: 28
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 921a9b11af2aa3e90800000000000000
      internalID: -7045139472144817879
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_114'
      rect:
        serializedVersion: 2
        x: 389
        y: 617
        width: 22
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82974473a2d0179b0800000000000000
      internalID: -5084268029357557464
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_115'
      rect:
        serializedVersion: 2
        x: 420
        y: 614
        width: 24
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68dfded91d06de0f0800000000000000
      internalID: -1086105481712370298
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_116'
      rect:
        serializedVersion: 2
        x: 452
        y: 613
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8da50e756558141f0800000000000000
      internalID: -1062421431194723624
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_117'
      rect:
        serializedVersion: 2
        x: 482
        y: 617
        width: 28
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8d6228694b116f00800000000000000
      internalID: 1108196985403501963
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_118'
      rect:
        serializedVersion: 2
        x: 5
        y: 581
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 363eb48aba52e0b10800000000000000
      internalID: 1949537107891184483
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_119'
      rect:
        serializedVersion: 2
        x: 37
        y: 581
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57394f590fd804ab0800000000000000
      internalID: -5025861119697972363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_120'
      rect:
        serializedVersion: 2
        x: 67
        y: 583
        width: 16
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0c306ffd22e2d6e0800000000000000
      internalID: -1814139012690985970
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_121'
      rect:
        serializedVersion: 2
        x: 81
        y: 583
        width: 15
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1752148194e45a880800000000000000
      internalID: -8600381837547592335
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_122'
      rect:
        serializedVersion: 2
        x: 99
        y: 583
        width: 16
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7a4ce5492a7f6b00800000000000000
      internalID: 824011574517451391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_123'
      rect:
        serializedVersion: 2
        x: 113
        y: 583
        width: 15
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f1f989fb70accf090800000000000000
      internalID: -7999296703488811233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_124'
      rect:
        serializedVersion: 2
        x: 131
        y: 586
        width: 26
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f8f49846c311b2630800000000000000
      internalID: 3903232453129097103
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_125'
      rect:
        serializedVersion: 2
        x: 164
        y: 581
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2ec3c157a776e3a70800000000000000
      internalID: 8808591696831724770
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_126'
      rect:
        serializedVersion: 2
        x: 195
        y: 581
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b3c0d213f416dbe0800000000000000
      internalID: -1452950795555126347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_127'
      rect:
        serializedVersion: 2
        x: 226
        y: 581
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 253764b5e0a29b820800000000000000
      internalID: 2934422873357841234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_128'
      rect:
        serializedVersion: 2
        x: 258
        y: 582
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c33a4647dc6d57dc0800000000000000
      internalID: -3641768545749785796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_129'
      rect:
        serializedVersion: 2
        x: 294
        y: 581
        width: 20
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0977531408bda1b0800000000000000
      internalID: -5643652430381483761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_130'
      rect:
        serializedVersion: 2
        x: 4
        y: 549
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c64063d8c46022e00800000000000000
      internalID: 1018383391595562092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_131'
      rect:
        serializedVersion: 2
        x: 36
        y: 549
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d6dafc386c7dbb60800000000000000
      internalID: 7763498119798118100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_132'
      rect:
        serializedVersion: 2
        x: 68
        y: 549
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2f16b37b61c22d50800000000000000
      internalID: 6711139061982437163
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_133'
      rect:
        serializedVersion: 2
        x: 100
        y: 549
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1fc9796e5493593e0800000000000000
      internalID: -2047667483185799951
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_134'
      rect:
        serializedVersion: 2
        x: 132
        y: 549
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b729ee32fef90440800000000000000
      internalID: 4902729985760503732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_135'
      rect:
        serializedVersion: 2
        x: 164
        y: 549
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7c167f76834ab4e0800000000000000
      internalID: -1965184042450674564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_136'
      rect:
        serializedVersion: 2
        x: 196
        y: 549
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 341ad580f6b62d980800000000000000
      internalID: -8515625820778290877
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_137'
      rect:
        serializedVersion: 2
        x: 228
        y: 549
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2de1d3af08aae1340800000000000000
      internalID: 4836490520773336786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_138'
      rect:
        serializedVersion: 2
        x: 260
        y: 549
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f0e21e0962d7b6d0800000000000000
      internalID: -2974677680205405963
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_139'
      rect:
        serializedVersion: 2
        x: 292
        y: 549
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a17ebc8373257cc00800000000000000
      internalID: 920795045950121754
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_140'
      rect:
        serializedVersion: 2
        x: 324
        y: 549
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ba1fce025ae90190800000000000000
      internalID: -7995602025262933321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_141'
      rect:
        serializedVersion: 2
        x: 356
        y: 549
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6362d3a356a8ab140800000000000000
      internalID: 4736250125506913846
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_142'
      rect:
        serializedVersion: 2
        x: 386
        y: 549
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 176dcfa67b7026620800000000000000
      internalID: 2765781605514270321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_143'
      rect:
        serializedVersion: 2
        x: 418
        y: 549
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ffcfd1c5fdeb0f50800000000000000
      internalID: 6848829298122412017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_144'
      rect:
        serializedVersion: 2
        x: 450
        y: 549
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: feb4378a4ddf2a650800000000000000
      internalID: 6242831123289951215
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_145'
      rect:
        serializedVersion: 2
        x: 483
        y: 549
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 423ee2cbc9254dc30800000000000000
      internalID: 4383219170463900452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_146'
      rect:
        serializedVersion: 2
        x: 3
        y: 517
        width: 25
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d010d1f4423ba4610800000000000000
      internalID: 1606293185638498573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_147'
      rect:
        serializedVersion: 2
        x: 34
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bfa53eede3e715ad0800000000000000
      internalID: -2715250291835184389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_148'
      rect:
        serializedVersion: 2
        x: 66
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 337139cf72126bb90800000000000000
      internalID: -7226552096444442829
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_149'
      rect:
        serializedVersion: 2
        x: 99
        y: 518
        width: 26
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 203d41b3a1d541e80800000000000000
      internalID: -8208833853547818238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_150'
      rect:
        serializedVersion: 2
        x: 130
        y: 519
        width: 28
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19b2bc81220d2b400800000000000000
      internalID: 338561766869642129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_151'
      rect:
        serializedVersion: 2
        x: 162
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 94b9c12e94f338db0800000000000000
      internalID: -4790915992033387703
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_152'
      rect:
        serializedVersion: 2
        x: 194
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64c67c806342addc0800000000000000
      internalID: -3613535936517280698
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_153'
      rect:
        serializedVersion: 2
        x: 226
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cac4b0e96b1655930800000000000000
      internalID: 4131315670145780908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_154'
      rect:
        serializedVersion: 2
        x: 258
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f02b757417eec56b0800000000000000
      internalID: -5306104090653642225
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_155'
      rect:
        serializedVersion: 2
        x: 293
        y: 517
        width: 22
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51c40a88ed0b48290800000000000000
      internalID: -7888986177424831467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_156'
      rect:
        serializedVersion: 2
        x: 322
        y: 517
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 93c37f080e13c9740800000000000000
      internalID: 5160054113365867577
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_157'
      rect:
        serializedVersion: 2
        x: 354
        y: 518
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bfea315085c62b200800000000000000
      internalID: 194336859228450555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_158'
      rect:
        serializedVersion: 2
        x: 387
        y: 519
        width: 27
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b2a7a210353494b0800000000000000
      internalID: -5434660369741667656
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_159'
      rect:
        serializedVersion: 2
        x: 419
        y: 517
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce856e5dc2f9a3030800000000000000
      internalID: 3475265077385779436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_160'
      rect:
        serializedVersion: 2
        x: 450
        y: 520
        width: 28
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a2ec9e7dbc58bd10800000000000000
      internalID: 2141563591757456033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_161'
      rect:
        serializedVersion: 2
        x: 486
        y: 517
        width: 20
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f13effb98e9b438d0800000000000000
      internalID: -2867462654027373793
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_162'
      rect:
        serializedVersion: 2
        x: 2
        y: 485
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b915307103b496290800000000000000
      internalID: -7896697801700847205
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_163'
      rect:
        serializedVersion: 2
        x: 38
        y: 485
        width: 20
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 502fe417d15f55390800000000000000
      internalID: -7830082870309883387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_164'
      rect:
        serializedVersion: 2
        x: 66
        y: 485
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f4ed092215017660800000000000000
      internalID: 7381686839793280247
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_165'
      rect:
        serializedVersion: 2
        x: 98
        y: 486
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 579ed0a326b62fa50800000000000000
      internalID: 6553418477402450293
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_166'
      rect:
        serializedVersion: 2
        x: 130
        y: 485
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a8beec4db2b43350800000000000000
      internalID: 5995613530046249124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_167'
      rect:
        serializedVersion: 2
        x: 163
        y: 487
        width: 25
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24de79a5c83f9aba0800000000000000
      internalID: -6077058438018896574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_168'
      rect:
        serializedVersion: 2
        x: 194
        y: 485
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7e821ed98e037ab0800000000000000
      internalID: -5011645975034163585
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_169'
      rect:
        serializedVersion: 2
        x: 226
        y: 486
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 279851f1a750bf7f0800000000000000
      internalID: -577862105121322638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_170'
      rect:
        serializedVersion: 2
        x: 258
        y: 488
        width: 28
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb201704ec0bc4c40800000000000000
      internalID: 5497963645003432635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_171'
      rect:
        serializedVersion: 2
        x: 290
        y: 486
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e6b08bec35961490800000000000000
      internalID: -7775863617726925083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_172'
      rect:
        serializedVersion: 2
        x: 325
        y: 485
        width: 22
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7a0f979ab64aa380800000000000000
      internalID: -8959270741478536579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_173'
      rect:
        serializedVersion: 2
        x: 354
        y: 485
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6fe20fcf1bf2b230800000000000000
      internalID: 3653258361786527596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_174'
      rect:
        serializedVersion: 2
        x: 388
        y: 485
        width: 25
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c9bc0bbe567c2e70800000000000000
      internalID: 9091771896991365568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_175'
      rect:
        serializedVersion: 2
        x: 418
        y: 485
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6a5fda8e35fcfe80800000000000000
      internalID: -8143364377226028434
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_176'
      rect:
        serializedVersion: 2
        x: 450
        y: 485
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea1a5610dc88f0eb0800000000000000
      internalID: -4751428667779472978
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_177'
      rect:
        serializedVersion: 2
        x: 483
        y: 485
        width: 26
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59a937730af3aa960800000000000000
      internalID: 7613968077380885141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_178'
      rect:
        serializedVersion: 2
        x: 4
        y: 456
        width: 19
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b130548a14b96a350800000000000000
      internalID: 6027675857580262171
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_179'
      rect:
        serializedVersion: 2
        x: 34
        y: 453
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcf45bc3c1c720d40800000000000000
      internalID: 5549134151593316301
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_180'
      rect:
        serializedVersion: 2
        x: 66
        y: 453
        width: 29
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36ffc53277b167d50800000000000000
      internalID: 6734600491287510883
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_181'
      rect:
        serializedVersion: 2
        x: 101
        y: 453
        width: 21
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e7a1384624b757f0800000000000000
      internalID: -623831946854553623
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_182'
      rect:
        serializedVersion: 2
        x: 132
        y: 453
        width: 22
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 13fe1c9f1e31ecfd0800000000000000
      internalID: -2319894896770420943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_183'
      rect:
        serializedVersion: 2
        x: 164
        y: 453
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3e7d8b09efa5e0c0800000000000000
      internalID: -4547034833304584646
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_184'
      rect:
        serializedVersion: 2
        x: 196
        y: 453
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5ce5975d5d68c230800000000000000
      internalID: 3659294944907357278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_185'
      rect:
        serializedVersion: 2
        x: 226
        y: 456
        width: 27
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d51edaa9211e48070800000000000000
      internalID: 8107852699194483037
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_186'
      rect:
        serializedVersion: 2
        x: 259
        y: 453
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c80cd2c93dc6bda50800000000000000
      internalID: 6546946139427291276
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_187'
      rect:
        serializedVersion: 2
        x: 291
        y: 453
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a1ef1d27449501b0800000000000000
      internalID: -5690979520692100696
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_188'
      rect:
        serializedVersion: 2
        x: 323
        y: 453
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44c0bc6ec74b449c0800000000000000
      internalID: -3943828925128700860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_189'
      rect:
        serializedVersion: 2
        x: 354
        y: 453
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 160c37f19dd5807f0800000000000000
      internalID: -646163359410700191
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_190'
      rect:
        serializedVersion: 2
        x: 397
        y: 473
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e352651044fae1ff0800000000000000
      internalID: -63420638121548482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_191'
      rect:
        serializedVersion: 2
        x: 401
        y: 468
        width: 8
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36953475982a8e120800000000000000
      internalID: 2443381508606744931
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_192'
      rect:
        serializedVersion: 2
        x: 422
        y: 464
        width: 24
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 061c9308e95702090800000000000000
      internalID: -8061314009376636576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_193'
      rect:
        serializedVersion: 2
        x: 457
        y: 453
        width: 21
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 444a2e076be0eb1e0800000000000000
      internalID: -2180288992860003260
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_194'
      rect:
        serializedVersion: 2
        x: 486
        y: 453
        width: 20
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e9e0b3594a55ffb0800000000000000
      internalID: -4614682972187989533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_195'
      rect:
        serializedVersion: 2
        x: 2
        y: 453
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c7176738628a4e30800000000000000
      internalID: 4488543362745833415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_196'
      rect:
        serializedVersion: 2
        x: 19
        y: 454
        width: 11
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acbe0f193b0772f70800000000000000
      internalID: 9162415883458505674
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_197'
      rect:
        serializedVersion: 2
        x: 385
        y: 453
        width: 29
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e758931c93b868fb0800000000000000
      internalID: -4645872885414918786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_198'
      rect:
        serializedVersion: 2
        x: 424
        y: 462
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8d4eab2260bc8f10800000000000000
      internalID: 2273385847601384844
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_199'
      rect:
        serializedVersion: 2
        x: 427
        y: 458
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 902c71b2ca88a0fb0800000000000000
      internalID: -4680778589659479543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_200'
      rect:
        serializedVersion: 2
        x: 451
        y: 460
        width: 10
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e26d096ceae9af60800000000000000
      internalID: 8046220510369768166
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_201'
      rect:
        serializedVersion: 2
        x: 423
        y: 453
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd67323cbfe9ea330800000000000000
      internalID: 3724088746029840092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_202'
      rect:
        serializedVersion: 2
        x: 2
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 242c32b6a0a71e1f0800000000000000
      internalID: -1017397855643254206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_203'
      rect:
        serializedVersion: 2
        x: 34
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1f9a160b8eef11f0800000000000000
      internalID: -1071875905420615909
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_204'
      rect:
        serializedVersion: 2
        x: 66
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c37f272040fce08b0800000000000000
      internalID: -5183978504928954564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_205'
      rect:
        serializedVersion: 2
        x: 98
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8ce42894129d9940800000000000000
      internalID: 5304556553261870222
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_206'
      rect:
        serializedVersion: 2
        x: 130
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff5fa327bc18464b0800000000000000
      internalID: -5448086938416646657
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_207'
      rect:
        serializedVersion: 2
        x: 162
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 829f761d27ab6d370800000000000000
      internalID: 8347063961680083240
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_208'
      rect:
        serializedVersion: 2
        x: 194
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f2498c326bccb920800000000000000
      internalID: 3007502273959510769
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_209'
      rect:
        serializedVersion: 2
        x: 226
        y: 421
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8730c31319818620800000000000000
      internalID: 2774649560465684364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_210'
      rect:
        serializedVersion: 2
        x: 258
        y: 422
        width: 28
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2306a84c570bae20800000000000000
      internalID: 3362789639699432236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_211'
      rect:
        serializedVersion: 2
        x: 290
        y: 424
        width: 28
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f9124203c4ad6ca10800000000000000
      internalID: 1929469511134880159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_212'
      rect:
        serializedVersion: 2
        x: 322
        y: 422
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3e46093262f138a0800000000000000
      internalID: -6327009497801404868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_213'
      rect:
        serializedVersion: 2
        x: 356
        y: 421
        width: 24
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37125f937bd009ae0800000000000000
      internalID: -1544719591585537677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_214'
      rect:
        serializedVersion: 2
        x: 387
        y: 422
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 786f91288c54ed520800000000000000
      internalID: 2728695151711614599
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_215'
      rect:
        serializedVersion: 2
        x: 417
        y: 421
        width: 30
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb47c5dd648230aa0800000000000000
      internalID: -6196064377505024834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_216'
      rect:
        serializedVersion: 2
        x: 450
        y: 420
        width: 28
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f5c41b6c611bdd40800000000000000
      internalID: 5610096918170748405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_217'
      rect:
        serializedVersion: 2
        x: 483
        y: 422
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8599d8405a938050800000000000000
      internalID: 5801650414593611148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_218'
      rect:
        serializedVersion: 2
        x: 3
        y: 389
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89866b7eef089cb50800000000000000
      internalID: 6613959360067823768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_219'
      rect:
        serializedVersion: 2
        x: 35
        y: 390
        width: 25
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6da05071194ef6ce0800000000000000
      internalID: -1409656846536078634
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_220'
      rect:
        serializedVersion: 2
        x: 71
        y: 389
        width: 18
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0097281e1a27b85c0800000000000000
      internalID: -4212146986876241664
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_221'
      rect:
        serializedVersion: 2
        x: 99
        y: 389
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3091dc68ccc385c0800000000000000
      internalID: -4214299665097453505
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_222'
      rect:
        serializedVersion: 2
        x: 132
        y: 390
        width: 24
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a28c12fee2328e800800000000000000
      internalID: 641801631387732010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_223'
      rect:
        serializedVersion: 2
        x: 165
        y: 389
        width: 23
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c5a02f7b173c6d10800000000000000
      internalID: 2120130115821151688
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_224'
      rect:
        serializedVersion: 2
        x: 194
        y: 389
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d69e5f6fff55bc020800000000000000
      internalID: 2363076987334289773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_225'
      rect:
        serializedVersion: 2
        x: 227
        y: 390
        width: 27
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2531dd0316083f60800000000000000
      internalID: 8014162215811757358
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_226'
      rect:
        serializedVersion: 2
        x: 259
        y: 389
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d19f49398785c9770800000000000000
      internalID: 8618861061775620381
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_227'
      rect:
        serializedVersion: 2
        x: 291
        y: 389
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0b1f9ac97d228440800000000000000
      internalID: 4936558142665202443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_228'
      rect:
        serializedVersion: 2
        x: 322
        y: 389
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 72f433411ddf5a9e0800000000000000
      internalID: -1610602217286185177
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_229'
      rect:
        serializedVersion: 2
        x: 357
        y: 388
        width: 24
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbfdcfe4ee86cbea0800000000000000
      internalID: -5855690042751787076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_230'
      rect:
        serializedVersion: 2
        x: 386
        y: 389
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86ce3532730c90ee0800000000000000
      internalID: -1294292074843870104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_231'
      rect:
        serializedVersion: 2
        x: 418
        y: 388
        width: 29
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77e167ae96f4380e0800000000000000
      internalID: -2268882470940828041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_232'
      rect:
        serializedVersion: 2
        x: 451
        y: 389
        width: 27
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c272056f130bb4e0800000000000000
      internalID: -1964973379043560768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_233'
      rect:
        serializedVersion: 2
        x: 484
        y: 388
        width: 24
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 579aca1065100eb20800000000000000
      internalID: 3161528407320996213
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_234'
      rect:
        serializedVersion: 2
        x: 2
        y: 357
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b164e3ff8f146f750800000000000000
      internalID: 6338326063260517915
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_235'
      rect:
        serializedVersion: 2
        x: 33
        y: 358
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1be7702a4455ce940800000000000000
      internalID: 5326726212536991409
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_236'
      rect:
        serializedVersion: 2
        x: 65
        y: 361
        width: 29
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e2818435ee9c2290800000000000000
      internalID: -7913775737931201815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_237'
      rect:
        serializedVersion: 2
        x: 97
        y: 359
        width: 28
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63cec3d86007afe40800000000000000
      internalID: 5690984252577737782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_238'
      rect:
        serializedVersion: 2
        x: 131
        y: 358
        width: 25
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15f4862111ca5c500800000000000000
      internalID: 415927729924886353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_239'
      rect:
        serializedVersion: 2
        x: 161
        y: 357
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 969617d7145b5e5e0800000000000000
      internalID: -1880897976475227799
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_240'
      rect:
        serializedVersion: 2
        x: 196
        y: 356
        width: 22
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c49d7bfb5ee4b5060800000000000000
      internalID: 6943229999192725836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_241'
      rect:
        serializedVersion: 2
        x: 225
        y: 356
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38a809eeea47fc790800000000000000
      internalID: -7507653759127745917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_242'
      rect:
        serializedVersion: 2
        x: 258
        y: 356
        width: 26
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c905017d8aad5fae0800000000000000
      internalID: -1516065280842772324
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_243'
      rect:
        serializedVersion: 2
        x: 293
        y: 357
        width: 19
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5118def7567d60650800000000000000
      internalID: 6198878768060006677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_244'
      rect:
        serializedVersion: 2
        x: 321
        y: 357
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84708a73a423b7770800000000000000
      internalID: 8609530406991562568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_245'
      rect:
        serializedVersion: 2
        x: 353
        y: 357
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09ab113849a04ec70800000000000000
      internalID: 8999329588363508368
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_246'
      rect:
        serializedVersion: 2
        x: 384
        y: 356
        width: 29
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f70b785cc89d72d10800000000000000
      internalID: 2100886949824278655
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_247'
      rect:
        serializedVersion: 2
        x: 417
        y: 356
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2b755eeb7a0ea410800000000000000
      internalID: 1490140054102047531
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_248'
      rect:
        serializedVersion: 2
        x: 451
        y: 359
        width: 26
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d0cae85da3d7fdc0800000000000000
      internalID: -3605180235212209959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_249'
      rect:
        serializedVersion: 2
        x: 4
        y: 325
        width: 26
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56cd4e2b680e28960800000000000000
      internalID: 7602885990086401125
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_250'
      rect:
        serializedVersion: 2
        x: 34
        y: 325
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbaef7a7714daae30800000000000000
      internalID: 4515654773696883388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_251'
      rect:
        serializedVersion: 2
        x: 69
        y: 325
        width: 23
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 081e843a15c471d10800000000000000
      internalID: 2096228065079779712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_252'
      rect:
        serializedVersion: 2
        x: 98
        y: 325
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ead017ce95c7e5020800000000000000
      internalID: 2332438382685261230
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_253'
      rect:
        serializedVersion: 2
        x: 130
        y: 325
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96715c1339ecc0950800000000000000
      internalID: 6416730700686628713
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_254'
      rect:
        serializedVersion: 2
        x: 163
        y: 325
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2522eaeefcb8f1820800000000000000
      internalID: 2891183210974028370
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_255'
      rect:
        serializedVersion: 2
        x: 194
        y: 326
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e31e8fedcaa65530800000000000000
      internalID: 3843447133471380455
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_256'
      rect:
        serializedVersion: 2
        x: 226
        y: 326
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6531dd54d49505260800000000000000
      internalID: 7084260402273260374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_257'
      rect:
        serializedVersion: 2
        x: 259
        y: 325
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ea4a4ac96fa12bd0800000000000000
      internalID: -2656649436271588639
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_258'
      rect:
        serializedVersion: 2
        x: 290
        y: 325
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 506565780065212e0800000000000000
      internalID: -2156566711286475259
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_259'
      rect:
        serializedVersion: 2
        x: 323
        y: 325
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a7a988593f866bd0800000000000000
      internalID: -2637263055317522524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_260'
      rect:
        serializedVersion: 2
        x: 354
        y: 328
        width: 28
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1453de5e98ddf6710800000000000000
      internalID: 1688811969625011521
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_261'
      rect:
        serializedVersion: 2
        x: 386
        y: 326
        width: 31
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e73744c999a5ef020800000000000000
      internalID: 2377437269096428414
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_262'
      rect:
        serializedVersion: 2
        x: 418
        y: 326
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c1147497cb69e9e0800000000000000
      internalID: -1591622488365461047
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_263'
      rect:
        serializedVersion: 2
        x: 450
        y: 325
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f5d0bbabc8124c80800000000000000
      internalID: -8340076296898816527
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_264'
      rect:
        serializedVersion: 2
        x: 2
        y: 293
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5fa03e294142c5bd0800000000000000
      internalID: -2640195610763654411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_265'
      rect:
        serializedVersion: 2
        x: 34
        y: 293
        width: 28
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d10fb7815e901d10800000000000000
      internalID: 2094347899734262227
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_266'
      rect:
        serializedVersion: 2
        x: 68
        y: 293
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f08590d12ae45cb90800000000000000
      internalID: -7222279969239902193
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_267'
      rect:
        serializedVersion: 2
        x: 98
        y: 296
        width: 28
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3780c727b537fe3f0800000000000000
      internalID: -869349366459266957
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_268'
      rect:
        serializedVersion: 2
        x: 130
        y: 296
        width: 28
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d04f0ba9cdd8b3b0800000000000000
      internalID: -5496399486977621808
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_269'
      rect:
        serializedVersion: 2
        x: 162
        y: 293
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7c2ad3784607c640800000000000000
      internalID: 5100052011271466107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_270'
      rect:
        serializedVersion: 2
        x: 194
        y: 293
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25361e1652d6e6fa0800000000000000
      internalID: -5805582862310612142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_271'
      rect:
        serializedVersion: 2
        x: 226
        y: 293
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6f0066682dceee90800000000000000
      internalID: -6994427597859844246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_272'
      rect:
        serializedVersion: 2
        x: 259
        y: 293
        width: 27
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a22842ec78809c90800000000000000
      internalID: -7165076937192758615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_273'
      rect:
        serializedVersion: 2
        x: 290
        y: 295
        width: 26
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 930b562cd692c43c0800000000000000
      internalID: -4374075586693976007
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_274'
      rect:
        serializedVersion: 2
        x: 322
        y: 293
        width: 27
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7050640f6d659f9b0800000000000000
      internalID: -5045906426337819385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_275'
      rect:
        serializedVersion: 2
        x: 3
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64743705a4e826270800000000000000
      internalID: 8242306717869885254
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_276'
      rect:
        serializedVersion: 2
        x: 35
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5220193e14c000c60800000000000000
      internalID: 7782233633226555941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_277'
      rect:
        serializedVersion: 2
        x: 67
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff3c24cbba90c3400800000000000000
      internalID: 305129507956900863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_278'
      rect:
        serializedVersion: 2
        x: 99
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48b4056e3e6a359b0800000000000000
      internalID: -5092543255874090108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_279'
      rect:
        serializedVersion: 2
        x: 131
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb05f049a476a3db0800000000000000
      internalID: -4811419681882681157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_280'
      rect:
        serializedVersion: 2
        x: 163
        y: 262
        width: 27
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d43983815d73b4950800000000000000
      internalID: 6434297881002808141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_281'
      rect:
        serializedVersion: 2
        x: 4
        y: 229
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 992e2a36cea49cba0800000000000000
      internalID: -6068236643760676199
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_282'
      rect:
        serializedVersion: 2
        x: 35
        y: 229
        width: 26
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f485069f0df2f7f30800000000000000
      internalID: 4575428321015257167
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_283'
      rect:
        serializedVersion: 2
        x: 68
        y: 228
        width: 23
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b474fdf9f2cba1760800000000000000
      internalID: 7429457448032749387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_284'
      rect:
        serializedVersion: 2
        x: 101
        y: 227
        width: 23
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 704c77f17a8a27440800000000000000
      internalID: 4932189977665061895
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_285'
      rect:
        serializedVersion: 2
        x: 133
        y: 227
        width: 23
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c428d11277221290800000000000000
      internalID: -7921225423712148282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_286'
      rect:
        serializedVersion: 2
        x: 165
        y: 227
        width: 23
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eeea0726cdb46bed0800000000000000
      internalID: -2398646341611770130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_287'
      rect:
        serializedVersion: 2
        x: 197
        y: 227
        width: 23
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b73ae61674f0f66d0800000000000000
      internalID: -2995158427926355077
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_288'
      rect:
        serializedVersion: 2
        x: 229
        y: 227
        width: 23
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb152398b1c256050800000000000000
      internalID: 5793084992459526587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_289'
      rect:
        serializedVersion: 2
        x: 256
        y: 227
        width: 32
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb904dee3fa989790800000000000000
      internalID: -7523092805047809605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_290'
      rect:
        serializedVersion: 2
        x: 288
        y: 229
        width: 32
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e855abd6db966630800000000000000
      internalID: 3919991872782948577
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_291'
      rect:
        serializedVersion: 2
        x: 321
        y: 228
        width: 31
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2adf222e74dc4c900800000000000000
      internalID: 703913150396956066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_292'
      rect:
        serializedVersion: 2
        x: 354
        y: 230
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba2702075f20d1c50800000000000000
      internalID: 6637464678985396907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_293'
      rect:
        serializedVersion: 2
        x: 0
        y: 195
        width: 32
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9f7f545f0547ea830800000000000000
      internalID: 4084329803132827641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_294'
      rect:
        serializedVersion: 2
        x: 34
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe80eca44be1a8280800000000000000
      internalID: -9040379542294886161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_295'
      rect:
        serializedVersion: 2
        x: 66
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 90926dbd890be5e90800000000000000
      internalID: -7034991397336372983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_296'
      rect:
        serializedVersion: 2
        x: 98
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 711a6c0c1dc349a90800000000000000
      internalID: -7308149423735398121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_297'
      rect:
        serializedVersion: 2
        x: 130
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf12e48778ec04730800000000000000
      internalID: 3981409151829811708
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_298'
      rect:
        serializedVersion: 2
        x: 162
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2bc1c40f3c10695c0800000000000000
      internalID: -4209174860669379406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_299'
      rect:
        serializedVersion: 2
        x: 194
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cdf85ac8a65b61aa0800000000000000
      internalID: -6190561168543346724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_300'
      rect:
        serializedVersion: 2
        x: 226
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb969777907ec56a0800000000000000
      internalID: -6459033737710966338
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_301'
      rect:
        serializedVersion: 2
        x: 258
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db0da66620e7a7510800000000000000
      internalID: 1547687970728497341
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_302'
      rect:
        serializedVersion: 2
        x: 290
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7902c0ff88ccf6fa0800000000000000
      internalID: -5805196505890676585
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_303'
      rect:
        serializedVersion: 2
        x: 322
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8e8765f6f2c55a10800000000000000
      internalID: 1897637183941480077
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_304'
      rect:
        serializedVersion: 2
        x: 354
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9eff05825fd3da280800000000000000
      internalID: -9030493554655297559
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_305'
      rect:
        serializedVersion: 2
        x: 386
        y: 196
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0ec6206a837886a0800000000000000
      internalID: -6446775828425028083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_306'
      rect:
        serializedVersion: 2
        x: 8
        y: 186
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88d33972b2d996150800000000000000
      internalID: 5866392798299897224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_307'
      rect:
        serializedVersion: 2
        x: 14
        y: 183
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc698b7adab57a890800000000000000
      internalID: -7446882657432463668
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_308'
      rect:
        serializedVersion: 2
        x: 22
        y: 186
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f20d8eba12f29460800000000000000
      internalID: 7247120947072533235
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_309'
      rect:
        serializedVersion: 2
        x: 34
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e08135ca8a0ed1360800000000000000
      internalID: 7142111599129729038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_310'
      rect:
        serializedVersion: 2
        x: 45
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0690e7964cc45410800000000000000
      internalID: 1465020382370108940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_311'
      rect:
        serializedVersion: 2
        x: 66
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70d4749fc13135cb0800000000000000
      internalID: -4876532956349903609
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_312'
      rect:
        serializedVersion: 2
        x: 77
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 120a5cb68aa7fd200800000000000000
      internalID: 207018971663540257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_313'
      rect:
        serializedVersion: 2
        x: 98
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ede55bf6569e2f40800000000000000
      internalID: 5705663078253063651
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_314'
      rect:
        serializedVersion: 2
        x: 109
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34b2802f77ffb7f20800000000000000
      internalID: 3421609232544836419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_315'
      rect:
        serializedVersion: 2
        x: 130
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1fb06ee07fdc93f90800000000000000
      internalID: -6973316087015928847
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_316'
      rect:
        serializedVersion: 2
        x: 141
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da9710264032c55e0800000000000000
      internalID: -1919620839435568723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_317'
      rect:
        serializedVersion: 2
        x: 162
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e9fe3209d78c26ba0800000000000000
      internalID: -6097090503891488866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_318'
      rect:
        serializedVersion: 2
        x: 173
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 826fbd1e82d6b7170800000000000000
      internalID: 8177249570776675880
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_319'
      rect:
        serializedVersion: 2
        x: 194
        y: 165
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77e00065d4e919120800000000000000
      internalID: 2418888529867181687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_320'
      rect:
        serializedVersion: 2
        x: 205
        y: 187
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 834a996bc47bdc220800000000000000
      internalID: 2507862107624154168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_321'
      rect:
        serializedVersion: 2
        x: 237
        y: 182
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: adee9994019543d10800000000000000
      internalID: 2104404852378037978
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_322'
      rect:
        serializedVersion: 2
        x: 257
        y: 164
        width: 30
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c50a41a2a60a58790800000000000000
      internalID: -7528434824246943652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_323'
      rect:
        serializedVersion: 2
        x: 301
        y: 182
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1fb291ba784035540800000000000000
      internalID: 4995341392421661681
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_324'
      rect:
        serializedVersion: 2
        x: 327
        y: 166
        width: 19
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d39de3ef34769080800000000000000
      internalID: -9181022972592417838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_325'
      rect:
        serializedVersion: 2
        x: 356
        y: 165
        width: 24
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 025ec4c03bb806350800000000000000
      internalID: 6007955504034014496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_326'
      rect:
        serializedVersion: 2
        x: 385
        y: 164
        width: 30
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6bba4c36f9dd04ac0800000000000000
      internalID: -3872852002895254602
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_327'
      rect:
        serializedVersion: 2
        x: 420
        y: 164
        width: 24
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 733980d1e33ca6ac0800000000000000
      internalID: -3861059058974289097
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_328'
      rect:
        serializedVersion: 2
        x: 1
        y: 164
        width: 24
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c273454152ccd6930800000000000000
      internalID: 4138188092250273580
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_329'
      rect:
        serializedVersion: 2
        x: 6
        y: 178
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1224b4c657c7c13f0800000000000000
      internalID: -928730579375275487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_330'
      rect:
        serializedVersion: 2
        x: 18
        y: 176
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec21610746828c2d0800000000000000
      internalID: -3258309918560218418
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_331'
      rect:
        serializedVersion: 2
        x: 225
        y: 170
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28440c167b3d99620800000000000000
      internalID: 2781487029450458242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_332'
      rect:
        serializedVersion: 2
        x: 227
        y: 179
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cad8695f95555bbf0800000000000000
      internalID: -309247154544276052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_333'
      rect:
        serializedVersion: 2
        x: 246
        y: 179
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71be36d85e91ca2b0800000000000000
      internalID: -5572050165253805289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_334'
      rect:
        serializedVersion: 2
        x: 289
        y: 170
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6bb99d02918b6d1b0800000000000000
      internalID: -5632111865915401290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_335'
      rect:
        serializedVersion: 2
        x: 291
        y: 179
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c508a3ca1506d19a0800000000000000
      internalID: -6260742003076988836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: '#1 - Transparent Icons_336'
      rect:
        serializedVersion: 2
        x: 310
        y: 179
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d2cdff7efe783ab0800000000000000
      internalID: -5028129352425028905
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable: {}
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

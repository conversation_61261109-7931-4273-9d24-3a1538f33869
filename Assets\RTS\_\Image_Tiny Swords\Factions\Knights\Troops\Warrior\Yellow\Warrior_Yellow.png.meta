fileFormatVersion: 2
guid: 8281da2eb1581f9499fc2566758fecd2
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1991435032617538378
    second: Warrior_Yellow_0
  - first:
      213: -6967288148028644749
    second: Warrior_Yellow_1
  - first:
      213: -4022200608054006397
    second: Warrior_Yellow_2
  - first:
      213: -4285312004751942264
    second: Warrior_Yellow_3
  - first:
      213: 6419622488461153640
    second: Warrior_Yellow_4
  - first:
      213: -4971819575706166738
    second: Warrior_Yellow_5
  - first:
      213: -4015984204674507470
    second: Warrior_Yellow_6
  - first:
      213: -4006559205247594933
    second: Warrior_Yellow_7
  - first:
      213: 938323130256339752
    second: Warrior_Yellow_8
  - first:
      213: 2717981475013178385
    second: Warrior_Yellow_9
  - first:
      213: -2187471619270954700
    second: Warrior_Yellow_10
  - first:
      213: -1730311214312491107
    second: Warrior_Yellow_11
  - first:
      213: -9173930890846004641
    second: Warrior_Yellow_12
  - first:
      213: 100852292313613531
    second: Warrior_Yellow_13
  - first:
      213: -1748595758175074802
    second: Warrior_Yellow_14
  - first:
      213: -5496770217167107306
    second: Warrior_Yellow_15
  - first:
      213: -8539356555806127068
    second: Warrior_Yellow_16
  - first:
      213: 7225681854716281781
    second: Warrior_Yellow_17
  - first:
      213: -7030607542886280917
    second: Warrior_Yellow_18
  - first:
      213: -4157908292927387067
    second: Warrior_Yellow_19
  - first:
      213: 2602250000151032687
    second: Warrior_Yellow_20
  - first:
      213: 3364761257953675028
    second: Warrior_Yellow_21
  - first:
      213: -8335237267181682645
    second: Warrior_Yellow_22
  - first:
      213: -5302145035101541793
    second: Warrior_Yellow_23
  - first:
      213: -8992679245070357929
    second: Warrior_Yellow_24
  - first:
      213: -7028266041091071233
    second: Warrior_Yellow_25
  - first:
      213: 2144232616574780300
    second: Warrior_Yellow_26
  - first:
      213: 6324208912828455707
    second: Warrior_Yellow_27
  - first:
      213: 4633463401767340695
    second: Warrior_Yellow_28
  - first:
      213: 4397865617136004219
    second: Warrior_Yellow_29
  - first:
      213: 8652470680015601653
    second: Warrior_Yellow_30
  - first:
      213: -7355868163081092859
    second: Warrior_Yellow_31
  - first:
      213: -5828544883868578437
    second: Warrior_Yellow_32
  - first:
      213: -5308614454042287756
    second: Warrior_Yellow_33
  - first:
      213: -618252575870951262
    second: Warrior_Yellow_34
  - first:
      213: 7051205036580748173
    second: Warrior_Yellow_35
  - first:
      213: -7310020424001536439
    second: Warrior_Yellow_36
  - first:
      213: -5760356722022724595
    second: Warrior_Yellow_37
  - first:
      213: 2295647409883671499
    second: Warrior_Yellow_38
  - first:
      213: 2598412330396123018
    second: Warrior_Yellow_39
  - first:
      213: 1495826608280141849
    second: Warrior_Yellow_40
  - first:
      213: -7765141425826519637
    second: Warrior_Yellow_41
  - first:
      213: 40630138288693660
    second: Warrior_Yellow_42
  - first:
      213: 7921104473877173894
    second: Warrior_Yellow_43
  - first:
      213: -1532923145842775265
    second: Warrior_Yellow_44
  - first:
      213: -3575772503862909307
    second: Warrior_Yellow_45
  - first:
      213: -7483494299847601693
    second: Warrior_Yellow_46
  - first:
      213: 4517469405552455284
    second: Warrior_Yellow_47
  - first:
      213: 1089325564516864928
    second: Warrior_Yellow_48
  - first:
      213: -55428727153241670
    second: Warrior_Yellow_49
  - first:
      213: 5655057821408709014
    second: Warrior_Yellow_50
  - first:
      213: -1283898058186490740
    second: Warrior_Yellow_51
  - first:
      213: -4771741185947572397
    second: Warrior_Yellow_52
  - first:
      213: 5393773712194820723
    second: Warrior_Yellow_53
  - first:
      213: 4315289977415559896
    second: Warrior_Yellow_54
  - first:
      213: 2650923518018447998
    second: Warrior_Yellow_55
  - first:
      213: 1596178596579847344
    second: Warrior_Yellow_56
  - first:
      213: 4707506918287455791
    second: Warrior_Yellow_57
  - first:
      213: -2533384868851654593
    second: Warrior_Yellow_58
  - first:
      213: -1667245859755149911
    second: Warrior_Yellow_59
  - first:
      213: 1595298084537287850
    second: Warrior_Yellow_60
  - first:
      213: -4609951452127959854
    second: Warrior_Yellow_61
  - first:
      213: 701087979730747082
    second: Warrior_Yellow_62
  - first:
      213: -135836921505113210
    second: Warrior_Yellow_63
  - first:
      213: 8088074477003572302
    second: Warrior_Yellow_64
  - first:
      213: -812807016604332760
    second: Warrior_Yellow_65
  - first:
      213: 3885479684810487885
    second: Warrior_Yellow_66
  - first:
      213: 974746929242695597
    second: Warrior_Yellow_67
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_Yellow_0
      rect:
        serializedVersion: 2
        x: 62
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a43df607c9ff2ab10800000000000000
      internalID: 1991435032617538378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_1
      rect:
        serializedVersion: 2
        x: 254
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 372a6f8f7583f4f90800000000000000
      internalID: -6967288148028644749
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_2
      rect:
        serializedVersion: 2
        x: 445
        y: 1399
        width: 82
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38da4958bd54e28c0800000000000000
      internalID: -4022200608054006397
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_3
      rect:
        serializedVersion: 2
        x: 637
        y: 1399
        width: 82
        height: 89
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88d1b0daf638784c0800000000000000
      internalID: -4285312004751942264
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_4
      rect:
        serializedVersion: 2
        x: 830
        y: 1399
        width: 80
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86516d004a4171950800000000000000
      internalID: 6419622488461153640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_5
      rect:
        serializedVersion: 2
        x: 1022
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2a53be007c800bb0800000000000000
      internalID: -4971819575706166738
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_6
      rect:
        serializedVersion: 2
        x: 70
        y: 1207
        width: 62
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23ddbc8b4ab5448c0800000000000000
      internalID: -4015984204674507470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_7
      rect:
        serializedVersion: 2
        x: 259
        y: 1207
        width: 71
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4e45a761a7d568c0800000000000000
      internalID: -4006559205247594933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_8
      rect:
        serializedVersion: 2
        x: 447
        y: 1207
        width: 80
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 823367f5ae7950d00800000000000000
      internalID: 938323130256339752
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_9
      rect:
        serializedVersion: 2
        x: 629
        y: 1207
        width: 64
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 114b04440c538b520800000000000000
      internalID: 2717981475013178385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_10
      rect:
        serializedVersion: 2
        x: 694
        y: 1232
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4313b19e62a84a1e0800000000000000
      internalID: -2187471619270954700
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_11
      rect:
        serializedVersion: 2
        x: 824
        y: 1207
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9777004e13bcf7e0800000000000000
      internalID: -1730311214312491107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_12
      rect:
        serializedVersion: 2
        x: 883
        y: 1227
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5a1429d576afa080800000000000000
      internalID: -9173930890846004641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_13
      rect:
        serializedVersion: 2
        x: 1022
        y: 1207
        width: 81
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd4032220ac466100800000000000000
      internalID: 100852292313613531
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_14
      rect:
        serializedVersion: 2
        x: 57
        y: 1015
        width: 73
        height: 110
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0648322c6dbbb7e0800000000000000
      internalID: -1748595758175074802
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_15
      rect:
        serializedVersion: 2
        x: 248
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 61bcec15c9c87b3b0800000000000000
      internalID: -5496770217167107306
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_16
      rect:
        serializedVersion: 2
        x: 296
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42cc5c8b47c1e7980800000000000000
      internalID: -8539356555806127068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_17
      rect:
        serializedVersion: 2
        x: 440
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b389521d57c64460800000000000000
      internalID: 7225681854716281781
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_18
      rect:
        serializedVersion: 2
        x: 488
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b21d41380b34e6e90800000000000000
      internalID: -7030607542886280917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_19
      rect:
        serializedVersion: 2
        x: 651
        y: 1015
        width: 53
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 54a8967ef642c46c0800000000000000
      internalID: -4157908292927387067
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_20
      rect:
        serializedVersion: 2
        x: 696
        y: 1014
        width: 64
        height: 114
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6333cb769c0d1420800000000000000
      internalID: 2602250000151032687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_21
      rect:
        serializedVersion: 2
        x: 841
        y: 1012
        width: 109
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 417435e698802be20800000000000000
      internalID: 3364761257953675028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_22
      rect:
        serializedVersion: 2
        x: 1026
        y: 1015
        width: 102
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b20c9031ed9435c80800000000000000
      internalID: -8335237267181682645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_23
      rect:
        serializedVersion: 2
        x: 33
        y: 823
        width: 84
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5aeb2bbe2ffa66b0800000000000000
      internalID: -5302145035101541793
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_24
      rect:
        serializedVersion: 2
        x: 223
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75aad823fd5933380800000000000000
      internalID: -8992679245070357929
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_25
      rect:
        serializedVersion: 2
        x: 415
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff263d1e545967e90800000000000000
      internalID: -7028266041091071233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_26
      rect:
        serializedVersion: 2
        x: 633
        y: 813
        width: 125
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c830fb24438d1cd10800000000000000
      internalID: 2144232616574780300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_27
      rect:
        serializedVersion: 2
        x: 827
        y: 823
        width: 68
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1bdf76cf7a14c750800000000000000
      internalID: 6324208912828455707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_28
      rect:
        serializedVersion: 2
        x: 896
        y: 832
        width: 55
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79a8d1e2a6e5d4040800000000000000
      internalID: 4633463401767340695
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_29
      rect:
        serializedVersion: 2
        x: 904
        y: 824
        width: 16
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7cab475a7b580d30800000000000000
      internalID: 4397865617136004219
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_30
      rect:
        serializedVersion: 2
        x: 1021
        y: 823
        width: 87
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ffffcf9d30c31870800000000000000
      internalID: 8652470680015601653
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_31
      rect:
        serializedVersion: 2
        x: 57
        y: 631
        width: 80
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50142f4f0e4bae990800000000000000
      internalID: -7355868163081092859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_32
      rect:
        serializedVersion: 2
        x: 246
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7dc79f1f49dc1fa0800000000000000
      internalID: -5828544883868578437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_33
      rect:
        serializedVersion: 2
        x: 306
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4712e41c7430456b0800000000000000
      internalID: -5308614454042287756
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_34
      rect:
        serializedVersion: 2
        x: 438
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a0edb1de868b67f0800000000000000
      internalID: -618252575870951262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_35
      rect:
        serializedVersion: 2
        x: 498
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8b37d33f99ead160800000000000000
      internalID: 7051205036580748173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_36
      rect:
        serializedVersion: 2
        x: 621
        y: 588
        width: 120
        height: 139
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 94eb98e87279d8a90800000000000000
      internalID: -7310020424001536439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_37
      rect:
        serializedVersion: 2
        x: 808
        y: 600
        width: 91
        height: 126
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0c4d4cb41a1f00b0800000000000000
      internalID: -5760356722022724595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_38
      rect:
        serializedVersion: 2
        x: 1013
        y: 627
        width: 68
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcb69ab0827cbdf10800000000000000
      internalID: 2295647409883671499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_39
      rect:
        serializedVersion: 2
        x: 35
        y: 439
        width: 82
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8f714d5f3a6f0420800000000000000
      internalID: 2598412330396123018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_40
      rect:
        serializedVersion: 2
        x: 226
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9108d10006e32c410800000000000000
      internalID: 1495826608280141849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_41
      rect:
        serializedVersion: 2
        x: 418
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba5a0f2c30dac3490800000000000000
      internalID: -7765141425826519637
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_42
      rect:
        serializedVersion: 2
        x: 614
        y: 411
        width: 148
        height: 119
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c916fdf35e8509000800000000000000
      internalID: 40630138288693660
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_43
      rect:
        serializedVersion: 2
        x: 835
        y: 414
        width: 118
        height: 117
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 686d43a1d8a6ded60800000000000000
      internalID: 7921104473877173894
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_44
      rect:
        serializedVersion: 2
        x: 1021
        y: 439
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f1b21184586f9bae0800000000000000
      internalID: -1532923145842775265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_45
      rect:
        serializedVersion: 2
        x: 1075
        y: 454
        width: 47
        height: 45
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58664ad5add406ec0800000000000000
      internalID: -3575772503862909307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_46
      rect:
        serializedVersion: 2
        x: 882
        y: 412
        width: 29
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e15ce26599452890800000000000000
      internalID: -7483494299847601693
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_47
      rect:
        serializedVersion: 2
        x: 74
        y: 247
        width: 64
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47e26a16d7641be30800000000000000
      internalID: 4517469405552455284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_48
      rect:
        serializedVersion: 2
        x: 264
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a397c554df0e1f00800000000000000
      internalID: 1089325564516864928
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_49
      rect:
        serializedVersion: 2
        x: 456
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab1dda7cdd31b3ff0800000000000000
      internalID: -55428727153241670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_50
      rect:
        serializedVersion: 2
        x: 622
        y: 248
        width: 118
        height: 109
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6994c55702dca7e40800000000000000
      internalID: 5655057821408709014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_51
      rect:
        serializedVersion: 2
        x: 837
        y: 345
        width: 47
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8cfaf9b48dae2ee0800000000000000
      internalID: -1283898058186490740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_52
      rect:
        serializedVersion: 2
        x: 851
        y: 248
        width: 86
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3576e735cae57cdb0800000000000000
      internalID: -4771741185947572397
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_53
      rect:
        serializedVersion: 2
        x: 1035
        y: 247
        width: 81
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37a56322d988ada40800000000000000
      internalID: 5393773712194820723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_54
      rect:
        serializedVersion: 2
        x: 603
        y: 290
        width: 10
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8da68aefe5df2eb30800000000000000
      internalID: 4315289977415559896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_55
      rect:
        serializedVersion: 2
        x: 613
        y: 307
        width: 17
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7ae73af4e8f9c420800000000000000
      internalID: 2650923518018447998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_56
      rect:
        serializedVersion: 2
        x: 18
        y: 270
        width: 59
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bc29370af3c62610800000000000000
      internalID: 1596178596579847344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_57
      rect:
        serializedVersion: 2
        x: 212
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f26b73ac99c645140800000000000000
      internalID: 4707506918287455791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_58
      rect:
        serializedVersion: 2
        x: 404
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f306fbc52eb97dcd0800000000000000
      internalID: -2533384868851654593
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_59
      rect:
        serializedVersion: 2
        x: 75
        y: 55
        width: 99
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a5bd0859b0ccd8e0800000000000000
      internalID: -1667245859755149911
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_60
      rect:
        serializedVersion: 2
        x: 267
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa4c4b7d723a32610800000000000000
      internalID: 1595298084537287850
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_61
      rect:
        serializedVersion: 2
        x: 459
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d4ed1034992600c0800000000000000
      internalID: -4609951452127959854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_62
      rect:
        serializedVersion: 2
        x: 584
        y: 56
        width: 148
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aceb5249dc3cab900800000000000000
      internalID: 701087979730747082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_63
      rect:
        serializedVersion: 2
        x: 724
        y: 92
        width: 22
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68b1e422a096d1ef0800000000000000
      internalID: -135836921505113210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_64
      rect:
        serializedVersion: 2
        x: 772
        y: 94
        width: 72
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4483b662ec9e3070800000000000000
      internalID: 8088074477003572302
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_65
      rect:
        serializedVersion: 2
        x: 826
        y: 56
        width: 74
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82d8a58235458b4f0800000000000000
      internalID: -812807016604332760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_66
      rect:
        serializedVersion: 2
        x: 1000
        y: 86
        width: 31
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d48302bef2ffbe530800000000000000
      internalID: 3885479684810487885
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Yellow_67
      rect:
        serializedVersion: 2
        x: 1032
        y: 55
        width: 62
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: da7e96fea2ff68d00800000000000000
      internalID: 974746929242695597
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Warrior_Yellow_0: 1991435032617538378
      Warrior_Yellow_1: -6967288148028644749
      Warrior_Yellow_10: -2187471619270954700
      Warrior_Yellow_11: -1730311214312491107
      Warrior_Yellow_12: -9173930890846004641
      Warrior_Yellow_13: 100852292313613531
      Warrior_Yellow_14: -1748595758175074802
      Warrior_Yellow_15: -5496770217167107306
      Warrior_Yellow_16: -8539356555806127068
      Warrior_Yellow_17: 7225681854716281781
      Warrior_Yellow_18: -7030607542886280917
      Warrior_Yellow_19: -4157908292927387067
      Warrior_Yellow_2: -4022200608054006397
      Warrior_Yellow_20: 2602250000151032687
      Warrior_Yellow_21: 3364761257953675028
      Warrior_Yellow_22: -8335237267181682645
      Warrior_Yellow_23: -5302145035101541793
      Warrior_Yellow_24: -8992679245070357929
      Warrior_Yellow_25: -7028266041091071233
      Warrior_Yellow_26: 2144232616574780300
      Warrior_Yellow_27: 6324208912828455707
      Warrior_Yellow_28: 4633463401767340695
      Warrior_Yellow_29: 4397865617136004219
      Warrior_Yellow_3: -4285312004751942264
      Warrior_Yellow_30: 8652470680015601653
      Warrior_Yellow_31: -7355868163081092859
      Warrior_Yellow_32: -5828544883868578437
      Warrior_Yellow_33: -5308614454042287756
      Warrior_Yellow_34: -618252575870951262
      Warrior_Yellow_35: 7051205036580748173
      Warrior_Yellow_36: -7310020424001536439
      Warrior_Yellow_37: -5760356722022724595
      Warrior_Yellow_38: 2295647409883671499
      Warrior_Yellow_39: 2598412330396123018
      Warrior_Yellow_4: 6419622488461153640
      Warrior_Yellow_40: 1495826608280141849
      Warrior_Yellow_41: -7765141425826519637
      Warrior_Yellow_42: 40630138288693660
      Warrior_Yellow_43: 7921104473877173894
      Warrior_Yellow_44: -1532923145842775265
      Warrior_Yellow_45: -3575772503862909307
      Warrior_Yellow_46: -7483494299847601693
      Warrior_Yellow_47: 4517469405552455284
      Warrior_Yellow_48: 1089325564516864928
      Warrior_Yellow_49: -55428727153241670
      Warrior_Yellow_5: -4971819575706166738
      Warrior_Yellow_50: 5655057821408709014
      Warrior_Yellow_51: -1283898058186490740
      Warrior_Yellow_52: -4771741185947572397
      Warrior_Yellow_53: 5393773712194820723
      Warrior_Yellow_54: 4315289977415559896
      Warrior_Yellow_55: 2650923518018447998
      Warrior_Yellow_56: 1596178596579847344
      Warrior_Yellow_57: 4707506918287455791
      Warrior_Yellow_58: -2533384868851654593
      Warrior_Yellow_59: -1667245859755149911
      Warrior_Yellow_6: -4015984204674507470
      Warrior_Yellow_60: 1595298084537287850
      Warrior_Yellow_61: -4609951452127959854
      Warrior_Yellow_62: 701087979730747082
      Warrior_Yellow_63: -135836921505113210
      Warrior_Yellow_64: 8088074477003572302
      Warrior_Yellow_65: -812807016604332760
      Warrior_Yellow_66: 3885479684810487885
      Warrior_Yellow_67: 974746929242695597
      Warrior_Yellow_7: -4006559205247594933
      Warrior_Yellow_8: 938323130256339752
      Warrior_Yellow_9: 2717981475013178385
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: 1eec4daae096edc449c02c6d3cb5ff42
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 420
      width: 63
      height: 75
    spriteID: cec7c273c941bfe4ebfb5e0114312727
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.45161292, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 752
      width: 62
      height: 75
    spriteID: 219b347b66d2e8e42a8f38707ec7ac35
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 752}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 503
      width: 63
      height: 75
    spriteID: e181567297313be4eb13d19788d5f311
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 503}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.44444448, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 586
      width: 63
      height: 75
    spriteID: f3020f2c131b6534780b4c9a721dfecf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 586}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.44776118, y: -0.7837838}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 568
      width: 67
      height: 74
    spriteID: 1bc1e368ae9bb87478f90d0abfe67c95
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 568}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.46153846, y: -0.81690145}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 835
      width: 65
      height: 71
    spriteID: b9f2de2d375331a46b0d25eb38195f66
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 835}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.46774188, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 914
      width: 62
      height: 73
    spriteID: 31c0eb84540ec614ebf6abf70e6a4d47
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 914}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.44776118, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 483
      width: 67
      height: 77
    spriteID: 2469f794256bc7446b64cf9cbd840c37
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 483}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.44615382, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 733
      width: 65
      height: 76
    spriteID: 5a50915ed41577d41a7109e3d84347f4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 733}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 126
      width: 62
      height: 70
    spriteID: d00449285c313f44593bcb90f5e71f0b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 126}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.4861111, y: -0.7532468}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 510
      width: 72
      height: 77
    spriteID: 707eacbabd4c84b41a0f421d93321f5e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 510}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47826087, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 4
      width: 69
      height: 76
    spriteID: 88345c6397c0c484593a98557ed985e1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.45161292, y: -0.8285715}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 62
      height: 70
    spriteID: fee1cdc64464aa6498ee9cebcc77845e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.44615382, y: -0.725}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 293
      width: 65
      height: 80
    spriteID: 9e8e813f2d7813c4d854f68b4a9f22cb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 293}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.38666663, y: -0.70731705}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 644
      width: 75
      height: 82
    spriteID: 1ed8df9f4c816ee44ae380bb9fc3ca5c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 644}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.46774188, y: -0.50877196}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 62
      height: 114
    spriteID: c78a5a9b3079c0348ad0824ac541a4ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.530303, y: -0.59183675}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 538
      width: 66
      height: 98
    spriteID: feea89d5482ba3b4da17d31abc4e2f15
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 538}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 734
      width: 66
      height: 92
    spriteID: 43474c17cbbcd8543a6cf8cc9e35c4ba
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 734}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.530303, y: -0.6304348}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 834
      width: 66
      height: 92
    spriteID: 323aecd9ffda3914cb0ddffdfbfbb840
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 834}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.53623194, y: -0.6170213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 436
      width: 69
      height: 94
    spriteID: 44a3a521ed7a582489476b5548cc5b18
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 436}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.46774188, y: -0.65909094}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 595
      width: 62
      height: 88
    spriteID: f05855e6b8944704c9500ad84af333b0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 595}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 88
      width: 64
      height: 75
    spriteID: fb6601e51fa052e429739d551ba40e81
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 88}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 282
      width: 55
      height: 75
    spriteID: 2a01d9091a3a99f4c9802e86d277d149
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 282}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 4
      width: 92
      height: 75
    spriteID: 6289b4d39fda2e642a02e4ca1d29025b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 4}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.51388896, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 853
      width: 72
      height: 73
    spriteID: 839d47706513e054ca413d677ab5b038
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 853}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 344
      width: 74
      height: 75
    spriteID: f972c1b36ad932e459be4deb9ba2d6eb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 344}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 427
      width: 74
      height: 75
    spriteID: d9bb16df11f3d6a40a27271ee75f7f03
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 427}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.51282054, y: -0.7631579}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 934
      width: 78
      height: 76
    spriteID: bb89582dc708aa841aa7e6ff9d6b795e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 934}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.469697, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 650
      width: 66
      height: 75
    spriteID: 9aea0c64fc4b808418e9459d5396e4d1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 650}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 171
      width: 64
      height: 75
    spriteID: 963046aa78210684eb2ccefa76ef7bf3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 171}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 365
      width: 55
      height: 75
    spriteID: c1adeb6d725e232438ff365c82eb4eff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 365}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 87
      width: 92
      height: 75
    spriteID: ef1681747197ff747ac83d503a83d387
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 87}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.46835443, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 85
      width: 79
      height: 73
    spriteID: ba44267cf6da1b144a6013e8cfe3af76
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 85}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 691
      width: 73
      height: 73
    spriteID: 5a04f9b9cc1d61346b49b0c53d9b6593
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 691}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.50684935, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 772
      width: 73
      height: 73
    spriteID: c48f1369c2a0f504fb91e84f2c52f899
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 772}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.5625, y: -0.79452056}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 4
      width: 80
      height: 73
    spriteID: 9bf7c7fadfa12614284bf47ee90c1c75
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 4}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49206352, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 669
      width: 63
      height: 75
    spriteID: 261435434e68eba4b944815469c2efe0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 669}
  - name: Frame_37
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 254
      width: 64
      height: 75
    spriteID: 9b3b74fa1aec77e44ad7c630ea90aa39
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 254}
  - name: Frame_38
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 448
      width: 55
      height: 75
    spriteID: 8d881ad9f58c190448db6e2e9ecb1772
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 448}
  - name: Frame_39
    originalName: 
    pivot: {x: 0.33695653, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 170
      width: 92
      height: 75
    spriteID: 4a1425efb8f38264f8e54a21292c3b82
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 170}
  - name: Frame_40
    originalName: 
    pivot: {x: 0.52459013, y: -0.5930233}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 934
      width: 61
      height: 86
    spriteID: e7714329f923d2a4db500908b6acd4c4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 934}
  - name: Frame_41
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 4
      width: 67
      height: 88
    spriteID: 9d8235fcc315bd5439d6b16de2046a86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 4}
  - name: Frame_42
    originalName: 
    pivot: {x: 0.53731346, y: -0.55681825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 100
      width: 67
      height: 88
    spriteID: bf24d75b13e90f0419bcd131e3ef8d4e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 100}
  - name: Frame_43
    originalName: 
    pivot: {x: 0.5675675, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 253
      width: 74
      height: 89
    spriteID: 374b5cb8aa7e11744946598aedb269dd
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 253}
  - name: Frame_44
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 166
      width: 69
      height: 81
    spriteID: 992368c1400a5884d84f61df7206235b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 166}
  - name: Frame_45
    originalName: 
    pivot: {x: 0.625, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 337
      width: 64
      height: 75
    spriteID: c039693483a5a0c44b1c7db80bb53a07
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 337}
  - name: Frame_46
    originalName: 
    pivot: {x: 0.5636364, y: -0.7733334}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 531
      width: 55
      height: 75
    spriteID: 8714e80558e06854cae36c80b4ea1004
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 531}
  - name: Frame_47
    originalName: 
    pivot: {x: 0.36904767, y: -0.70512825}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 350
      width: 84
      height: 78
    spriteID: 22917d19514eef84693f6a745b78e229
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 350}
  - name: Frame_48
    originalName: 
    pivot: {x: 0.58181816, y: -0.45744678}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 381
      width: 55
      height: 94
    spriteID: a40f3e4272476f04688cc55ad0562fe0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 381}
  - name: Frame_49
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 817
      width: 55
      height: 89
    spriteID: d513d2688b6214643877a8cad25915f1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 817}
  - name: Frame_50
    originalName: 
    pivot: {x: 0.58181816, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 914
      width: 55
      height: 89
    spriteID: 069dc7cce65981241879c9f343d7226d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 914}
  - name: Frame_51
    originalName: 
    pivot: {x: 0.5932203, y: -0.53932583}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 174
      y: 196
      width: 59
      height: 89
    spriteID: 16c4485bb7a3eb448b9851e495f454fe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 174, y: 196}
  - name: Frame_52
    originalName: 
    pivot: {x: 0.53623194, y: -0.64197534}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 249
      y: 255
      width: 69
      height: 81
    spriteID: ee43baa670b3e29409a0fbbfccd10dae
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 249, y: 255}
  - name: Frame_53
    originalName: 
    pivot: {x: 0.49206352, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 337
      y: 995
      width: 63
      height: 14
    spriteID: 470f5513fcc77c54f855ba406affef6f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 337, y: 995}
  - name: Frame_54
    originalName: 
    pivot: {x: 0.6326531, y: -6.142857}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 614
      width: 49
      height: 14
    spriteID: 2c0226cce75467a40a8050ca67ff8b25
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 614}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 189937119
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Archer_Red
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: cec7c273c941bfe4ebfb5e0114312727
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 75
      spriteId: 219b347b66d2e8e42a8f38707ec7ac35
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: e181567297313be4eb13d19788d5f311
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 63
        height: 75
      spriteId: f3020f2c131b6534780b4c9a721dfecf
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 74
      spriteId: 1bc1e368ae9bb87478f90d0abfe67c95
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 65
        height: 71
      spriteId: b9f2de2d375331a46b0d25eb38195f66
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 73
      spriteId: 31c0eb84540ec614ebf6abf70e6a4d47
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 58
        width: 67
        height: 77
      spriteId: 2469f794256bc7446b64cf9cbd840c37
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 76
      spriteId: 5a50915ed41577d41a7109e3d84347f4
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: d00449285c313f44593bcb90f5e71f0b
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 72
        height: 77
      spriteId: 707eacbabd4c84b41a0f421d93321f5e
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 58
        width: 69
        height: 76
      spriteId: 88345c6397c0c484593a98557ed985e1
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 58
        width: 62
        height: 70
      spriteId: fee1cdc64464aa6498ee9cebcc77845e
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 65
        height: 80
      spriteId: 9e8e813f2d7813c4d854f68b4a9f22cb
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 75
        height: 82
      spriteId: 1ed8df9f4c816ee44ae380bb9fc3ca5c
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 114
      spriteId: c78a5a9b3079c0348ad0824ac541a4ca
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 98
      spriteId: feea89d5482ba3b4da17d31abc4e2f15
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: 43474c17cbbcd8543a6cf8cc9e35c4ba
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 58
        width: 66
        height: 92
      spriteId: 323aecd9ffda3914cb0ddffdfbfbb840
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 69
        height: 94
      spriteId: 44a3a521ed7a582489476b5548cc5b18
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 58
        width: 62
        height: 88
      spriteId: f05855e6b8944704c9500ad84af333b0
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: fb6601e51fa052e429739d551ba40e81
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 2a01d9091a3a99f4c9802e86d277d149
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 6289b4d39fda2e642a02e4ca1d29025b
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 72
        height: 73
      spriteId: 839d47706513e054ca413d677ab5b038
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: f972c1b36ad932e459be4deb9ba2d6eb
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 74
        height: 75
      spriteId: d9bb16df11f3d6a40a27271ee75f7f03
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 78
        height: 76
      spriteId: bb89582dc708aa841aa7e6ff9d6b795e
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 66
        height: 75
      spriteId: 9aea0c64fc4b808418e9459d5396e4d1
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 963046aa78210684eb2ccefa76ef7bf3
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: c1adeb6d725e232438ff365c82eb4eff
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: ef1681747197ff747ac83d503a83d387
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 79
        height: 73
      spriteId: ba44267cf6da1b144a6013e8cfe3af76
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: 5a04f9b9cc1d61346b49b0c53d9b6593
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 58
        width: 73
        height: 73
      spriteId: c48f1369c2a0f504fb91e84f2c52f899
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 58
        width: 80
        height: 73
      spriteId: 9bf7c7fadfa12614284bf47ee90c1c75
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 63
        height: 75
      spriteId: 261435434e68eba4b944815469c2efe0
    - name: Frame_37
      frameIndex: 37
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: 9b3b74fa1aec77e44ad7c630ea90aa39
    - name: Frame_38
      frameIndex: 38
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 8d881ad9f58c190448db6e2e9ecb1772
    - name: Frame_39
      frameIndex: 39
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 92
        height: 75
      spriteId: 4a1425efb8f38264f8e54a21292c3b82
    - name: Frame_40
      frameIndex: 40
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 51
        width: 61
        height: 86
      spriteId: e7714329f923d2a4db500908b6acd4c4
    - name: Frame_41
      frameIndex: 41
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: 9d8235fcc315bd5439d6b16de2046a86
    - name: Frame_42
      frameIndex: 42
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 49
        width: 67
        height: 88
      spriteId: bf24d75b13e90f0419bcd131e3ef8d4e
    - name: Frame_43
      frameIndex: 43
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 48
        width: 74
        height: 89
      spriteId: 374b5cb8aa7e11744946598aedb269dd
    - name: Frame_44
      frameIndex: 44
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: 992368c1400a5884d84f61df7206235b
    - name: Frame_45
      frameIndex: 45
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 58
        width: 64
        height: 75
      spriteId: c039693483a5a0c44b1c7db80bb53a07
    - name: Frame_46
      frameIndex: 46
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 58
        width: 55
        height: 75
      spriteId: 8714e80558e06854cae36c80b4ea1004
    - name: Frame_47
      frameIndex: 47
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 55
        width: 84
        height: 78
      spriteId: 22917d19514eef84693f6a745b78e229
    - name: Frame_48
      frameIndex: 48
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 43
        width: 55
        height: 94
      spriteId: a40f3e4272476f04688cc55ad0562fe0
    - name: Frame_49
      frameIndex: 49
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: d513d2688b6214643877a8cad25915f1
    - name: Frame_50
      frameIndex: 50
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 48
        width: 55
        height: 89
      spriteId: 069dc7cce65981241879c9f343d7226d
    - name: Frame_51
      frameIndex: 51
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 48
        width: 59
        height: 89
      spriteId: 16c4485bb7a3eb448b9851e495f454fe
    - name: Frame_52
      frameIndex: 52
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 52
        width: 69
        height: 81
      spriteId: ee43baa670b3e29409a0fbbfccd10dae
    - name: Frame_53
      frameIndex: 53
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 63
        height: 14
      spriteId: 470f5513fcc77c54f855ba406affef6f
    - name: Frame_54
      frameIndex: 54
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 86
        width: 49
        height: 14
      spriteId: 2c0226cce75467a40a8050ca67ff8b25
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

fileFormatVersion: 2
guid: f0af9a1507623104597d2842cdccb9d6
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5027220119692830953
    second: HappySheep_Bouncing_0
  - first:
      213: 6860843237176026257
    second: HappySheep_Bouncing_1
  - first:
      213: -4737963809079716724
    second: HappySheep_Bouncing_2
  - first:
      213: 7636839774502669027
    second: HappySheep_Bouncing_3
  - first:
      213: 5661645519548905162
    second: HappySheep_Bouncing_4
  - first:
      213: 2725343537496752768
    second: HappySheep_Bouncing_5
  - first:
      213: 6056365299796131321
    second: HappySheep_Bouncing_6
  - first:
      213: 5155434032202430276
    second: HappySheep_Bouncing_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: HappySheep_Bouncing_0
      rect:
        serializedVersion: 2
        x: 42
        y: 41
        width: 45
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 717130ebfe9bb3ab0800000000000000
      internalID: -5027220119692830953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_1
      rect:
        serializedVersion: 2
        x: 172
        y: 41
        width: 40
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 194a48be29c963f50800000000000000
      internalID: 6860843237176026257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_2
      rect:
        serializedVersion: 2
        x: 298
        y: 58
        width: 47
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c849529b40f5f3eb0800000000000000
      internalID: -4737963809079716724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_3
      rect:
        serializedVersion: 2
        x: 426
        y: 59
        width: 47
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ea4ecd20518bf960800000000000000
      internalID: 7636839774502669027
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_4
      rect:
        serializedVersion: 2
        x: 554
        y: 41
        width: 47
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac6f4e47a94329e40800000000000000
      internalID: 5661645519548905162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_5
      rect:
        serializedVersion: 2
        x: 677
        y: 41
        width: 56
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0828663028d52d520800000000000000
      internalID: 2725343537496752768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_6
      rect:
        serializedVersion: 2
        x: 305
        y: 41
        width: 28
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fdb954f6288c0450800000000000000
      internalID: 6056365299796131321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: HappySheep_Bouncing_7
      rect:
        serializedVersion: 2
        x: 434
        y: 41
        width: 26
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 447b26800f7cb8740800000000000000
      internalID: 5155434032202430276
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      HappySheep_Bouncing_0: -5027220119692830953
      HappySheep_Bouncing_1: 6860843237176026257
      HappySheep_Bouncing_2: -4737963809079716724
      HappySheep_Bouncing_3: 7636839774502669027
      HappySheep_Bouncing_4: 5661645519548905162
      HappySheep_Bouncing_5: 2725343537496752768
      HappySheep_Bouncing_6: 6056365299796131321
      HappySheep_Bouncing_7: 5155434032202430276
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

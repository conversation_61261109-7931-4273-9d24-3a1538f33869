fileFormatVersion: 2
guid: 7e6dadba3e46c3b478cbde2b03d572ab
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1625884585012484814
    second: Rocks_04_0
  - first:
      213: 3303464186290649300
    second: Rocks_04_1
  - first:
      213: -7097198202087862344
    second: Rocks_04_2
  - first:
      213: 8185347640094293832
    second: Rocks_04_3
  - first:
      213: -8690840347793911461
    second: Rocks_04_4
  - first:
      213: 6808463656719756590
    second: Rocks_04_5
  - first:
      213: 393194626552862583
    second: Rocks_04_6
  - first:
      213: 7062963900064476109
    second: Rocks_04_7
  - first:
      213: -6309004450759894304
    second: Rocks_04_8
  - first:
      213: 4544877613300213028
    second: Rocks_04_9
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Rocks_04_0
      rect:
        serializedVersion: 2
        x: 23
        y: 31
        width: 85
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2352c809592bf69e0800000000000000
      internalID: -1625884585012484814
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_1
      rect:
        serializedVersion: 2
        x: 150
        y: 33
        width: 88
        height: 62
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d83418cc2348dd20800000000000000
      internalID: 3303464186290649300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_2
      rect:
        serializedVersion: 2
        x: 278
        y: 33
        width: 88
        height: 62
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bf030876dfa18d90800000000000000
      internalID: -7097198202087862344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_3
      rect:
        serializedVersion: 2
        x: 403
        y: 30
        width: 92
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84ff6e29f42389170800000000000000
      internalID: 8185347640094293832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_4
      rect:
        serializedVersion: 2
        x: 531
        y: 28
        width: 93
        height: 67
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b519c1eefbee36780800000000000000
      internalID: -8690840347793911461
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_6
      rect:
        serializedVersion: 2
        x: 659
        y: 29
        width: 93
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 777964bfd68e47500800000000000000
      internalID: 393194626552862583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_7
      rect:
        serializedVersion: 2
        x: 787
        y: 29
        width: 93
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcb20c4fe30b40260800000000000000
      internalID: 7062963900064476109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Rocks_04_8
      rect:
        serializedVersion: 2
        x: 915
        y: 30
        width: 92
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ea191ae0e9e178a0800000000000000
      internalID: -6309004450759894304
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: e691252359e246945b9483fcc517a3c0
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Rocks_04_0: -1625884585012484814
      Rocks_04_1: 3303464186290649300
      Rocks_04_2: -7097198202087862344
      Rocks_04_3: 8185347640094293832
      Rocks_04_4: -8690840347793911461
      Rocks_04_5: 6808463656719756590
      Rocks_04_6: 393194626552862583
      Rocks_04_7: 7062963900064476109
      Rocks_04_8: -6309004450759894304
      Rocks_04_9: 4544877613300213028
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: a34ef8297e3c00f4e934999442b5ee3d
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6045454254259307398
    second: <PERSON><PERSON>_<PERSON>_0
  - first:
      213: -2423285488819419956
    second: <PERSON><PERSON>_<PERSON>_1
  - first:
      213: -5010189232691514005
    second: <PERSON><PERSON>_<PERSON>_2
  - first:
      213: -5390116343607681507
    second: <PERSON><PERSON>_<PERSON>_3
  - first:
      213: -7627501968969996973
    second: <PERSON><PERSON>_<PERSON>_4
  - first:
      213: 3007210723233672673
    second: <PERSON><PERSON>_<PERSON>_5
  - first:
      213: -2718406118890799503
    second: <PERSON><PERSON>_<PERSON>_6
  - first:
      213: 1908858404467654192
    second: <PERSON><PERSON>_Blue_7
  - first:
      213: 1519025819761848976
    second: <PERSON><PERSON>_<PERSON>_8
  - first:
      213: -4532171271299795306
    second: <PERSON><PERSON>_<PERSON>_9
  - first:
      213: -5857065392324598592
    second: <PERSON><PERSON>_<PERSON>_10
  - first:
      213: 2225278828217939072
    second: <PERSON><PERSON>_<PERSON>_11
  - first:
      213: -1052690004320940063
    second: Barrel_Blue_12
  - first:
      213: 871457715994819015
    second: <PERSON><PERSON>_Blue_13
  - first:
      213: 4239719258015043117
    second: Barrel_Blue_14
  - first:
      213: 7818603063236946399
    second: Barrel_Blue_15
  - first:
      213: 5120929411463067236
    second: Barrel_Blue_16
  - first:
      213: -2206129326550140029
    second: Barrel_Blue_17
  - first:
      213: -6284387523198709842
    second: Barrel_Blue_18
  - first:
      213: -5411486139309320545
    second: Barrel_Blue_19
  - first:
      213: -2982696178579544675
    second: Barrel_Blue_20
  - first:
      213: -5992876652804177011
    second: Barrel_Blue_21
  - first:
      213: 5399823191368615344
    second: Barrel_Blue_22
  - first:
      213: 949429558464918651
    second: Barrel_Blue_23
  - first:
      213: -7128455298753691592
    second: Barrel_Blue_24
  - first:
      213: -6297617479400447107
    second: Barrel_Blue_25
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Barrel_Blue_0
      rect:
        serializedVersion: 2
        x: 38
        y: 668
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68300033d94c5e350800000000000000
      internalID: 6045454254259307398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_1
      rect:
        serializedVersion: 2
        x: 34
        y: 540
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc44f81efa2ce5ed0800000000000000
      internalID: -2423285488819419956
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_2
      rect:
        serializedVersion: 2
        x: 170
        y: 536
        width: 44
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b657fef107b387ab0800000000000000
      internalID: -5010189232691514005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_3
      rect:
        serializedVersion: 2
        x: 294
        y: 536
        width: 52
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1e9c395eb57235b0800000000000000
      internalID: -5390116343607681507
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_4
      rect:
        serializedVersion: 2
        x: 420
        y: 537
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35563f71f5ba52690800000000000000
      internalID: -7627501968969996973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_5
      rect:
        serializedVersion: 2
        x: 550
        y: 537
        width: 52
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1eda61b4832cbb920800000000000000
      internalID: 3007210723233672673
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_6
      rect:
        serializedVersion: 2
        x: 678
        y: 537
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17ef340a908464ad0800000000000000
      internalID: -2718406118890799503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_7
      rect:
        serializedVersion: 2
        x: 38
        y: 409
        width: 52
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03e30d4fa90ad7a10800000000000000
      internalID: 1908858404467654192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_8
      rect:
        serializedVersion: 2
        x: 36
        y: 281
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0923d875fe9a41510800000000000000
      internalID: 1519025819761848976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_9
      rect:
        serializedVersion: 2
        x: 166
        y: 294
        width: 52
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 696f1f0cd3e7a11c0800000000000000
      internalID: -4532171271299795306
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_10
      rect:
        serializedVersion: 2
        x: 173
        y: 280
        width: 35
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c42248ce0687bea0800000000000000
      internalID: -5857065392324598592
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_11
      rect:
        serializedVersion: 2
        x: 298
        y: 280
        width: 44
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 084b022ed47c1ee10800000000000000
      internalID: 2225278828217939072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_12
      rect:
        serializedVersion: 2
        x: 418
        y: 284
        width: 60
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ef88d9b4081461f0800000000000000
      internalID: -1052690004320940063
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_13
      rect:
        serializedVersion: 2
        x: 552
        y: 284
        width: 47
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c9f4aeec2a081c00800000000000000
      internalID: 871457715994819015
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_14
      rect:
        serializedVersion: 2
        x: 678
        y: 284
        width: 52
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2e8e51c33286da30800000000000000
      internalID: 4239719258015043117
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_15
      rect:
        serializedVersion: 2
        x: 36
        y: 153
        width: 56
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd9e400bf02418c60800000000000000
      internalID: 7818603063236946399
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_16
      rect:
        serializedVersion: 2
        x: 166
        y: 152
        width: 52
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4663b1afa22311740800000000000000
      internalID: 5120929411463067236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_17
      rect:
        serializedVersion: 2
        x: 295
        y: 152
        width: 50
        height: 88
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38b386ae0114261e0800000000000000
      internalID: -2206129326550140029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_18
      rect:
        serializedVersion: 2
        x: 56
        y: 87
        width: 10
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eaf08d008de59c8a0800000000000000
      internalID: -6284387523198709842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_19
      rect:
        serializedVersion: 2
        x: 168
        y: 28
        width: 48
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f96948f070a86e4b0800000000000000
      internalID: -5411486139309320545
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_20
      rect:
        serializedVersion: 2
        x: 184
        y: 103
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d95e9dae0a55b96d0800000000000000
      internalID: -2982696178579544675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_21
      rect:
        serializedVersion: 2
        x: 294
        y: 28
        width: 52
        height: 81
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8300e69f6605dca0800000000000000
      internalID: -5992876652804177011
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_22
      rect:
        serializedVersion: 2
        x: 52
        y: 85
        width: 8
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b54173459600fa40800000000000000
      internalID: 5399823191368615344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_23
      rect:
        serializedVersion: 2
        x: 70
        y: 80
        width: 14
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b74be83372d0d2d00800000000000000
      internalID: 949429558464918651
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_24
      rect:
        serializedVersion: 2
        x: 332
        y: 96
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83009888ca3a21d90800000000000000
      internalID: -7128455298753691592
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Barrel_Blue_25
      rect:
        serializedVersion: 2
        x: 34
        y: 28
        width: 59
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d73ab09c44e5a98a0800000000000000
      internalID: -6297617479400447107
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Barrel_Blue_0: 6045454254259307398
      Barrel_Blue_1: -2423285488819419956
      Barrel_Blue_10: -5857065392324598592
      Barrel_Blue_11: 2225278828217939072
      Barrel_Blue_12: -1052690004320940063
      Barrel_Blue_13: 871457715994819015
      Barrel_Blue_14: 4239719258015043117
      Barrel_Blue_15: 7818603063236946399
      Barrel_Blue_16: 5120929411463067236
      Barrel_Blue_17: -2206129326550140029
      Barrel_Blue_18: -6284387523198709842
      Barrel_Blue_19: -5411486139309320545
      Barrel_Blue_2: -5010189232691514005
      Barrel_Blue_20: -2982696178579544675
      Barrel_Blue_21: -5992876652804177011
      Barrel_Blue_22: 5399823191368615344
      Barrel_Blue_23: 949429558464918651
      Barrel_Blue_24: -7128455298753691592
      Barrel_Blue_25: -6297617479400447107
      Barrel_Blue_3: -5390116343607681507
      Barrel_Blue_4: -7627501968969996973
      Barrel_Blue_5: 3007210723233672673
      Barrel_Blue_6: -2718406118890799503
      Barrel_Blue_7: 1908858404467654192
      Barrel_Blue_8: 1519025819761848976
      Barrel_Blue_9: -4532171271299795306
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

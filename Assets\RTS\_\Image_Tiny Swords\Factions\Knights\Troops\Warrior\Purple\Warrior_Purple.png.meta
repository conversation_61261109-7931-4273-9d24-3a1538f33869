fileFormatVersion: 2
guid: c4c5e0b4dbb805349be80cd5e2daac31
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5765384498261851376
    second: <PERSON>_Purple_0
  - first:
      213: 3510383775961326391
    second: Warrior_Purple_1
  - first:
      213: 311859896922910450
    second: Warrior_Purple_2
  - first:
      213: 3892754469516865428
    second: Warrior_Purple_3
  - first:
      213: -8283658547208696731
    second: Warrior_Purple_4
  - first:
      213: -5149530005767681786
    second: <PERSON>_Purple_5
  - first:
      213: 2677349103593817838
    second: Warrior_Purple_6
  - first:
      213: -959103928103930023
    second: Warrior_Purple_7
  - first:
      213: -5986785513652630109
    second: <PERSON>_Purple_8
  - first:
      213: 9212685180267929163
    second: <PERSON>_Purple_9
  - first:
      213: -2335009014406609423
    second: Warrior_<PERSON>_10
  - first:
      213: -7738798194672193487
    second: Warrior_<PERSON>_11
  - first:
      213: -5169136671150131587
    second: <PERSON>_Purple_12
  - first:
      213: -5025012844528068209
    second: Warrior_Purple_13
  - first:
      213: -8831741490759805623
    second: Warrior_<PERSON>_14
  - first:
      213: 9116830380409544711
    second: Warrior_Purple_15
  - first:
      213: 2825128017446276215
    second: Warrior_Purple_16
  - first:
      213: -746617433663462992
    second: Warrior_Purple_17
  - first:
      213: 8855182847257345412
    second: Warrior_Purple_18
  - first:
      213: 8116094010307894819
    second: Warrior_Purple_19
  - first:
      213: 294536308096643482
    second: Warrior_Purple_20
  - first:
      213: -45704713307245354
    second: Warrior_Purple_21
  - first:
      213: 8101903878904849644
    second: Warrior_Purple_22
  - first:
      213: 6462237613833058123
    second: Warrior_Purple_23
  - first:
      213: -195529405602304008
    second: Warrior_Purple_24
  - first:
      213: -2989879161186267339
    second: Warrior_Purple_25
  - first:
      213: -7069217776186826657
    second: Warrior_Purple_26
  - first:
      213: 2834732174769014844
    second: Warrior_Purple_27
  - first:
      213: 1683401626382301725
    second: Warrior_Purple_28
  - first:
      213: 7124328658204828671
    second: Warrior_Purple_29
  - first:
      213: -319467887583012568
    second: Warrior_Purple_30
  - first:
      213: 2938575399729292965
    second: Warrior_Purple_31
  - first:
      213: -3374141775889856228
    second: Warrior_Purple_32
  - first:
      213: -1660441683145606669
    second: Warrior_Purple_33
  - first:
      213: 8667007993062474251
    second: Warrior_Purple_34
  - first:
      213: -4712128825309896290
    second: Warrior_Purple_35
  - first:
      213: 6946090495268062392
    second: Warrior_Purple_36
  - first:
      213: 1841675742136149329
    second: Warrior_Purple_37
  - first:
      213: -3498638922822150334
    second: Warrior_Purple_38
  - first:
      213: 4912063175900776763
    second: Warrior_Purple_39
  - first:
      213: 2749007752846430510
    second: Warrior_Purple_40
  - first:
      213: 1213642391800805766
    second: Warrior_Purple_41
  - first:
      213: -4423429437060330549
    second: Warrior_Purple_42
  - first:
      213: 7349423308944173942
    second: Warrior_Purple_43
  - first:
      213: -4273638034262830096
    second: Warrior_Purple_44
  - first:
      213: -5734797811026916720
    second: Warrior_Purple_45
  - first:
      213: 4248652281018008639
    second: Warrior_Purple_46
  - first:
      213: -2195428298277968026
    second: Warrior_Purple_47
  - first:
      213: -5440254039875634991
    second: Warrior_Purple_48
  - first:
      213: -2207296028979526449
    second: Warrior_Purple_49
  - first:
      213: 6919492029604548124
    second: Warrior_Purple_50
  - first:
      213: -2502589002866089255
    second: Warrior_Purple_51
  - first:
      213: 3565693895458240543
    second: Warrior_Purple_52
  - first:
      213: 2040266418428537478
    second: Warrior_Purple_53
  - first:
      213: 285580359222023125
    second: Warrior_Purple_54
  - first:
      213: 9111697951020866203
    second: Warrior_Purple_55
  - first:
      213: 7388952016963945846
    second: Warrior_Purple_56
  - first:
      213: -1971893232475391062
    second: Warrior_Purple_57
  - first:
      213: -6157475543353396853
    second: Warrior_Purple_58
  - first:
      213: -7783483961150712612
    second: Warrior_Purple_59
  - first:
      213: 472481078870429373
    second: Warrior_Purple_60
  - first:
      213: -3064840312761271416
    second: Warrior_Purple_61
  - first:
      213: -6807397309032012378
    second: Warrior_Purple_62
  - first:
      213: 3786270736139740711
    second: Warrior_Purple_63
  - first:
      213: -6083236133305800148
    second: Warrior_Purple_64
  - first:
      213: -6764248154315212656
    second: Warrior_Purple_65
  - first:
      213: -4144038847237931337
    second: Warrior_Purple_66
  - first:
      213: -3337309794086529128
    second: Warrior_Purple_67
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_Purple_0
      rect:
        serializedVersion: 2
        x: 62
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0fcf074a7a2c20050800000000000000
      internalID: 5765384498261851376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_1
      rect:
        serializedVersion: 2
        x: 254
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7332877027367b030800000000000000
      internalID: 3510383775961326391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_2
      rect:
        serializedVersion: 2
        x: 445
        y: 1399
        width: 82
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2fea7c78ce2f35400800000000000000
      internalID: 311859896922910450
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_3
      rect:
        serializedVersion: 2
        x: 637
        y: 1399
        width: 82
        height: 89
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49fed59b097d50630800000000000000
      internalID: 3892754469516865428
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_4
      rect:
        serializedVersion: 2
        x: 830
        y: 1399
        width: 80
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56c4ff4a0788a0d80800000000000000
      internalID: -8283658547208696731
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_5
      rect:
        serializedVersion: 2
        x: 1022
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 609800e3eb13988b0800000000000000
      internalID: -5149530005767681786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_6
      rect:
        serializedVersion: 2
        x: 70
        y: 1207
        width: 62
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee68b6313dad72520800000000000000
      internalID: 2677349103593817838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_7
      rect:
        serializedVersion: 2
        x: 259
        y: 1207
        width: 71
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 957fea61f0490b2f0800000000000000
      internalID: -959103928103930023
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_8
      rect:
        serializedVersion: 2
        x: 447
        y: 1207
        width: 80
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3adc7407b4aaaeca0800000000000000
      internalID: -5986785513652630109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_9
      rect:
        serializedVersion: 2
        x: 629
        y: 1207
        width: 64
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4e106d4c580adf70800000000000000
      internalID: 9212685180267929163
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_10
      rect:
        serializedVersion: 2
        x: 694
        y: 1232
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f585fc3ca1689fd0800000000000000
      internalID: -2335009014406609423
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_11
      rect:
        serializedVersion: 2
        x: 824
        y: 1207
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 130312a3b044a9490800000000000000
      internalID: -7738798194672193487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_12
      rect:
        serializedVersion: 2
        x: 883
        y: 1227
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d76b6a8b5998348b0800000000000000
      internalID: -5169136671150131587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_13
      rect:
        serializedVersion: 2
        x: 1022
        y: 1207
        width: 81
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f89112a0171934ab0800000000000000
      internalID: -5025012844528068209
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_14
      rect:
        serializedVersion: 2
        x: 57
        y: 1015
        width: 73
        height: 110
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 945de2f18e95f6580800000000000000
      internalID: -8831741490759805623
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_15
      rect:
        serializedVersion: 2
        x: 248
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 700eccd8dec758e70800000000000000
      internalID: 9116830380409544711
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_16
      rect:
        serializedVersion: 2
        x: 296
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77c83e4b6fed43720800000000000000
      internalID: 2825128017446276215
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_17
      rect:
        serializedVersion: 2
        x: 440
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b1206b566b73a5f0800000000000000
      internalID: -746617433663462992
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_18
      rect:
        serializedVersion: 2
        x: 488
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48df7eff1ede3ea70800000000000000
      internalID: 8855182847257345412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_19
      rect:
        serializedVersion: 2
        x: 651
        y: 1015
        width: 53
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32ed310608822a070800000000000000
      internalID: 8116094010307894819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_20
      rect:
        serializedVersion: 2
        x: 696
        y: 1014
        width: 64
        height: 114
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a913e813637661400800000000000000
      internalID: 294536308096643482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_21
      rect:
        serializedVersion: 2
        x: 841
        y: 1012
        width: 109
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d4b2ce2ecf9d5ff0800000000000000
      internalID: -45704713307245354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_22
      rect:
        serializedVersion: 2
        x: 1026
        y: 1015
        width: 102
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce4ab72f6aebf6070800000000000000
      internalID: 8101903878904849644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_23
      rect:
        serializedVersion: 2
        x: 33
        y: 823
        width: 84
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b47ffa21fda7ea950800000000000000
      internalID: 6462237613833058123
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_24
      rect:
        serializedVersion: 2
        x: 223
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ff6ee3aa07594df0800000000000000
      internalID: -195529405602304008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_25
      rect:
        serializedVersion: 2
        x: 415
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 53f46247eb0d186d0800000000000000
      internalID: -2989879161186267339
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_26
      rect:
        serializedVersion: 2
        x: 633
        y: 813
        width: 125
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f50e18a03e715ed90800000000000000
      internalID: -7069217776186826657
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_27
      rect:
        serializedVersion: 2
        x: 827
        y: 823
        width: 68
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c384a76d4edf65720800000000000000
      internalID: 2834732174769014844
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_28
      rect:
        serializedVersion: 2
        x: 896
        y: 832
        width: 55
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1af4d23cd4ac5710800000000000000
      internalID: 1683401626382301725
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_29
      rect:
        serializedVersion: 2
        x: 904
        y: 824
        width: 16
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff32f671b23bed260800000000000000
      internalID: 7124328658204828671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_30
      rect:
        serializedVersion: 2
        x: 1021
        y: 823
        width: 87
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 829587b16a5019bf0800000000000000
      internalID: -319467887583012568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_31
      rect:
        serializedVersion: 2
        x: 57
        y: 631
        width: 80
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a6e09eb1cae7c820800000000000000
      internalID: 2938575399729292965
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_32
      rect:
        serializedVersion: 2
        x: 246
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c19874a8ee3ac21d0800000000000000
      internalID: -3374141775889856228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_33
      rect:
        serializedVersion: 2
        x: 306
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f9bad9261de4f8e0800000000000000
      internalID: -1660441683145606669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_34
      rect:
        serializedVersion: 2
        x: 438
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b02c5f399d5674870800000000000000
      internalID: 8667007993062474251
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_35
      rect:
        serializedVersion: 2
        x: 498
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e91668f9cc72b9eb0800000000000000
      internalID: -4712128825309896290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_36
      rect:
        serializedVersion: 2
        x: 621
        y: 588
        width: 120
        height: 139
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b48b24e088756060800000000000000
      internalID: 6946090495268062392
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_37
      rect:
        serializedVersion: 2
        x: 808
        y: 600
        width: 91
        height: 126
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 155aa717452fe8910800000000000000
      internalID: 1841675742136149329
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_38
      rect:
        serializedVersion: 2
        x: 1013
        y: 627
        width: 68
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24794dfaf66527fc0800000000000000
      internalID: -3498638922822150334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_39
      rect:
        serializedVersion: 2
        x: 35
        y: 439
        width: 82
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b39c7d9ee672b2440800000000000000
      internalID: 4912063175900776763
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_40
      rect:
        serializedVersion: 2
        x: 226
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e21d4318cff662620800000000000000
      internalID: 2749007752846430510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_41
      rect:
        serializedVersion: 2
        x: 418
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68974af3359b7d010800000000000000
      internalID: 1213642391800805766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_42
      rect:
        serializedVersion: 2
        x: 614
        y: 411
        width: 148
        height: 119
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcb43030d52dc92c0800000000000000
      internalID: -4423429437060330549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_43
      rect:
        serializedVersion: 2
        x: 835
        y: 414
        width: 118
        height: 117
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67be7918f856ef560800000000000000
      internalID: 7349423308944173942
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_44
      rect:
        serializedVersion: 2
        x: 1021
        y: 439
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f35a2db9dcf0b4c0800000000000000
      internalID: -4273638034262830096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_45
      rect:
        serializedVersion: 2
        x: 1075
        y: 454
        width: 47
        height: 45
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09a8d0f66c7e960b0800000000000000
      internalID: -5734797811026916720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_46
      rect:
        serializedVersion: 2
        x: 882
        y: 412
        width: 29
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f344b304dbe36fa30800000000000000
      internalID: 4248652281018008639
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_47
      rect:
        serializedVersion: 2
        x: 74
        y: 247
        width: 64
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6672d4738954881e0800000000000000
      internalID: -2195428298277968026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_48
      rect:
        serializedVersion: 2
        x: 264
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1d8345146c55084b0800000000000000
      internalID: -5440254039875634991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_49
      rect:
        serializedVersion: 2
        x: 456
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc48b86d4fb1e51e0800000000000000
      internalID: -2207296028979526449
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_50
      rect:
        serializedVersion: 2
        x: 622
        y: 248
        width: 118
        height: 109
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c1ec9b20759f60060800000000000000
      internalID: 6919492029604548124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_51
      rect:
        serializedVersion: 2
        x: 837
        y: 345
        width: 47
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d22e59af84054dd0800000000000000
      internalID: -2502589002866089255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_52
      rect:
        serializedVersion: 2
        x: 851
        y: 248
        width: 86
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f183190c4b3eb7130800000000000000
      internalID: 3565693895458240543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_53
      rect:
        serializedVersion: 2
        x: 1035
        y: 247
        width: 81
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68a679d6f7b705c10800000000000000
      internalID: 2040266418428537478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_54
      rect:
        serializedVersion: 2
        x: 603
        y: 290
        width: 10
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d3d14ad2d596f300800000000000000
      internalID: 285580359222023125
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_55
      rect:
        serializedVersion: 2
        x: 613
        y: 307
        width: 17
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9ad047b201437e70800000000000000
      internalID: 9111697951020866203
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_56
      rect:
        serializedVersion: 2
        x: 18
        y: 270
        width: 59
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 675ca8906b4da8660800000000000000
      internalID: 7388952016963945846
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_57
      rect:
        serializedVersion: 2
        x: 212
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aabbe477d8d62a4e0800000000000000
      internalID: -1971893232475391062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_58
      rect:
        serializedVersion: 2
        x: 404
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b81f345fd904c8aa0800000000000000
      internalID: -6157475543353396853
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_59
      rect:
        serializedVersion: 2
        x: 75
        y: 55
        width: 99
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd459a473928bf390800000000000000
      internalID: -7783483961150712612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_60
      rect:
        serializedVersion: 2
        x: 267
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dbe4a25b9079e8600800000000000000
      internalID: 472481078870429373
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_61
      rect:
        serializedVersion: 2
        x: 459
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88b319849ff7775d0800000000000000
      internalID: -3064840312761271416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_62
      rect:
        serializedVersion: 2
        x: 584
        y: 56
        width: 148
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a5623ee6344781a0800000000000000
      internalID: -6807397309032012378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_63
      rect:
        serializedVersion: 2
        x: 724
        y: 92
        width: 22
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 726e01b8c298b8430800000000000000
      internalID: 3786270736139740711
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_64
      rect:
        serializedVersion: 2
        x: 772
        y: 94
        width: 72
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2aac7597f0049ba0800000000000000
      internalID: -6083236133305800148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_65
      rect:
        serializedVersion: 2
        x: 826
        y: 56
        width: 74
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 098337564209022a0800000000000000
      internalID: -6764248154315212656
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_66
      rect:
        serializedVersion: 2
        x: 1000
        y: 86
        width: 31
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7beb6fedf9a6d76c0800000000000000
      internalID: -4144038847237931337
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Purple_67
      rect:
        serializedVersion: 2
        x: 1032
        y: 55
        width: 62
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8975a029c6e7fa1d0800000000000000
      internalID: -3337309794086529128
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Warrior_Purple_0: 5765384498261851376
      Warrior_Purple_1: 3510383775961326391
      Warrior_Purple_10: -2335009014406609423
      Warrior_Purple_11: -7738798194672193487
      Warrior_Purple_12: -5169136671150131587
      Warrior_Purple_13: -5025012844528068209
      Warrior_Purple_14: -8831741490759805623
      Warrior_Purple_15: 9116830380409544711
      Warrior_Purple_16: 2825128017446276215
      Warrior_Purple_17: -746617433663462992
      Warrior_Purple_18: 8855182847257345412
      Warrior_Purple_19: 8116094010307894819
      Warrior_Purple_2: 311859896922910450
      Warrior_Purple_20: 294536308096643482
      Warrior_Purple_21: -45704713307245354
      Warrior_Purple_22: 8101903878904849644
      Warrior_Purple_23: 6462237613833058123
      Warrior_Purple_24: -195529405602304008
      Warrior_Purple_25: -2989879161186267339
      Warrior_Purple_26: -7069217776186826657
      Warrior_Purple_27: 2834732174769014844
      Warrior_Purple_28: 1683401626382301725
      Warrior_Purple_29: 7124328658204828671
      Warrior_Purple_3: 3892754469516865428
      Warrior_Purple_30: -319467887583012568
      Warrior_Purple_31: 2938575399729292965
      Warrior_Purple_32: -3374141775889856228
      Warrior_Purple_33: -1660441683145606669
      Warrior_Purple_34: 8667007993062474251
      Warrior_Purple_35: -4712128825309896290
      Warrior_Purple_36: 6946090495268062392
      Warrior_Purple_37: 1841675742136149329
      Warrior_Purple_38: -3498638922822150334
      Warrior_Purple_39: 4912063175900776763
      Warrior_Purple_4: -8283658547208696731
      Warrior_Purple_40: 2749007752846430510
      Warrior_Purple_41: 1213642391800805766
      Warrior_Purple_42: -4423429437060330549
      Warrior_Purple_43: 7349423308944173942
      Warrior_Purple_44: -4273638034262830096
      Warrior_Purple_45: -5734797811026916720
      Warrior_Purple_46: 4248652281018008639
      Warrior_Purple_47: -2195428298277968026
      Warrior_Purple_48: -5440254039875634991
      Warrior_Purple_49: -2207296028979526449
      Warrior_Purple_5: -5149530005767681786
      Warrior_Purple_50: 6919492029604548124
      Warrior_Purple_51: -2502589002866089255
      Warrior_Purple_52: 3565693895458240543
      Warrior_Purple_53: 2040266418428537478
      Warrior_Purple_54: 285580359222023125
      Warrior_Purple_55: 9111697951020866203
      Warrior_Purple_56: 7388952016963945846
      Warrior_Purple_57: -1971893232475391062
      Warrior_Purple_58: -6157475543353396853
      Warrior_Purple_59: -7783483961150712612
      Warrior_Purple_6: 2677349103593817838
      Warrior_Purple_60: 472481078870429373
      Warrior_Purple_61: -3064840312761271416
      Warrior_Purple_62: -6807397309032012378
      Warrior_Purple_63: 3786270736139740711
      Warrior_Purple_64: -6083236133305800148
      Warrior_Purple_65: -6764248154315212656
      Warrior_Purple_66: -4144038847237931337
      Warrior_Purple_67: -3337309794086529128
      Warrior_Purple_7: -959103928103930023
      Warrior_Purple_8: -5986785513652630109
      Warrior_Purple_9: 9212685180267929163
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

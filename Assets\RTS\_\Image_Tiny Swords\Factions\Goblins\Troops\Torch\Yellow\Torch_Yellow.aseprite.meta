fileFormatVersion: 2
guid: 9a56581050a05cf4b8674e5c8f805187
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5633803, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 420
      width: 71
      height: 97
    spriteID: 933d7f07f5facc241aebed008d30bb19
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.6233766, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 809
      width: 77
      height: 97
    spriteID: ff6a3b337ac739446876724e12bd939f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 809}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.35999998, y: -0.6020408}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 914
      width: 75
      height: 98
    spriteID: 327f2f2dbeadaea4a8578316132246e8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 914}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5810811, y: -0.7468354}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 281
      width: 74
      height: 79
    spriteID: 209fba948142c90439c6c345b9715847
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 281}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.59999996, y: -0.71951216}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 889
      width: 75
      height: 82
    spriteID: f664a6299acab1b418414b39164e0d80
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 889}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.59459454, y: -0.7108433}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 4
      width: 74
      height: 83
    spriteID: 4b977ce48de63e247b880ef2c356e37c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5833334, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 189
      width: 72
      height: 84
    spriteID: 610ccef9e456c54498f9c4d1a3818221
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 189}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.57746476, y: -0.68604654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 95
      width: 71
      height: 86
    spriteID: 60f1dff0f3df2454aaa80d414b8696e8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 95}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.57746476, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 793
      width: 71
      height: 88
    spriteID: 986a8edc8c7b6f44eb4164c0fe60a4ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 793}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5633803, y: -0.6629213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 696
      width: 71
      height: 89
    spriteID: 8fe0757abc8ea604baae4e748906b7ba
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 696}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.7129629, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 85
      width: 108
      height: 73
    spriteID: 7da0db77ec6c94242bc175dbb2fb900d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 85}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.6868687, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 725
      width: 99
      height: 76
    spriteID: a724a1cd8b31f264786cd13359a76f4e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 725}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 166
      width: 105
      height: 73
    spriteID: da6534d9f105b6549ad046091dd820c5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 166}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.6936937, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 4
      width: 111
      height: 73
    spriteID: 71f5cafda6f759844a7b51ee8ff2cc02
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 4}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.68, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 641
      width: 100
      height: 76
    spriteID: ab9a63e3f6d55ce4da794bb8cbf1243c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 641}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 247
      width: 105
      height: 73
    spriteID: 87a9a9ecea9c4b24cb7ea51d9b6ce9ed
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 247}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.65, y: -0.6145833}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 537
      width: 80
      height: 96
    spriteID: 6029ae56868768444ab02a12dd64d0ee
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 537}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.6627907, y: -0.57843137}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 229
      width: 86
      height: 102
    spriteID: 8912792e4b5b9e542943584eab2c07ef
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 229}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.67777777, y: -0.5412844}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 112
      width: 90
      height: 109
    spriteID: 5a2bd7e6736d4a04696f9fb5028e2f85
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 112}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.4160584, y: -0.54716986}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 118
      width: 137
      height: 106
    spriteID: 1edbe07e0fe4f6e4087247869b72abf4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 118}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.41481486, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 232
      width: 135
      height: 107
    spriteID: 096629cf378cc5647a1f32e1a820af50
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 232}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.43089432, y: -0.5462963}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 585
      width: 123
      height: 108
    spriteID: 5b9755212111b684a8561e129bf1c100
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 585}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.6202531, y: -0.7375}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 608
      width: 79
      height: 80
    spriteID: 02851e2f7372531478aa63fec7d4241c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 608}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.6395349, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 328
      width: 86
      height: 84
    spriteID: 358bef0196e62054fbe3ba519f4ea973
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 328}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.65168536, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 441
      width: 89
      height: 88
    spriteID: 3374e12f2fdd49242ae260cf698f8c9a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 441}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.50862074, y: -0.2566372}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 701
      width: 116
      height: 113
    spriteID: 80987b1e82f99c44daff20d4651adeab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 701}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.48739493, y: -0.1965812}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 460
      width: 119
      height: 117
    spriteID: 5a13eca57955950468b75c552fa04517
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 460}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.39215687, y: -0.26999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 4
      width: 102
      height: 100
    spriteID: b023c172d10f9b14fb3e91cbf72a6c44
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 4}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.30769235, y: -0.78666663}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 525
      width: 91
      height: 75
    spriteID: 5c84ed7f05c52654d80a3faaf09b6a76
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 525}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.32978725, y: -0.4835165}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 923
      width: 94
      height: 91
    spriteID: ca34d7bedcccaa443be7414883623c1c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 923}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.34065938, y: -0.43617022}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 339
      width: 91
      height: 94
    spriteID: 4374651567e804149aefdee352f7a092
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 339}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.5928571, y: -0.44339624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 140
      height: 106
    spriteID: 8583fb8ec8b5daf459d6485dd6bc4558
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.6058394, y: -0.4857143}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 347
      width: 137
      height: 105
    spriteID: a1b6bd55ba1e0b646aea9d1569ee8e1e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 347}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.69827586, y: -0.63440853}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 822
      width: 116
      height: 93
    spriteID: c38d7246e06e85b4e861ff87a334b430
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 822}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 4054095833
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Torch_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 97
      spriteId: 933d7f07f5facc241aebed008d30bb19
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 59
        width: 77
        height: 97
      spriteId: ff6a3b337ac739446876724e12bd939f
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 59
        width: 75
        height: 98
      spriteId: 327f2f2dbeadaea4a8578316132246e8
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 59
        width: 74
        height: 79
      spriteId: 209fba948142c90439c6c345b9715847
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 59
        width: 75
        height: 82
      spriteId: f664a6299acab1b418414b39164e0d80
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 52
        y: 59
        width: 74
        height: 83
      spriteId: 4b977ce48de63e247b880ef2c356e37c
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 59
        width: 72
        height: 84
      spriteId: 610ccef9e456c54498f9c4d1a3818221
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 86
      spriteId: 60f1dff0f3df2454aaa80d414b8696e8
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 88
      spriteId: 986a8edc8c7b6f44eb4164c0fe60a4ed
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 89
      spriteId: 8fe0757abc8ea604baae4e748906b7ba
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 108
        height: 73
      spriteId: 7da0db77ec6c94242bc175dbb2fb900d
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 99
        height: 76
      spriteId: a724a1cd8b31f264786cd13359a76f4e
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: da6534d9f105b6549ad046091dd820c5
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 111
        height: 73
      spriteId: 71f5cafda6f759844a7b51ee8ff2cc02
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 100
        height: 76
      spriteId: ab9a63e3f6d55ce4da794bb8cbf1243c
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: 87a9a9ecea9c4b24cb7ea51d9b6ce9ed
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 44
        y: 59
        width: 80
        height: 96
      spriteId: 6029ae56868768444ab02a12dd64d0ee
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 59
        width: 86
        height: 102
      spriteId: 8912792e4b5b9e542943584eab2c07ef
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 59
        width: 90
        height: 109
      spriteId: 5a2bd7e6736d4a04696f9fb5028e2f85
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 58
        width: 137
        height: 106
      spriteId: 1edbe07e0fe4f6e4087247869b72abf4
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 57
        width: 135
        height: 107
      spriteId: 096629cf378cc5647a1f32e1a820af50
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 59
        width: 123
        height: 108
      spriteId: 5b9755212111b684a8561e129bf1c100
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 47
        y: 59
        width: 79
        height: 80
      spriteId: 02851e2f7372531478aa63fec7d4241c
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 59
        width: 86
        height: 84
      spriteId: 358bef0196e62054fbe3ba519f4ea973
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 59
        width: 89
        height: 88
      spriteId: 3374e12f2fdd49242ae260cf698f8c9a
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 116
        height: 113
      spriteId: 80987b1e82f99c44daff20d4651adeab
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 23
        width: 119
        height: 117
      spriteId: 5a13eca57955950468b75c552fa04517
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 27
        width: 102
        height: 100
      spriteId: b023c172d10f9b14fb3e91cbf72a6c44
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 59
        width: 91
        height: 75
      spriteId: 5c84ed7f05c52654d80a3faaf09b6a76
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 44
        width: 94
        height: 91
      spriteId: ca34d7bedcccaa443be7414883623c1c
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 41
        width: 91
        height: 94
      spriteId: 4374651567e804149aefdee352f7a092
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 47
        width: 140
        height: 106
      spriteId: 8583fb8ec8b5daf459d6485dd6bc4558
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 51
        width: 137
        height: 105
      spriteId: a1b6bd55ba1e0b646aea9d1569ee8e1e
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 15
        y: 59
        width: 116
        height: 93
      spriteId: c38d7246e06e85b4e861ff87a334b430
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

fileFormatVersion: 2
guid: 32a0f99147d7c52458257eb3021fe666
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4329173317399445151
    second: <PERSON><PERSON><PERSON>_Blue_0
  - first:
      213: 8555011917411257359
    second: <PERSON><PERSON><PERSON>_Blue_1
  - first:
      213: -8681932915944446542
    second: <PERSON>_<PERSON>_Blue_2
  - first:
      213: 5492699376048798953
    second: <PERSON><PERSON><PERSON>_Blue_3
  - first:
      213: -6841447439412970388
    second: <PERSON><PERSON><PERSON>_Blue_4
  - first:
      213: -2523372667273648364
    second: <PERSON><PERSON><PERSON>_<PERSON>_5
  - first:
      213: -1019287073827455614
    second: <PERSON><PERSON><PERSON>_Blue_6
  - first:
      213: 5112643037596333782
    second: <PERSON><PERSON><PERSON>_Blue_7
  - first:
      213: -3112607047936341857
    second: <PERSON><PERSON><PERSON>_Blue_8
  - first:
      213: -8743230344272488137
    second: <PERSON><PERSON><PERSON>_<PERSON>_9
  - first:
      213: -8750841540471951380
    second: <PERSON><PERSON><PERSON>_<PERSON>_10
  - first:
      213: 2538531249042620503
    second: <PERSON><PERSON><PERSON>_Blue_11
  - first:
      213: 3421040970401981108
    second: <PERSON><PERSON><PERSON>_<PERSON>_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Bow_Blue_0
      rect:
        serializedVersion: 2
        x: 68
        y: 259
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f9e71baf130541c30800000000000000
      internalID: 4329173317399445151
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_1
      rect:
        serializedVersion: 2
        x: 112
        y: 251
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0c0882330289b670800000000000000
      internalID: 8555011917411257359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_2
      rect:
        serializedVersion: 2
        x: 46
        y: 62
        width: 48
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b52c550304938780800000000000000
      internalID: -8681932915944446542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_3
      rect:
        serializedVersion: 2
        x: 96
        y: 63
        width: 20
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ecf56f1bfcf93c40800000000000000
      internalID: 5492699376048798953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_4
      rect:
        serializedVersion: 2
        x: 256
        y: 62
        width: 55
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6cb8482dcb4e01a0800000000000000
      internalID: -6841447439412970388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_5
      rect:
        serializedVersion: 2
        x: 475
        y: 62
        width: 67
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4176933bded2bfcd0800000000000000
      internalID: -2523372667273648364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_6
      rect:
        serializedVersion: 2
        x: 641
        y: 59
        width: 74
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28dfd588ec3cad1f0800000000000000
      internalID: -1019287073827455614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_7
      rect:
        serializedVersion: 2
        x: 828
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d6bd4731c1c3f640800000000000000
      internalID: 5112643037596333782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_8
      rect:
        serializedVersion: 2
        x: 1020
        y: 59
        width: 73
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f988066916ccdc4d0800000000000000
      internalID: -3112607047936341857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_9
      rect:
        serializedVersion: 2
        x: 1202
        y: 73
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 739d834535ec9a680800000000000000
      internalID: -8743230344272488137
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_10
      rect:
        serializedVersion: 2
        x: 1265
        y: 59
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cefef6e9bf3ce8680800000000000000
      internalID: -8750841540471951380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_11
      rect:
        serializedVersion: 2
        x: 1409
        y: 67
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7500a66a8bcaa3320800000000000000
      internalID: 2538531249042620503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Bow_Blue_12
      rect:
        serializedVersion: 2
        x: 1454
        y: 57
        width: 19
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b22eda13aaf97f20800000000000000
      internalID: 3421040970401981108
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Bow_Blue_0: 4329173317399445151
      Archer_Bow_Blue_1: 8555011917411257359
      Archer_Bow_Blue_10: -8750841540471951380
      Archer_Bow_Blue_11: 2538531249042620503
      Archer_Bow_Blue_12: 3421040970401981108
      Archer_Bow_Blue_2: -8681932915944446542
      Archer_Bow_Blue_3: 5492699376048798953
      Archer_Bow_Blue_4: -6841447439412970388
      Archer_Bow_Blue_5: -2523372667273648364
      Archer_Bow_Blue_6: -1019287073827455614
      Archer_Bow_Blue_7: 5112643037596333782
      Archer_Bow_Blue_8: -3112607047936341857
      Archer_Bow_Blue_9: -8743230344272488137
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: 8e0f523d75ee1914a8695196dabbbf5a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4018474691228133916
    second: TNT_Blue_0
  - first:
      213: -1322779965892065620
    second: TNT_Blue_1
  - first:
      213: -698591530043233218
    second: TNT_Blue_2
  - first:
      213: 2263616036653217452
    second: TNT_Blue_3
  - first:
      213: 9217159713942085056
    second: TNT_Blue_4
  - first:
      213: 4677824975902095554
    second: TNT_Blue_5
  - first:
      213: -1672866227228960289
    second: TNT_Blue_6
  - first:
      213: 8196633686204137313
    second: TNT_Blue_7
  - first:
      213: -2983388483930243164
    second: TNT_Blue_8
  - first:
      213: -4465804784606467390
    second: TNT_Blue_9
  - first:
      213: -1537509751666136771
    second: TNT_Blue_10
  - first:
      213: -455381705162110367
    second: TNT_Blue_11
  - first:
      213: -251161555349555468
    second: TNT_Blue_12
  - first:
      213: -1931856458177630270
    second: TNT_Blue_13
  - first:
      213: 6797021380799551527
    second: TNT_Blue_14
  - first:
      213: -6019588919026583489
    second: TNT_Blue_15
  - first:
      213: 6824378157758382570
    second: TNT_Blue_16
  - first:
      213: 7550169068514017198
    second: TNT_Blue_17
  - first:
      213: 4468404440877863613
    second: TNT_Blue_18
  - first:
      213: -3792055948110156169
    second: TNT_Blue_19
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: TNT_Blue_0
      rect:
        serializedVersion: 2
        x: 57
        y: 440
        width: 88
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e513722f828b38c0800000000000000
      internalID: -4018474691228133916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_1
      rect:
        serializedVersion: 2
        x: 250
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca6000121aa84ade0800000000000000
      internalID: -1322779965892065620
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_2
      rect:
        serializedVersion: 2
        x: 443
        y: 440
        width: 85
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3c2da864ba1e46f0800000000000000
      internalID: -698591530043233218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_3
      rect:
        serializedVersion: 2
        x: 634
        y: 440
        width: 88
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca6b79ccacaf96f10800000000000000
      internalID: 2263616036653217452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_4
      rect:
        serializedVersion: 2
        x: 824
        y: 440
        width: 88
        height: 67
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0cd1f8bdcede9ef70800000000000000
      internalID: 9217159713942085056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_5
      rect:
        serializedVersion: 2
        x: 1016
        y: 440
        width: 84
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c8381a0809fae040800000000000000
      internalID: 4677824975902095554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_6
      rect:
        serializedVersion: 2
        x: 1099
        y: 487
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd915419709c8c8e0800000000000000
      internalID: -1672866227228960289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_7
      rect:
        serializedVersion: 2
        x: 59
        y: 250
        width: 82
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16fb234f8ea40c170800000000000000
      internalID: 8196633686204137313
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_8
      rect:
        serializedVersion: 2
        x: 252
        y: 250
        width: 82
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4afb8540bffd896d0800000000000000
      internalID: -2983388483930243164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_9
      rect:
        serializedVersion: 2
        x: 443
        y: 250
        width: 85
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2caaa4195364602c0800000000000000
      internalID: -4465804784606467390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_10
      rect:
        serializedVersion: 2
        x: 633
        y: 250
        width: 87
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d39ad35e60ba9aae0800000000000000
      internalID: -1537509751666136771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_11
      rect:
        serializedVersion: 2
        x: 824
        y: 250
        width: 84
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16ac99a70c82ea9f0800000000000000
      internalID: -455381705162110367
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_12
      rect:
        serializedVersion: 2
        x: 1017
        y: 250
        width: 82
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f6b72865e1b38cf0800000000000000
      internalID: -251161555349555468
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_13
      rect:
        serializedVersion: 2
        x: 54
        y: 56
        width: 79
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2cf274569caa035e0800000000000000
      internalID: -1931856458177630270
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_14
      rect:
        serializedVersion: 2
        x: 242
        y: 56
        width: 78
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 728da450feed35e50800000000000000
      internalID: 6797021380799551527
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_15
      rect:
        serializedVersion: 2
        x: 439
        y: 56
        width: 77
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f38fa8375cf167ca0800000000000000
      internalID: -6019588919026583489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_16
      rect:
        serializedVersion: 2
        x: 633
        y: 56
        width: 76
        height: 65
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae18f1207cf05be50800000000000000
      internalID: 6824378157758382570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_17
      rect:
        serializedVersion: 2
        x: 824
        y: 56
        width: 75
        height: 70
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eaf7beae3c697c860800000000000000
      internalID: 7550169068514017198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_18
      rect:
        serializedVersion: 2
        x: 1014
        y: 56
        width: 66
        height: 83
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db29d431a26f20e30800000000000000
      internalID: 4468404440877863613
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Blue_19
      rect:
        serializedVersion: 2
        x: 1210
        y: 56
        width: 68
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77eac74d439ef5bc0800000000000000
      internalID: -3792055948110156169
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      TNT_Blue_0: -4018474691228133916
      TNT_Blue_1: -1322779965892065620
      TNT_Blue_10: -1537509751666136771
      TNT_Blue_11: -455381705162110367
      TNT_Blue_12: -251161555349555468
      TNT_Blue_13: -1931856458177630270
      TNT_Blue_14: 6797021380799551527
      TNT_Blue_15: -6019588919026583489
      TNT_Blue_16: 6824378157758382570
      TNT_Blue_17: 7550169068514017198
      TNT_Blue_18: 4468404440877863613
      TNT_Blue_19: -3792055948110156169
      TNT_Blue_2: -698591530043233218
      TNT_Blue_3: 2263616036653217452
      TNT_Blue_4: 9217159713942085056
      TNT_Blue_5: 4677824975902095554
      TNT_Blue_6: -1672866227228960289
      TNT_Blue_7: 8196633686204137313
      TNT_Blue_8: -2983388483930243164
      TNT_Blue_9: -4465804784606467390
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

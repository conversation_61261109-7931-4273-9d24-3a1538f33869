fileFormatVersion: 2
guid: 6c7291377f82bb24698a1b4ea7771790
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 2649847697073447219
    second: Dead_0
  - first:
      213: -8293461521501190705
    second: Dead_1
  - first:
      213: -1631342717326862633
    second: Dead_2
  - first:
      213: -3927913518680497933
    second: Dead_3
  - first:
      213: -1057675302478342458
    second: Dead_4
  - first:
      213: -7954960911384535938
    second: Dead_5
  - first:
      213: -1451345828014226549
    second: Dead_6
  - first:
      213: -2214552090018719568
    second: Dead_7
  - first:
      213: -8629073497498026496
    second: Dead_8
  - first:
      213: 7690500479702966033
    second: Dead_9
  - first:
      213: 1563200991656865400
    second: Dead_10
  - first:
      213: 8237937431785053145
    second: Dead_11
  - first:
      213: 2997290389361952000
    second: Dead_12
  - first:
      213: 4942476526513585314
    second: Dead_13
  - first:
      213: 2881778980199032837
    second: Dead_14
  - first:
      213: 5058045045001801558
    second: Dead_15
  - first:
      213: 4040790378147729158
    second: Dead_16
  - first:
      213: -5794410756507078890
    second: Dead_17
  - first:
      213: 6605484185259868286
    second: Dead_18
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Dead_0
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33123f4e07626c420800000000000000
      internalID: 2649847697073447219
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_1
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc50d84dfa4b7ec80800000000000000
      internalID: -8293461521501190705
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_2
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d6dfca117e4c59e0800000000000000
      internalID: -1631342717326862633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_3
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f0289a377f3d79c0800000000000000
      internalID: -3927913518680497933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_4
      rect:
        serializedVersion: 2
        x: 512
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c6131c8ae16251f0800000000000000
      internalID: -1057675302478342458
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_5
      rect:
        serializedVersion: 2
        x: 640
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7c9ec2c23d4a9190800000000000000
      internalID: -7954960911384535938
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_6
      rect:
        serializedVersion: 2
        x: 768
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8387f2b8a8cbdbe0800000000000000
      internalID: -1451345828014226549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_7
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b8d5d97b945441e0800000000000000
      internalID: -2214552090018719568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_8
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00e74e07f5f5f3880800000000000000
      internalID: -8629073497498026496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_9
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 11f9aa5b0752aba60800000000000000
      internalID: 7690500479702966033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_10
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87eee3c050b91b510800000000000000
      internalID: 1563200991656865400
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_11
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9db117f9278035270800000000000000
      internalID: 8237937431785053145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_12
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0058c929ab3889920800000000000000
      internalID: 2997290389361952000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dead_13
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2ac7c89e634379440800000000000000
      internalID: 4942476526513585314
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 8319d306253c117499788cd0d8ff3a53
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Dead_0: 2649847697073447219
      Dead_1: -8293461521501190705
      Dead_10: 1563200991656865400
      Dead_11: 8237937431785053145
      Dead_12: 2997290389361952000
      Dead_13: 4942476526513585314
      Dead_2: -1631342717326862633
      Dead_3: -3927913518680497933
      Dead_4: -1057675302478342458
      Dead_5: -7954960911384535938
      Dead_6: -1451345828014226549
      Dead_7: -2214552090018719568
      Dead_8: -8629073497498026496
      Dead_9: 7690500479702966033
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

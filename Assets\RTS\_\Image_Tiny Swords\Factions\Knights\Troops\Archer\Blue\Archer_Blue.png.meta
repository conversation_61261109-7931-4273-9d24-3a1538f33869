fileFormatVersion: 2
guid: cae82dca6a582614bb80e8b8a3ea7008
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7033695032449837365
    second: <PERSON><PERSON><PERSON>_0
  - first:
      213: 6114246488613233537
    second: <PERSON>_<PERSON>_1
  - first:
      213: -5010010259858565645
    second: <PERSON>_Blue_2
  - first:
      213: 8006088990915778119
    second: <PERSON>_Blue_3
  - first:
      213: -795545583119653244
    second: <PERSON>_<PERSON>_4
  - first:
      213: 7478542217812414065
    second: <PERSON>_<PERSON>_5
  - first:
      213: -3216901329665983845
    second: <PERSON>_<PERSON>_6
  - first:
      213: -5029872168309620029
    second: <PERSON>_<PERSON>_7
  - first:
      213: -9064025154593754007
    second: <PERSON>_Blue_8
  - first:
      213: -8278522423458954068
    second: <PERSON>_<PERSON>_9
  - first:
      213: -6069542870818123003
    second: <PERSON>_<PERSON>_10
  - first:
      213: -2712621913878235787
    second: <PERSON><PERSON><PERSON>_11
  - first:
      213: 5940929062476048998
    second: <PERSON>_<PERSON>_12
  - first:
      213: 2957541457843667387
    second: <PERSON>_<PERSON>_13
  - first:
      213: 8559424275854079522
    second: Archer_<PERSON>_14
  - first:
      213: -1830901047940275935
    second: <PERSON>_<PERSON>_15
  - first:
      213: -4227779676925609068
    second: <PERSON>_<PERSON>_16
  - first:
      213: 1126587424600933757
    second: <PERSON>_<PERSON>_17
  - first:
      213: -3992046011941290770
    second: Archer_Blue_18
  - first:
      213: -4524189058815036579
    second: Archer_Blue_19
  - first:
      213: -3130385886679002657
    second: Archer_Blue_20
  - first:
      213: -948891528349140180
    second: Archer_Blue_21
  - first:
      213: -3321098064577581836
    second: Archer_Blue_22
  - first:
      213: 5918292673152654742
    second: Archer_Blue_23
  - first:
      213: -1375868106999554863
    second: Archer_Blue_24
  - first:
      213: -3974645948555670355
    second: Archer_Blue_25
  - first:
      213: 2776042213502293496
    second: Archer_Blue_26
  - first:
      213: 7959813195889624457
    second: Archer_Blue_27
  - first:
      213: -3093811841882010217
    second: Archer_Blue_28
  - first:
      213: 6824409124470345055
    second: Archer_Blue_29
  - first:
      213: -7866555688576549673
    second: Archer_Blue_30
  - first:
      213: 3629576908501536167
    second: Archer_Blue_31
  - first:
      213: -7274046902297519927
    second: Archer_Blue_32
  - first:
      213: 8560472985470075578
    second: Archer_Blue_33
  - first:
      213: -5122523609243933794
    second: Archer_Blue_34
  - first:
      213: 2797235972633672342
    second: Archer_Blue_35
  - first:
      213: -3126045974256145544
    second: Archer_Blue_36
  - first:
      213: -1881630756298349670
    second: Archer_Blue_37
  - first:
      213: -1833571180098712622
    second: Archer_Blue_38
  - first:
      213: 167391717528496023
    second: Archer_Blue_39
  - first:
      213: -7388167028423196361
    second: Archer_Blue_40
  - first:
      213: 8198042478603660113
    second: Archer_Blue_41
  - first:
      213: 1511012060357282073
    second: Archer_Blue_42
  - first:
      213: 6344413207994965893
    second: Archer_Blue_43
  - first:
      213: -8129871543213522617
    second: Archer_Blue_44
  - first:
      213: 7418448941583349981
    second: Archer_Blue_45
  - first:
      213: 7641456314498658908
    second: Archer_Blue_46
  - first:
      213: -4959512633333549721
    second: Archer_Blue_47
  - first:
      213: 5009402246161469269
    second: Archer_Blue_48
  - first:
      213: 1253072248284259174
    second: Archer_Blue_49
  - first:
      213: -106705275555082950
    second: Archer_Blue_50
  - first:
      213: 1229654063248624451
    second: Archer_Blue_51
  - first:
      213: -1027743654290022984
    second: Archer_Blue_52
  - first:
      213: 4118516568766301594
    second: Archer_Blue_53
  - first:
      213: -1557887090808726545
    second: Archer_Blue_54
  - first:
      213: -206179087595372860
    second: Archer_Blue_55
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Archer_Blue_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bc6d56452ab436e90800000000000000
      internalID: -7033695032449837365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_1
      rect:
        serializedVersion: 2
        x: 192
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 183a8f669ca2ad450800000000000000
      internalID: 6114246488613233537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_2
      rect:
        serializedVersion: 2
        x: 384
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f5511c763ed87ab0800000000000000
      internalID: -5010010259858565645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_3
      rect:
        serializedVersion: 2
        x: 576
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74653b7e4875b1f60800000000000000
      internalID: 8006088990915778119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_4
      rect:
        serializedVersion: 2
        x: 768
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 486a583d187a5f4f0800000000000000
      internalID: -795545583119653244
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_5
      rect:
        serializedVersion: 2
        x: 960
        y: 1152
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17e887a268e19c760800000000000000
      internalID: 7478542217812414065
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_6
      rect:
        serializedVersion: 2
        x: 0
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9af6c7d8454b53d0800000000000000
      internalID: -3216901329665983845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_7
      rect:
        serializedVersion: 2
        x: 192
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c2084e89ed423ab0800000000000000
      internalID: -5029872168309620029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_8
      rect:
        serializedVersion: 2
        x: 384
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 964fb39252d163280800000000000000
      internalID: -9064025154593754007
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_9
      rect:
        serializedVersion: 2
        x: 576
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca48f94a7b7cc1d80800000000000000
      internalID: -8278522423458954068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_10
      rect:
        serializedVersion: 2
        x: 768
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50391e3bae6a4cba0800000000000000
      internalID: -6069542870818123003
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_11
      rect:
        serializedVersion: 2
        x: 960
        y: 960
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 575f85dcdb4da5ad0800000000000000
      internalID: -2712621913878235787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_12
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66aeda4818b627250800000000000000
      internalID: 5940929062476048998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_13
      rect:
        serializedVersion: 2
        x: 192
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb14343494c4b0920800000000000000
      internalID: 2957541457843667387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_14
      rect:
        serializedVersion: 2
        x: 384
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2263324770f29c670800000000000000
      internalID: 8559424275854079522
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_15
      rect:
        serializedVersion: 2
        x: 576
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12121aa82355796e0800000000000000
      internalID: -1830901047940275935
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_16
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49bbb3898c8e355c0800000000000000
      internalID: -4227779676925609068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_17
      rect:
        serializedVersion: 2
        x: 960
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d75c2c33b4172af00800000000000000
      internalID: 1126587424600933757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_18
      rect:
        serializedVersion: 2
        x: 1152
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eec793b8d476998c0800000000000000
      internalID: -3992046011941290770
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_19
      rect:
        serializedVersion: 2
        x: 1344
        y: 768
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d53fe9c650ad631c0800000000000000
      internalID: -4524189058815036579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_20
      rect:
        serializedVersion: 2
        x: 0
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd11a9e1f92ae84d0800000000000000
      internalID: -3130385886679002657
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_21
      rect:
        serializedVersion: 2
        x: 192
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2b3f52be2cd4d2f0800000000000000
      internalID: -948891528349140180
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_22
      rect:
        serializedVersion: 2
        x: 384
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4fcad9cf7e619e1d0800000000000000
      internalID: -3321098064577581836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_23
      rect:
        serializedVersion: 2
        x: 576
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69d9f0c64dff12250800000000000000
      internalID: 5918292673152654742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_24
      rect:
        serializedVersion: 2
        x: 768
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1dc90801f3fe7ece0800000000000000
      internalID: -1375868106999554863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_25
      rect:
        serializedVersion: 2
        x: 960
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dacc460819837d8c0800000000000000
      internalID: -3974645948555670355
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_26
      rect:
        serializedVersion: 2
        x: 1152
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f51b416fab768620800000000000000
      internalID: 2776042213502293496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_27
      rect:
        serializedVersion: 2
        x: 1344
        y: 576
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 985c773cdefe67e60800000000000000
      internalID: 7959813195889624457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_28
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79dc9b285829015d0800000000000000
      internalID: -3093811841882010217
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_29
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f5d4df101fb25be50800000000000000
      internalID: 6824409124470345055
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_30
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7dcf090b74164d290800000000000000
      internalID: -7866555688576549673
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_31
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7adc13cc7f8de5230800000000000000
      internalID: 3629576908501536167
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_32
      rect:
        serializedVersion: 2
        x: 768
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c89605c1e46d0b90800000000000000
      internalID: -7274046902297519927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_33
      rect:
        serializedVersion: 2
        x: 960
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab2854033d8ecc670800000000000000
      internalID: 8560472985470075578
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_34
      rect:
        serializedVersion: 2
        x: 1152
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e93a67deae329e8b0800000000000000
      internalID: -5122523609243933794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_35
      rect:
        serializedVersion: 2
        x: 1344
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69a36c5fb47c1d620800000000000000
      internalID: 2797235972633672342
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_36
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87b2ce8bfbd0e94d0800000000000000
      internalID: -3126045974256145544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_37
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a932364ebca13e5e0800000000000000
      internalID: -1881630756298349670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_38
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2db82d3f9b8dd86e0800000000000000
      internalID: -1833571180098712622
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_39
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 797e0a551e1b25200800000000000000
      internalID: 167391717528496023
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_40
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73105995b35f77990800000000000000
      internalID: -7388167028423196361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_41
      rect:
        serializedVersion: 2
        x: 960
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15bebbef23c45c170800000000000000
      internalID: 8198042478603660113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_42
      rect:
        serializedVersion: 2
        x: 1152
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9194de4967138f410800000000000000
      internalID: 1511012060357282073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_43
      rect:
        serializedVersion: 2
        x: 1344
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58f16d2d232eb0850800000000000000
      internalID: 6344413207994965893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_44
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7418335c7e4ec2f80800000000000000
      internalID: -8129871543213522617
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_45
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd8b27bf100a3f660800000000000000
      internalID: 7418448941583349981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_46
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c52bb582808eb0a60800000000000000
      internalID: 7641456314498658908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_47
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 765771f59854c2bb0800000000000000
      internalID: -4959512633333549721
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_48
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55f8f7b4dc8f48540800000000000000
      internalID: 5009402246161469269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_49
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66322ec129ec36110800000000000000
      internalID: 1253072248284259174
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_50
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3d01e20e18e48ef0800000000000000
      internalID: -106705275555082950
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Archer_Blue_51
      rect:
        serializedVersion: 2
        x: 1344
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34bcc840bdb901110800000000000000
      internalID: 1229654063248624451
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: d9259ea5c6ee6614aa15df9a9e3e5969
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Archer_Blue_0: -7033695032449837365
      Archer_Blue_1: 6114246488613233537
      Archer_Blue_10: -6069542870818123003
      Archer_Blue_11: -2712621913878235787
      Archer_Blue_12: 5940929062476048998
      Archer_Blue_13: 2957541457843667387
      Archer_Blue_14: 8559424275854079522
      Archer_Blue_15: -1830901047940275935
      Archer_Blue_16: -4227779676925609068
      Archer_Blue_17: 1126587424600933757
      Archer_Blue_18: -3992046011941290770
      Archer_Blue_19: -4524189058815036579
      Archer_Blue_2: -5010010259858565645
      Archer_Blue_20: -3130385886679002657
      Archer_Blue_21: -948891528349140180
      Archer_Blue_22: -3321098064577581836
      Archer_Blue_23: 5918292673152654742
      Archer_Blue_24: -1375868106999554863
      Archer_Blue_25: -3974645948555670355
      Archer_Blue_26: 2776042213502293496
      Archer_Blue_27: 7959813195889624457
      Archer_Blue_28: -3093811841882010217
      Archer_Blue_29: 6824409124470345055
      Archer_Blue_3: 8006088990915778119
      Archer_Blue_30: -7866555688576549673
      Archer_Blue_31: 3629576908501536167
      Archer_Blue_32: -7274046902297519927
      Archer_Blue_33: 8560472985470075578
      Archer_Blue_34: -5122523609243933794
      Archer_Blue_35: 2797235972633672342
      Archer_Blue_36: -3126045974256145544
      Archer_Blue_37: -1881630756298349670
      Archer_Blue_38: -1833571180098712622
      Archer_Blue_39: 167391717528496023
      Archer_Blue_4: -795545583119653244
      Archer_Blue_40: -7388167028423196361
      Archer_Blue_41: 8198042478603660113
      Archer_Blue_42: 1511012060357282073
      Archer_Blue_43: 6344413207994965893
      Archer_Blue_44: -8129871543213522617
      Archer_Blue_45: 7418448941583349981
      Archer_Blue_46: 7641456314498658908
      Archer_Blue_47: -4959512633333549721
      Archer_Blue_48: 5009402246161469269
      Archer_Blue_49: 1253072248284259174
      Archer_Blue_5: 7478542217812414065
      Archer_Blue_50: -106705275555082950
      Archer_Blue_51: 1229654063248624451
      Archer_Blue_6: -3216901329665983845
      Archer_Blue_7: -5029872168309620029
      Archer_Blue_8: -9064025154593754007
      Archer_Blue_9: -8278522423458954068
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

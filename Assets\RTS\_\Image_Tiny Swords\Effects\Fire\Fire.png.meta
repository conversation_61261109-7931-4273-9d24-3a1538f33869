fileFormatVersion: 2
guid: ab525e971682d3a4bbd99e17fd3615d5
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1098286324429101383
    second: Fire_0
  - first:
      213: 7419590508823533442
    second: Fire_1
  - first:
      213: 2333874723133794383
    second: Fire_2
  - first:
      213: 6211036013863027259
    second: Fire_3
  - first:
      213: 2168491126659702453
    second: Fire_4
  - first:
      213: 3081219861163341933
    second: Fire_5
  - first:
      213: 519553080483729991
    second: Fire_6
  - first:
      213: 964659150160153652
    second: Fire_7
  - first:
      213: -6879244290724207698
    second: Fire_8
  - first:
      213: 7786682891651826984
    second: Fire_9
  - first:
      213: 4566438268169137410
    second: Fire_10
  - first:
      213: -2935316082851191871
    second: Fire_11
  - first:
      213: 1139766347546768350
    second: Fire_12
  - first:
      213: -3504707368110082297
    second: Fire_13
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Fire_0
      rect:
        serializedVersion: 2
        x: 33
        y: 22
        width: 62
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7491445d795ed3f00800000000000000
      internalID: 1098286324429101383
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_1
      rect:
        serializedVersion: 2
        x: 177
        y: 98
        width: 27
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2875941d14ea7f660800000000000000
      internalID: 7419590508823533442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_2
      rect:
        serializedVersion: 2
        x: 306
        y: 101
        width: 24
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4827aef1b6936020800000000000000
      internalID: 2333874723133794383
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_3
      rect:
        serializedVersion: 2
        x: 429
        y: 76
        width: 36
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b32c67dd858023650800000000000000
      internalID: 6211036013863027259
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_4
      rect:
        serializedVersion: 2
        x: 437
        y: 109
        width: 19
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5be5327c037081e10800000000000000
      internalID: 2168491126659702453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_5
      rect:
        serializedVersion: 2
        x: 560
        y: 88
        width: 28
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d68f4838321b2ca20800000000000000
      internalID: 3081219861163341933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_6
      rect:
        serializedVersion: 2
        x: 699
        y: 99
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 742513426c2d53700800000000000000
      internalID: 519553080483729991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_7
      rect:
        serializedVersion: 2
        x: 808
        y: 16
        width: 51
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43c40a6d268236d00800000000000000
      internalID: 964659150160153652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_8
      rect:
        serializedVersion: 2
        x: 283
        y: 22
        width: 75
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea3ef30f3c30880a0800000000000000
      internalID: -6879244290724207698
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_9
      rect:
        serializedVersion: 2
        x: 306
        y: 99
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 825c48795dadf0c60800000000000000
      internalID: 7786682891651826984
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_10
      rect:
        serializedVersion: 2
        x: 159
        y: 23
        width: 67
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20da8fe296f3f5f30800000000000000
      internalID: 4566438268169137410
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_11
      rect:
        serializedVersion: 2
        x: 410
        y: 22
        width: 77
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c3e92a1399a347d0800000000000000
      internalID: -2935316082851191871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_12
      rect:
        serializedVersion: 2
        x: 547
        y: 16
        width: 58
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed33870547341df00800000000000000
      internalID: 1139766347546768350
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_13
      rect:
        serializedVersion: 2
        x: 680
        y: 13
        width: 50
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 707ba1aa737cc5fc0800000000000000
      internalID: -3504707368110082297
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Fire_0: 1098286324429101383
      Fire_1: 7419590508823533442
      Fire_10: 4566438268169137410
      Fire_11: -2935316082851191871
      Fire_12: 1139766347546768350
      Fire_13: -3504707368110082297
      Fire_2: 2333874723133794383
      Fire_3: 6211036013863027259
      Fire_4: 2168491126659702453
      Fire_5: 3081219861163341933
      Fire_6: 519553080483729991
      Fire_7: 964659150160153652
      Fire_8: -6879244290724207698
      Fire_9: 7786682891651826984
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

fileFormatVersion: 2
guid: 3674a24ffb72ed24a87e906733712f71
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1129200354784714896
    second: W_Spawn_0
  - first:
      213: 881351308166189228
    second: W_Spawn_1
  - first:
      213: 3484891072757752029
    second: W_Spawn_2
  - first:
      213: 1284026188990882361
    second: W_Spawn_3
  - first:
      213: 4539264352730145269
    second: W_Spawn_4
  - first:
      213: -6739551860241670898
    second: W_Spawn_5
  - first:
      213: -2986562603663768972
    second: W_Spawn_6
  - first:
      213: -8260901935368209594
    second: W_Spawn_7
  - first:
      213: -3162008498740508791
    second: W_Spawn_8
  - first:
      213: 3461960246151421611
    second: W_Spawn_9
  - first:
      213: 1524229058725262338
    second: W_Spawn_10
  - first:
      213: -8475518167817759937
    second: W_Spawn_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: W_Spawn_0
      rect:
        serializedVersion: 2
        x: 42
        y: 42
        width: 46
        height: 46
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 090203b6db9bbaf00800000000000000
      internalID: 1129200354784714896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_1
      rect:
        serializedVersion: 2
        x: 164
        y: 28
        width: 58
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca0529d58503b3c00800000000000000
      internalID: 881351308166189228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_2
      rect:
        serializedVersion: 2
        x: 294
        y: 50
        width: 53
        height: 37
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd0e7a887f1dc5030800000000000000
      internalID: 3484891072757752029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_3
      rect:
        serializedVersion: 2
        x: 295
        y: 79
        width: 27
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 932a91d0407c1d110800000000000000
      internalID: 1284026188990882361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_4
      rect:
        serializedVersion: 2
        x: 333
        y: 54
        width: 19
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f1e216f0e4befe30800000000000000
      internalID: 4539264352730145269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_5
      rect:
        serializedVersion: 2
        x: 425
        y: 55
        width: 52
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0dd8f07a4d4872a0800000000000000
      internalID: -6739551860241670898
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_6
      rect:
        serializedVersion: 2
        x: 290
        y: 44
        width: 11
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 472c72da2299d86d0800000000000000
      internalID: -2986562603663768972
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_7
      rect:
        serializedVersion: 2
        x: 303
        y: 28
        width: 37
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64b7a0a35716b5d80800000000000000
      internalID: -8260901935368209594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_8
      rect:
        serializedVersion: 2
        x: 436
        y: 28
        width: 33
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9874ecff50a4e14d0800000000000000
      internalID: -3162008498740508791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_9
      rect:
        serializedVersion: 2
        x: 546
        y: 24
        width: 59
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bae116a608a5b0030800000000000000
      internalID: 3461960246151421611
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_10
      rect:
        serializedVersion: 2
        x: 681
        y: 25
        width: 46
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 204b5a1d046272510800000000000000
      internalID: 1524229058725262338
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: W_Spawn_11
      rect:
        serializedVersion: 2
        x: 806
        y: 25
        width: 51
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f335adfa129e06a80800000000000000
      internalID: -8475518167817759937
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      W_Spawn_0: 1129200354784714896
      W_Spawn_1: 881351308166189228
      W_Spawn_10: 1524229058725262338
      W_Spawn_11: -8475518167817759937
      W_Spawn_2: 3484891072757752029
      W_Spawn_3: 1284026188990882361
      W_Spawn_4: 4539264352730145269
      W_Spawn_5: -6739551860241670898
      W_Spawn_6: -2986562603663768972
      W_Spawn_7: -8260901935368209594
      W_Spawn_8: -3162008498740508791
      W_Spawn_9: 3461960246151421611
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

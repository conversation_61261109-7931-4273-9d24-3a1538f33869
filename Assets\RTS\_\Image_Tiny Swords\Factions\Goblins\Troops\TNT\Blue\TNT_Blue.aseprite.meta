fileFormatVersion: 2
guid: 6f0247d07e1ad43469261173271af35e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44705883, y: -0.826087}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 85
      height: 69
    spriteID: d9d04a1ae7143e142a8eef2114480e8d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.44186047, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 4
      width: 86
      height: 68
    spriteID: 958e3623ee61fd24989ac1d7e6b56811
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 4}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44578314, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 380
      y: 4
      width: 83
      height: 68
    spriteID: 9b8abad57716d804eaf0b1a624118dcf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 380, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.43373492, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 88
      y: 81
      width: 83
      height: 68
    spriteID: 38b13706d348d7d45a75c5e25b689e37
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 88, y: 81}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.4302326, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 286
      y: 4
      width: 86
      height: 66
    spriteID: b75d8aa3c89243a4d933b9a87a235aab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 286, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.4534884, y: -0.8769231}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 179
      y: 81
      width: 86
      height: 65
    spriteID: 29033f20e31397148a9ca21f6fd6744f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 179, y: 81}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.44827586, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 191
      y: 4
      width: 87
      height: 66
    spriteID: f841c7436147d6a4981e446cdf3f99cf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 191, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.45000002, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 164
      width: 80
      height: 64
    spriteID: 7af6e764f8f30a7478b5648dcdbb8ce6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 164}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.4375, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 236
      width: 80
      height: 67
    spriteID: ba078af5d1dfdf947b99ab6a35f6abc9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 236}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.43373492, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 185
      y: 164
      width: 83
      height: 60
    spriteID: 913e3f05af088814e92c5ea7cbe03023
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 185, y: 164}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44705883, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 85
      height: 64
    spriteID: c79262ab2ba670341b8f5c0789277a86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47560975, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 358
      y: 81
      width: 82
      height: 67
    spriteID: ae0a58aeacc75454abf72fa006cc2259
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 358, y: 81}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.475, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 276
      y: 164
      width: 80
      height: 60
    spriteID: d76b9f4b29e1bf041a99b6c9200ba71a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 276, y: 164}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5324675, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 81
      width: 77
      height: 72
    spriteID: 90ee2c273b1900741bddb90474a850a0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 81}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.59210527, y: -0.76}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 81
      width: 76
      height: 75
    spriteID: 352c3e8bf7b093841b6f3fcb79698d82
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 81}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.53333336, y: -0.95}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 231
      y: 311
      width: 75
      height: 60
    spriteID: dbf79613d223f954585b7e12dae5e127
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 231, y: 311}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.51351345, y: -0.9047619}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 364
      y: 164
      width: 74
      height: 63
    spriteID: 0aec12411c39c464a9b06ff27936add6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 364, y: 164}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.53424656, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 76
      y: 311
      width: 73
      height: 68
    spriteID: 8a5bb1c09116efc46ba91f08e56e66b9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 76, y: 311}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.640625, y: -0.7037037}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 311
      width: 64
      height: 81
    spriteID: b9e8ea4c7ff25454ebca7de1860c23b8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 311}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5606061, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 157
      y: 311
      width: 66
      height: 72
    spriteID: cd4da061e2396cd47ae20903da371297
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 157, y: 311}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 481
      y: 164
      width: 27
      height: 51
    spriteID: a44abe818e494c14c96b86b1ec3b2a83
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 481, y: 164}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.44444445, y: -1.4423077}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 446
      y: 164
      width: 27
      height: 52
    spriteID: 8615c413ac9115341a3ae8ff6a47a404
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 446, y: 164}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.46153846, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 314
      y: 311
      width: 26
      height: 51
    spriteID: 480d12abcef297942bef7a0a102a84dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 314, y: 311}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.48, y: -1.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 483
      y: 81
      width: 25
      height: 50
    spriteID: ea511af5551e3e1428007e2840e28078
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 483, y: 81}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 448
      y: 81
      width: 27
      height: 51
    spriteID: 18ce2c3106de4354a982cf7869155ea8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 448, y: 81}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.42857143, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 471
      y: 4
      width: 28
      height: 51
    spriteID: 1df309603d9b5ae44af7e1b479a85388
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 471, y: 4}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2713023203
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: TNT_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 85
        height: 69
      spriteId: d9d04a1ae7143e142a8eef2114480e8d
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 86
        height: 68
      spriteId: 958e3623ee61fd24989ac1d7e6b56811
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 83
        height: 68
      spriteId: 9b8abad57716d804eaf0b1a624118dcf
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 57
        width: 83
        height: 68
      spriteId: 38b13706d348d7d45a75c5e25b689e37
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 86
        height: 66
      spriteId: b75d8aa3c89243a4d933b9a87a235aab
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 86
        height: 65
      spriteId: 29033f20e31397148a9ca21f6fd6744f
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 87
        height: 66
      spriteId: f841c7436147d6a4981e446cdf3f99cf
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 80
        height: 64
      spriteId: 7af6e764f8f30a7478b5648dcdbb8ce6
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 59
        width: 80
        height: 67
      spriteId: ba078af5d1dfdf947b99ab6a35f6abc9
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 83
        height: 60
      spriteId: 913e3f05af088814e92c5ea7cbe03023
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 85
        height: 64
      spriteId: c79262ab2ba670341b8f5c0789277a86
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 59
        width: 82
        height: 67
      spriteId: ae0a58aeacc75454abf72fa006cc2259
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 80
        height: 60
      spriteId: d76b9f4b29e1bf041a99b6c9200ba71a
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 77
        height: 72
      spriteId: 90ee2c273b1900741bddb90474a850a0
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 57
        width: 76
        height: 75
      spriteId: 352c3e8bf7b093841b6f3fcb79698d82
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 57
        width: 75
        height: 60
      spriteId: dbf79613d223f954585b7e12dae5e127
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 74
        height: 63
      spriteId: 0aec12411c39c464a9b06ff27936add6
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 73
        height: 68
      spriteId: 8a5bb1c09116efc46ba91f08e56e66b9
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 64
        height: 81
      spriteId: b9e8ea4c7ff25454ebca7de1860c23b8
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 66
        height: 72
      spriteId: cd4da061e2396cd47ae20903da371297
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: a44abe818e494c14c96b86b1ec3b2a83
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 52
      spriteId: 8615c413ac9115341a3ae8ff6a47a404
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 26
        height: 51
      spriteId: 480d12abcef297942bef7a0a102a84dc
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 25
        height: 50
      spriteId: ea511af5551e3e1428007e2840e28078
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: 18ce2c3106de4354a982cf7869155ea8
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 28
        height: 51
      spriteId: 1df309603d9b5ae44af7e1b479a85388
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

using UnityEngine;

public class CameraController
{
    private float m_PanSpeed;
    private float m_MobilePanSpeed;
    private bool m_IsDragging = false;
    private Vector3 m_LastInputPosition;
    private float m_DragThreshold = 5f; // الحد الأدنى للحركة لاعتبارها سحب
    private bool m_HasMoved = false;

    public bool LockCamera { get; set; }
    public bool IsDragging => m_IsDragging && m_HasMoved; // خاصية للتحقق من حالة السحب

    public CameraController(float panSpeed, float mobilePanSpeed)
    {
        m_PanSpeed = panSpeed;
        m_MobilePanSpeed = mobilePanSpeed;
    }

    public void Update()
    {
        if (LockCamera) return;

        HandleInput();
    }

    private void HandleInput()
    {
        if (Input.touchCount == 1)
        {
            HandleTouchInput();
        }
        else if (Input.touchCount == 0)
        {
            HandleMouseInput();
        }
    }

    private void HandleTouchInput()
    {
        var touch = Input.GetTouch(0);
        Vector3 currentInputPosition = touch.position;

        if (touch.phase == TouchPhase.Began)
        {
            StartDrag(currentInputPosition);
        }
        else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
        {
            EndDrag();
        }
        else if (touch.phase == TouchPhase.Moved && m_IsDragging)
        {
            ProcessDrag(currentInputPosition, true);
        }
    }

    private void HandleMouseInput()
    {
        Vector3 currentInputPosition = Input.mousePosition;

        if (Input.GetMouseButtonDown(0))
        {
            StartDrag(currentInputPosition);
        }
        else if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
        else if (Input.GetMouseButton(0) && m_IsDragging)
        {
            ProcessDrag(currentInputPosition, false);
        }
    }

    private void StartDrag(Vector3 inputPosition)
    {
        m_IsDragging = true;
        m_HasMoved = false;
        m_LastInputPosition = inputPosition;
    }

    private void EndDrag()
    {
        m_IsDragging = false;
        m_HasMoved = false;
    }

    private void ProcessDrag(Vector3 currentInputPosition, bool isTouchInput)
    {
        Vector3 deltaPosition = currentInputPosition - m_LastInputPosition;

        // التحقق من تجاوز الحد الأدنى للحركة
        if (!m_HasMoved && deltaPosition.magnitude > m_DragThreshold)
        {
            m_HasMoved = true;
        }

        if (m_HasMoved)
        {
            if (isTouchInput)
            {
                // للمس: استخدام deltaPosition مباشرة
                Vector2 normalizedDelta = deltaPosition / new Vector2(Screen.width, Screen.height);
                Vector3 worldDelta = new Vector3(
                    -normalizedDelta.x * m_MobilePanSpeed,
                    -normalizedDelta.y * m_MobilePanSpeed,
                    0
                );
                Camera.main.transform.position += worldDelta;
            }
            else
            {
                // للماوس: استخدام Input.GetAxis للحصول على حركة أكثر سلاسة
                Vector2 mouseDelta = new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));
                Vector3 worldDelta = new Vector3(
                    -mouseDelta.x * Time.deltaTime * m_PanSpeed,
                    -mouseDelta.y * Time.deltaTime * m_PanSpeed,
                    0
                );
                Camera.main.transform.position += worldDelta;
            }
        }

        m_LastInputPosition = currentInputPosition;
    }
}

// using UnityEngine;


// public class CameraController
// {
//     private float m_PanSpeed;
//     private float m_MobilePanSpeed;


//     private bool m_IsDragging = false; // new


//     public bool LockCamera { get; set; }

//     public CameraController(float panSpeed, float mobilePanSpeed)
//     {
//         m_PanSpeed = panSpeed;
//         m_MobilePanSpeed = mobilePanSpeed;
//     }


//     // public void Update()
//     // {
//     //     if (LockCamera) return;

//     //     if (Input.touchCount == 1 && Input.GetTouch(0).phase == TouchPhase.Moved)
//     //     {

//     //         Vector2 touchDeltaPosition = Input.GetTouch(0).deltaPosition;
//     //         Vector2 normalizedDelta = touchDeltaPosition / new Vector2(Screen.width, Screen.height);

//     //         Camera.main.transform.Translate(
//     //             -normalizedDelta.x * m_MobilePanSpeed,
//     //             -normalizedDelta.y * m_MobilePanSpeed, 0
//     //         );

//     //     }

//     //     else if (Input.touchCount == 0 && Input.GetMouseButton(0))
//     //     {
//     //         Vector2 mouseDeltaPosition = new(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));

//     //         Camera.main.transform.Translate(
//     //            -mouseDeltaPosition.x * Time.deltaTime * m_PanSpeed,
//     //            -mouseDeltaPosition.y * Time.deltaTime * m_PanSpeed, 0
//     //         );


//     //     }

//     // }

//     public void Update()
//     {
//         if (LockCamera) return;

//         if (Input.touchCount == 1)
//         {
//             var touch = Input.GetTouch(0);

//             if (touch.phase == TouchPhase.Began)
//             {
//                 m_IsDragging = true;
//             }
//             else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
//             {
//                 m_IsDragging = false;
//             }
//             else if (touch.phase == TouchPhase.Moved && m_IsDragging)
//             {
//                 Vector2 touchDeltaPosition = touch.deltaPosition;
//                 Vector2 normalizedDelta = touchDeltaPosition / new Vector2(Screen.width, Screen.height);

//                 Camera.main.transform.Translate(
//                     -normalizedDelta.x * m_MobilePanSpeed,
//                     -normalizedDelta.y * m_MobilePanSpeed, 0
//                 );
//             }
//         }
//         else if (Input.touchCount == 0)
//         {
//             if (Input.GetMouseButtonDown(0))
//             {
//                 m_IsDragging = true;
//             }
//             else if (Input.GetMouseButtonUp(0))
//             {
//                 m_IsDragging = false;
//             }
//             else if (Input.GetMouseButton(0) && m_IsDragging)
//             {
//                 Vector2 mouseDeltaPosition = new(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));

//                 Camera.main.transform.Translate(
//                    -mouseDeltaPosition.x * Time.deltaTime * m_PanSpeed,
//                    -mouseDeltaPosition.y * Time.deltaTime * m_PanSpeed, 0
//                 );
//             }
//         }
//     }




// }// end class
using UnityEngine;

public class CameraController
{
    private float m_PanSpeed;
    private float m_MobilePanSpeed;
    private bool m_IsDragging = false;
    private Vector3 m_TargetPosition;
    private float m_SmoothSpeed = 10f;

    public bool LockCamera { get; set; }

    public CameraController(float panSpeed, float mobilePanSpeed)
    {
        m_PanSpeed = panSpeed;
        m_MobilePanSpeed = mobilePanSpeed;
        m_TargetPosition = Camera.main.transform.position;
    }

    public void Update()
    {
        if (LockCamera) return;

        if (Input.touchCount == 1)
        {
            var touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Began)
            {
                m_IsDragging = true;
            }
            else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
            {
                m_IsDragging = false;
            }
            else if (touch.phase == TouchPhase.Moved && m_IsDragging)
            {
                Vector2 touchDeltaPosition = touch.deltaPosition;
                Vector2 normalizedDelta = touchDeltaPosition / new Vector2(Screen.width, Screen.height);

                m_TargetPosition += new Vector3(
                    -normalizedDelta.x * m_MobilePanSpeed,
                    -normalizedDelta.y * m_MobilePanSpeed,
                    0
                );
            }
        }
        else if (Input.touchCount == 0)
        {
            if (Input.GetMouseButtonDown(0))
            {
                m_IsDragging = true;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                m_IsDragging = false;
            }
            else if (Input.GetMouseButton(0) && m_IsDragging)
            {
                Vector2 mouseDeltaPosition = new(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));

                m_TargetPosition += new Vector3(
                    -mouseDeltaPosition.x * Time.deltaTime * m_PanSpeed,
                    -mouseDeltaPosition.y * Time.deltaTime * m_PanSpeed,
                    0
                );
            }
        }

        // تحديث موضع الكاميرا بشكل سلس
        Camera.main.transform.position = Vector3.Lerp(
            Camera.main.transform.position,
            m_TargetPosition,
            Time.deltaTime * m_SmoothSpeed
        );
    }
}

// using UnityEngine;


// public class CameraController
// {
//     private float m_PanSpeed;
//     private float m_MobilePanSpeed;


//     private bool m_IsDragging = false; // new


//     public bool LockCamera { get; set; }

//     public CameraController(float panSpeed, float mobilePanSpeed)
//     {
//         m_PanSpeed = panSpeed;
//         m_MobilePanSpeed = mobilePanSpeed;
//     }


//     // public void Update()
//     // {
//     //     if (LockCamera) return;

//     //     if (Input.touchCount == 1 && Input.GetTouch(0).phase == TouchPhase.Moved)
//     //     {

//     //         Vector2 touchDeltaPosition = Input.GetTouch(0).deltaPosition;
//     //         Vector2 normalizedDelta = touchDeltaPosition / new Vector2(Screen.width, Screen.height);

//     //         Camera.main.transform.Translate(
//     //             -normalizedDelta.x * m_MobilePanSpeed,
//     //             -normalizedDelta.y * m_MobilePanSpeed, 0
//     //         );

//     //     }

//     //     else if (Input.touchCount == 0 && Input.GetMouseButton(0))
//     //     {
//     //         Vector2 mouseDeltaPosition = new(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));

//     //         Camera.main.transform.Translate(
//     //            -mouseDeltaPosition.x * Time.deltaTime * m_PanSpeed,
//     //            -mouseDeltaPosition.y * Time.deltaTime * m_PanSpeed, 0
//     //         );


//     //     }

//     // }

//     public void Update()
//     {
//         if (LockCamera) return;

//         if (Input.touchCount == 1)
//         {
//             var touch = Input.GetTouch(0);

//             if (touch.phase == TouchPhase.Began)
//             {
//                 m_IsDragging = true;
//             }
//             else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
//             {
//                 m_IsDragging = false;
//             }
//             else if (touch.phase == TouchPhase.Moved && m_IsDragging)
//             {
//                 Vector2 touchDeltaPosition = touch.deltaPosition;
//                 Vector2 normalizedDelta = touchDeltaPosition / new Vector2(Screen.width, Screen.height);

//                 Camera.main.transform.Translate(
//                     -normalizedDelta.x * m_MobilePanSpeed,
//                     -normalizedDelta.y * m_MobilePanSpeed, 0
//                 );
//             }
//         }
//         else if (Input.touchCount == 0)
//         {
//             if (Input.GetMouseButtonDown(0))
//             {
//                 m_IsDragging = true;
//             }
//             else if (Input.GetMouseButtonUp(0))
//             {
//                 m_IsDragging = false;
//             }
//             else if (Input.GetMouseButton(0) && m_IsDragging)
//             {
//                 Vector2 mouseDeltaPosition = new(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y"));

//                 Camera.main.transform.Translate(
//                    -mouseDeltaPosition.x * Time.deltaTime * m_PanSpeed,
//                    -mouseDeltaPosition.y * Time.deltaTime * m_PanSpeed, 0
//                 );
//             }
//         }
//     }




// }// end class
using UnityEngine;

/// <summary>
/// يمثل وحدة عدو في اللعبة، مع سلوك هجومي تجاه وحدات اللاعب. يتضمن منطق البحث عن الأهداف والهجوم والمطاردة.
/// </summary>
/// <remarks>
/// يرث من: HumanoidUnit
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class EnemyUnit : HumanoidUnit
{
    private float m_AttackCommitmentTime = 1f;
    private float m_CurrentAttackCommitmentTime = 0;
    public override bool IsPlayer => false;


    protected override void UpdateBehavior()
    {

        switch (CurrentState)
        {
            case UnitState.Idle:
            case UnitState.Moving:
                if (HasTarget)
                {
                    if (IsTargetInRange(Target))
                    {
                        SetUnitState(UnitState.Attacking);
                        StopMovement();
                    }
                    else
                    {
                        MoveTo(Target.transform.position);
                    }
                }
                else
                {
                    if (TryFindClosestTarget(out var target))
                    {
                        SetTarget(target);
                        MoveTo(target.transform.position);
                        // Debug.Log("Target Detected - Move to target!!");
                    }
                }
                break;

            case UnitState.Attacking:
                if (HasTarget)
                {
                    if (IsTargetInRange(Target))
                    {
                        m_CurrentAttackCommitmentTime = m_AttackCommitmentTime; // inital it = 1second
                        TryAttackCurrentTarget();
                    }
                    else
                    {
                        m_CurrentAttackCommitmentTime -= Time.deltaTime;
                        if (m_CurrentAttackCommitmentTime <= 0)
                        {
                            SetUnitState(UnitState.Moving);
                        }
                    }
                }
                else
                {
                    //Debug.Log("Back To Idle State");
                    SetUnitState(UnitState.Idle);
                }
                break;
        }



    }


}// end class


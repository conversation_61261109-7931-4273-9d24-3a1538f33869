fileFormatVersion: 2
guid: cbb6a159da0d10044bc29218e8c1eaf4
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1723869735436879832
    second: TNT_Red_0
  - first:
      213: -4575732086333183823
    second: TNT_Red_1
  - first:
      213: -8545997469525324644
    second: TNT_Red_2
  - first:
      213: 472558964566126397
    second: TNT_Red_3
  - first:
      213: -3272573693725979801
    second: TNT_Red_4
  - first:
      213: 4073718598777970583
    second: TNT_Red_5
  - first:
      213: -1850014194413969036
    second: TNT_Red_6
  - first:
      213: 6965017248118912694
    second: TNT_Red_7
  - first:
      213: -3503147640080845908
    second: TNT_Red_8
  - first:
      213: 2920799376480564313
    second: TNT_Red_9
  - first:
      213: -5227482290639326191
    second: TNT_Red_10
  - first:
      213: 6442362681435007131
    second: TNT_Red_11
  - first:
      213: -9053253831616230514
    second: TNT_Red_12
  - first:
      213: -8362919833067389751
    second: TNT_Red_13
  - first:
      213: -8159456244472171497
    second: TNT_Red_14
  - first:
      213: 8258795194819198663
    second: TNT_Red_15
  - first:
      213: 3382624021105550442
    second: TNT_Red_16
  - first:
      213: 1747851038808546189
    second: TNT_Red_17
  - first:
      213: -1111592450808163120
    second: TNT_Red_18
  - first:
      213: 4280689182580048338
    second: TNT_Red_19
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: TNT_Red_0
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d703c3146a6ce710800000000000000
      internalID: 1723869735436879832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_1
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b8e0c629ebbf70c0800000000000000
      internalID: -4575732086333183823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_2
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9c4e818494866980800000000000000
      internalID: -8545997469525324644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_3
      rect:
        serializedVersion: 2
        x: 576
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3fe362efddde8600800000000000000
      internalID: 472558964566126397
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_4
      rect:
        serializedVersion: 2
        x: 768
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76bcc2c709b7592d0800000000000000
      internalID: -3272573693725979801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_5
      rect:
        serializedVersion: 2
        x: 960
        y: 384
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79f101b6b71c88830800000000000000
      internalID: 4073718598777970583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_6
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 471668854ed6356e0800000000000000
      internalID: -1850014194413969036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_7
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b6b890f846b8a060800000000000000
      internalID: 6965017248118912694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_8
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca3bb3528c1526fc0800000000000000
      internalID: -3503147640080845908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_9
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9547e4fce83c88820800000000000000
      internalID: 2920799376480564313
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_10
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1143cc49d804477b0800000000000000
      internalID: -5227482290639326191
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_11
      rect:
        serializedVersion: 2
        x: 960
        y: 192
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b98aa25c9bed76950800000000000000
      internalID: 6442362681435007131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8b02193b916c5280800000000000000
      internalID: -9053253831616230514
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_13
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ccf2f078b0f0fb80800000000000000
      internalID: -8362919833067389751
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_14
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 714b4ebb6c9c3ce80800000000000000
      internalID: -8159456244472171497
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_15
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ceb6e309722d9270800000000000000
      internalID: 8258795194819198663
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_16
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6423b210ae71fe20800000000000000
      internalID: 3382624021105550442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_17
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8b1b70624d914810800000000000000
      internalID: 1747851038808546189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TNT_Red_18
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 192
        height: 192
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d4d2923e84d290f0800000000000000
      internalID: -1111592450808163120
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: c84529f7319c65c4e89df6537c8c9ecb
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      TNT_Red_0: 1723869735436879832
      TNT_Red_1: -4575732086333183823
      TNT_Red_10: -5227482290639326191
      TNT_Red_11: 6442362681435007131
      TNT_Red_12: -9053253831616230514
      TNT_Red_13: -8362919833067389751
      TNT_Red_14: -8159456244472171497
      TNT_Red_15: 8258795194819198663
      TNT_Red_16: 3382624021105550442
      TNT_Red_17: 1747851038808546189
      TNT_Red_18: -1111592450808163120
      TNT_Red_2: -8545997469525324644
      TNT_Red_3: 472558964566126397
      TNT_Red_4: -3272573693725979801
      TNT_Red_5: 4073718598777970583
      TNT_Red_6: -1850014194413969036
      TNT_Red_7: 6965017248118912694
      TNT_Red_8: -3503147640080845908
      TNT_Red_9: 2920799376480564313
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

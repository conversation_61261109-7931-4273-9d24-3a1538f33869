fileFormatVersion: 2
guid: dbc64949d84bed64c8e90b8b58ec4fcb
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 295
      width: 58
      height: 59
    spriteID: 90c91773a3a5d5b4d87ad5ff8d038f09
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 295}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.49999997, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 295
      width: 58
      height: 59
    spriteID: c3e14eb7b1d8ff343932b027e3929486
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 295}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5172414, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 363
      width: 58
      height: 57
    spriteID: 3a254a23260faf5418a6f3ed0d82c3ba
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 363}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5172414, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 363
      width: 58
      height: 56
    spriteID: d73b4b6c8dc5d174589475994fb94719
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 363}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 363
      width: 58
      height: 57
    spriteID: 863f4326135b46d4692a4af8c5192a8f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 363}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 363
      width: 58
      height: 58
    spriteID: 6c34b6d88dc4dcf48baa884e63c83844
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 363}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 202
      y: 295
      width: 58
      height: 59
    spriteID: 82ccd2136259d2e46b52458e58fae52e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 202, y: 295}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 141
      y: 227
      width: 58
      height: 60
    spriteID: 8b062543e6f6ca84692bd749e26d0c26
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 141, y: 227}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 268
      y: 295
      width: 58
      height: 59
    spriteID: ebc5afada45482e488eb2b3015cdaef1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 268, y: 295}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 243
      y: 78
      width: 58
      height: 55
    spriteID: 0625884019f5475408d4dd5001431430
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 243, y: 78}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.48387095, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 227
      width: 62
      height: 60
    spriteID: 423ec128ef1a7e647bc20c3ec7346611
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 227}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.49152538, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 74
      y: 227
      width: 59
      height: 59
    spriteID: 4212dc89e31b18b458a362b32b14229e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 74, y: 227}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.49999997, y: -1.1636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 309
      y: 78
      width: 58
      height: 55
    spriteID: da97ebae91901a3419bf012a53f52bac
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 309, y: 78}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.49230766, y: -0.85333335}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 150
      y: 142
      width: 65
      height: 75
    spriteID: 558bfe1c4a59acc4d9565f29e68a61f2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 150, y: 142}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 142
      width: 65
      height: 77
    spriteID: 1ddb86d20111e89438c33e2fab3bd309
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 142}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.50769234, y: -0.83116883}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 77
      y: 142
      width: 65
      height: 77
    spriteID: 89ac835b257309840a696c817127855a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 77, y: 142}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.34782606, y: -0.969697}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 92
      height: 66
    spriteID: ddaf2365f0ab2b348ba1891429ed1dcb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.3483146, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 414
      y: 4
      width: 89
      height: 56
    spriteID: c018507f87de9b94899bb8de88acbde5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 414, y: 4}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.3421052, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 389
      y: 142
      width: 76
      height: 58
    spriteID: 7bd0d8b7ec730144a9454e4603730c8b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 389, y: 142}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.654321, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 223
      y: 142
      width: 81
      height: 60
    spriteID: b83d2420b125d304bb09ea9e2ac61c2c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 223, y: 142}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 222
      y: 4
      width: 88
      height: 64
    spriteID: 4a61a1915a3de2040bc76bc765ea5405
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 222, y: 4}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.6704546, y: -1}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 318
      y: 4
      width: 88
      height: 64
    spriteID: a53571ba49785d840bb0037b4fc40fd8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 318, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.3909091, y: -1.1851852}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 104
      y: 4
      width: 110
      height: 54
    spriteID: 0fa38d647042651468c989c2ee0b0320
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 104, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.33333334, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 78
      width: 99
      height: 56
    spriteID: 7e7f955c877bd2d45a7e0e84b833d270
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 78}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.53623194, y: -0.8484849}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 312
      y: 142
      width: 69
      height: 66
    spriteID: 548fbabf4330c07469378e3ddd239f70
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 312, y: 142}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.49999997, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 207
      y: 227
      width: 58
      height: 60
    spriteID: b081a1fac90327642a4d515f5c0d70d7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 207, y: 227}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.5172414, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 295
      width: 58
      height: 59
    spriteID: d8b19322115b2a242901284e640963e1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 295}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.5172414, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 70
      y: 363
      width: 58
      height: 58
    spriteID: c130c510abaa7d447931535f7121cfa4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 70, y: 363}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.49999997, y: -1.122807}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 334
      y: 363
      width: 58
      height: 57
    spriteID: aa11ac84e6c75c249804319e44220d44
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 334, y: 363}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.48275867, y: -1.1034484}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 136
      y: 363
      width: 58
      height: 58
    spriteID: fb382b4516a4b4c4abab2eba5ddfe767
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 136, y: 363}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.48275867, y: -1.0847458}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 400
      y: 295
      width: 58
      height: 59
    spriteID: 0ace934e4893a8244a630fcd8a27a91e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 400, y: 295}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.53448284, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 227
      width: 58
      height: 60
    spriteID: f0bbf32f8c7a3834daa6dac46295c13b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 227}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.5172414, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 339
      y: 227
      width: 58
      height: 60
    spriteID: 1d5180ecf134ceb4ea4d8757270d092e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 339, y: 227}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 111
      y: 78
      width: 58
      height: 56
    spriteID: c8911833ca1297d44af9163bf0b0fc88
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 111, y: 78}
  - name: Frame_34
    originalName: 
    pivot: {x: 0.46551725, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 405
      y: 227
      width: 58
      height: 60
    spriteID: 6464367239301bd4180b83ff900a6efe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 405, y: 227}
  - name: Frame_35
    originalName: 
    pivot: {x: 0.48275867, y: -1.0666667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 295
      width: 58
      height: 60
    spriteID: ea94c555fa103fb4db068e17fe140589
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 295}
  - name: Frame_36
    originalName: 
    pivot: {x: 0.49999997, y: -1.1428572}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 177
      y: 78
      width: 58
      height: 56
    spriteID: 11ad7e28da1abb94d910358321b1ca1a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 177, y: 78}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3485600677
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Pawn_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: 90c91773a3a5d5b4d87ad5ff8d038f09
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 59
      spriteId: c3e14eb7b1d8ff343932b027e3929486
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 57
      spriteId: 3a254a23260faf5418a6f3ed0d82c3ba
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 56
      spriteId: d73b4b6c8dc5d174589475994fb94719
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: 863f4326135b46d4692a4af8c5192a8f
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: 6c34b6d88dc4dcf48baa884e63c83844
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: 82ccd2136259d2e46b52458e58fae52e
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: 8b062543e6f6ca84692bd749e26d0c26
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: ebc5afada45482e488eb2b3015cdaef1
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: 0625884019f5475408d4dd5001431430
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 62
        height: 60
      spriteId: 423ec128ef1a7e647bc20c3ec7346611
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 59
        height: 59
      spriteId: 4212dc89e31b18b458a362b32b14229e
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 55
      spriteId: da97ebae91901a3419bf012a53f52bac
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 65
        height: 75
      spriteId: 558bfe1c4a59acc4d9565f29e68a61f2
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 1ddb86d20111e89438c33e2fab3bd309
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 65
        height: 77
      spriteId: 89ac835b257309840a696c817127855a
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 64
        y: 64
        width: 92
        height: 66
      spriteId: ddaf2365f0ab2b348ba1891429ed1dcb
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 89
        height: 56
      spriteId: c018507f87de9b94899bb8de88acbde5
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 70
        y: 64
        width: 76
        height: 58
      spriteId: 7bd0d8b7ec730144a9454e4603730c8b
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 64
        width: 81
        height: 60
      spriteId: b83d2420b125d304bb09ea9e2ac61c2c
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: 4a61a1915a3de2040bc76bc765ea5405
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 64
        width: 88
        height: 64
      spriteId: a53571ba49785d840bb0037b4fc40fd8
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 64
        width: 110
        height: 54
      spriteId: 0fa38d647042651468c989c2ee0b0320
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 63
        y: 64
        width: 99
        height: 56
      spriteId: 7e7f955c877bd2d45a7e0e84b833d270
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 56
        width: 69
        height: 66
      spriteId: 548fbabf4330c07469378e3ddd239f70
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 60
      spriteId: b081a1fac90327642a4d515f5c0d70d7
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 59
      spriteId: d8b19322115b2a242901284e640963e1
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 58
      spriteId: c130c510abaa7d447931535f7121cfa4
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 57
      spriteId: aa11ac84e6c75c249804319e44220d44
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 58
      spriteId: fb382b4516a4b4c4abab2eba5ddfe767
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 59
      spriteId: 0ace934e4893a8244a630fcd8a27a91e
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 64
        width: 58
        height: 60
      spriteId: f0bbf32f8c7a3834daa6dac46295c13b
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 66
        y: 64
        width: 58
        height: 60
      spriteId: 1d5180ecf134ceb4ea4d8757270d092e
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: c8911833ca1297d44af9163bf0b0fc88
    - name: Frame_34
      frameIndex: 34
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 64
        width: 58
        height: 60
      spriteId: 6464367239301bd4180b83ff900a6efe
    - name: Frame_35
      frameIndex: 35
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 64
        width: 58
        height: 60
      spriteId: ea94c555fa103fb4db068e17fe140589
    - name: Frame_36
      frameIndex: 36
      additiveSortOrder: 0
      cellRect:
        x: 67
        y: 64
        width: 58
        height: 56
      spriteId: 11ad7e28da1abb94d910358321b1ca1a
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

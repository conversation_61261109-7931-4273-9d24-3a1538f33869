fileFormatVersion: 2
guid: f3800f440d126384ab17251a918e107d
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4809932544684580446
    second: Warrior_Red_0
  - first:
      213: 2543577689735042842
    second: Warrior_Red_1
  - first:
      213: 3443903036709564267
    second: Warrior_Red_2
  - first:
      213: 2419534834386294328
    second: Warrior_Red_3
  - first:
      213: 1043457302635204528
    second: Warrior_Red_4
  - first:
      213: -1958050581624333892
    second: Warrior_Red_5
  - first:
      213: 3530665314724634731
    second: Warrior_Red_6
  - first:
      213: 4193390506910184895
    second: Warrior_Red_7
  - first:
      213: -955471921561173437
    second: Warrior_Red_8
  - first:
      213: -7399781881338987461
    second: Warrior_Red_9
  - first:
      213: 6054411078291894343
    second: <PERSON>_<PERSON>_10
  - first:
      213: 8117055562494315405
    second: Warrior_Red_11
  - first:
      213: 9083920789369100618
    second: Warrior_Red_12
  - first:
      213: 6651091410808983154
    second: Warrior_Red_13
  - first:
      213: 705432133387022241
    second: Warrior_Red_14
  - first:
      213: -48003770214472717
    second: Warrior_Red_15
  - first:
      213: -7182564911176842938
    second: Warrior_Red_16
  - first:
      213: -8386167369878401206
    second: Warrior_Red_17
  - first:
      213: -643125589360662469
    second: Warrior_Red_18
  - first:
      213: -976986256928584092
    second: Warrior_Red_19
  - first:
      213: -2073025265035992272
    second: Warrior_Red_20
  - first:
      213: 7811779838078870718
    second: Warrior_Red_21
  - first:
      213: 4230333427372617479
    second: Warrior_Red_22
  - first:
      213: 8198125951179468388
    second: Warrior_Red_23
  - first:
      213: -1461406440695545633
    second: Warrior_Red_24
  - first:
      213: -1745663802244427199
    second: Warrior_Red_25
  - first:
      213: -7371467270822962544
    second: Warrior_Red_26
  - first:
      213: 8174127140272442543
    second: Warrior_Red_27
  - first:
      213: -8027030631863517758
    second: Warrior_Red_28
  - first:
      213: -7354539712258827513
    second: Warrior_Red_29
  - first:
      213: 6698726510534790426
    second: Warrior_Red_30
  - first:
      213: -3713856414385382096
    second: Warrior_Red_31
  - first:
      213: -3174078297123460466
    second: Warrior_Red_32
  - first:
      213: 4559795227268679481
    second: Warrior_Red_33
  - first:
      213: 6339360179002418142
    second: Warrior_Red_34
  - first:
      213: 90204345982682181
    second: Warrior_Red_35
  - first:
      213: 4223254876899549954
    second: Warrior_Red_36
  - first:
      213: -518190383419624302
    second: Warrior_Red_37
  - first:
      213: -917324717665945996
    second: Warrior_Red_38
  - first:
      213: -2584062036897194556
    second: Warrior_Red_39
  - first:
      213: -6203128383950665113
    second: Warrior_Red_40
  - first:
      213: -569740365149124069
    second: Warrior_Red_41
  - first:
      213: -1032199425804383132
    second: Warrior_Red_42
  - first:
      213: -2984276950222658561
    second: Warrior_Red_43
  - first:
      213: -6662886868624952826
    second: Warrior_Red_44
  - first:
      213: -4022881930129185016
    second: Warrior_Red_45
  - first:
      213: 8570803689666417814
    second: Warrior_Red_46
  - first:
      213: 5030418065673244633
    second: Warrior_Red_47
  - first:
      213: 2985711894977786574
    second: Warrior_Red_48
  - first:
      213: 6162922046130181995
    second: Warrior_Red_49
  - first:
      213: 6404622437958972735
    second: Warrior_Red_50
  - first:
      213: -6345331850428766135
    second: Warrior_Red_51
  - first:
      213: -8516953055057146945
    second: Warrior_Red_52
  - first:
      213: -5263704060446539546
    second: Warrior_Red_53
  - first:
      213: 4562183307929203273
    second: Warrior_Red_54
  - first:
      213: -4914822809202561747
    second: Warrior_Red_55
  - first:
      213: -6879101894109772054
    second: Warrior_Red_56
  - first:
      213: -5456099667398793613
    second: Warrior_Red_57
  - first:
      213: -6897297615037311661
    second: Warrior_Red_58
  - first:
      213: 5534061030980101819
    second: Warrior_Red_59
  - first:
      213: 7580511790285436758
    second: Warrior_Red_60
  - first:
      213: -950890636958488530
    second: Warrior_Red_61
  - first:
      213: -6843772968834383216
    second: Warrior_Red_62
  - first:
      213: 5613473180765782868
    second: Warrior_Red_63
  - first:
      213: 1582027097684769398
    second: Warrior_Red_64
  - first:
      213: 2812819105305777335
    second: Warrior_Red_65
  - first:
      213: -7463169048541444249
    second: Warrior_Red_66
  - first:
      213: -9158136619964262989
    second: Warrior_Red_67
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_Red_0
      rect:
        serializedVersion: 2
        x: 62
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5ed38f4a2050c240800000000000000
      internalID: 4809932544684580446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_1
      rect:
        serializedVersion: 2
        x: 254
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a17144fae6a9c4320800000000000000
      internalID: 2543577689735042842
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_2
      rect:
        serializedVersion: 2
        x: 445
        y: 1399
        width: 82
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6f66f7b0933bcf20800000000000000
      internalID: 3443903036709564267
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_3
      rect:
        serializedVersion: 2
        x: 637
        y: 1399
        width: 82
        height: 89
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8365745dc1ae39120800000000000000
      internalID: 2419534834386294328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_4
      rect:
        serializedVersion: 2
        x: 830
        y: 1399
        width: 80
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bfcbd563ea1b7e00800000000000000
      internalID: 1043457302635204528
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_5
      rect:
        serializedVersion: 2
        x: 1022
        y: 1399
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb9d806ce5b93d4e0800000000000000
      internalID: -1958050581624333892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_6
      rect:
        serializedVersion: 2
        x: 70
        y: 1207
        width: 62
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6064f0c5617ff030800000000000000
      internalID: 3530665314724634731
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_7
      rect:
        serializedVersion: 2
        x: 259
        y: 1207
        width: 71
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb570e0d27ae13a30800000000000000
      internalID: 4193390506910184895
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_8
      rect:
        serializedVersion: 2
        x: 447
        y: 1207
        width: 80
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3428c72895b7db2f0800000000000000
      internalID: -955471921561173437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_9
      rect:
        serializedVersion: 2
        x: 629
        y: 1207
        width: 64
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b3cfa6ba591be4990800000000000000
      internalID: -7399781881338987461
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_10
      rect:
        serializedVersion: 2
        x: 694
        y: 1232
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74037cd4cc6950450800000000000000
      internalID: 6054411078291894343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_11
      rect:
        serializedVersion: 2
        x: 824
        y: 1207
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8b0590370395a070800000000000000
      internalID: 8117055562494315405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_12
      rect:
        serializedVersion: 2
        x: 883
        y: 1227
        width: 32
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a49912a44d1901e70800000000000000
      internalID: 9083920789369100618
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_13
      rect:
        serializedVersion: 2
        x: 1022
        y: 1207
        width: 81
        height: 82
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27ecbed266c6d4c50800000000000000
      internalID: 6651091410808983154
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_14
      rect:
        serializedVersion: 2
        x: 57
        y: 1015
        width: 73
        height: 110
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1afedaba9c23ac900800000000000000
      internalID: 705432133387022241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_15
      rect:
        serializedVersion: 2
        x: 248
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f3ac7843d4755ff0800000000000000
      internalID: -48003770214472717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_16
      rect:
        serializedVersion: 2
        x: 296
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6418e15a447625c90800000000000000
      internalID: -7182564911176842938
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_17
      rect:
        serializedVersion: 2
        x: 440
        y: 1015
        width: 64
        height: 99
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4f0d6b75395e9b80800000000000000
      internalID: -8386167369878401206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_18
      rect:
        serializedVersion: 2
        x: 488
        y: 1067
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b38a1341fa82317f0800000000000000
      internalID: -643125589360662469
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_19
      rect:
        serializedVersion: 2
        x: 651
        y: 1015
        width: 53
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46e4555fc2c0172f0800000000000000
      internalID: -976986256928584092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_20
      rect:
        serializedVersion: 2
        x: 696
        y: 1014
        width: 64
        height: 114
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 033ae1b31822b33e0800000000000000
      internalID: -2073025265035992272
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_21
      rect:
        serializedVersion: 2
        x: 841
        y: 1012
        width: 109
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb4ef38cf54096c60800000000000000
      internalID: 7811779838078870718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_22
      rect:
        serializedVersion: 2
        x: 1026
        y: 1015
        width: 102
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70f6104c6d925ba30800000000000000
      internalID: 4230333427372617479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_23
      rect:
        serializedVersion: 2
        x: 33
        y: 823
        width: 84
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46ea6f7fd1895c170800000000000000
      internalID: 8198125951179468388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_24
      rect:
        serializedVersion: 2
        x: 223
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd0cfd5c59a08bbe0800000000000000
      internalID: -1461406440695545633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_25
      rect:
        serializedVersion: 2
        x: 415
        y: 823
        width: 86
        height: 91
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14296f1550826c7e0800000000000000
      internalID: -1745663802244427199
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_26
      rect:
        serializedVersion: 2
        x: 633
        y: 813
        width: 125
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09add29a29943b990800000000000000
      internalID: -7371467270822962544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_27
      rect:
        serializedVersion: 2
        x: 827
        y: 823
        width: 68
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fac81d06355507170800000000000000
      internalID: 8174127140272442543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_28
      rect:
        serializedVersion: 2
        x: 896
        y: 832
        width: 55
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2cd789a5b224a9090800000000000000
      internalID: -8027030631863517758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_29
      rect:
        serializedVersion: 2
        x: 904
        y: 824
        width: 16
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70b1f44091d6fe990800000000000000
      internalID: -7354539712258827513
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_30
      rect:
        serializedVersion: 2
        x: 1021
        y: 823
        width: 87
        height: 90
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1d0beb1548a6fc50800000000000000
      internalID: 6698726510534790426
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_31
      rect:
        serializedVersion: 2
        x: 57
        y: 631
        width: 80
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03dd0c9944bb57cc0800000000000000
      internalID: -3713856414385382096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_32
      rect:
        serializedVersion: 2
        x: 246
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8aeda21b9863f3d0800000000000000
      internalID: -3174078297123460466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_33
      rect:
        serializedVersion: 2
        x: 306
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9370fd1b995a74f30800000000000000
      internalID: 4559795227268679481
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_34
      rect:
        serializedVersion: 2
        x: 438
        y: 631
        width: 66
        height: 105
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed322b4de7ee9f750800000000000000
      internalID: 6339360179002418142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_35
      rect:
        serializedVersion: 2
        x: 498
        y: 674
        width: 25
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5448e7fef58704100800000000000000
      internalID: 90204345982682181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_36
      rect:
        serializedVersion: 2
        x: 621
        y: 588
        width: 120
        height: 139
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 203a88d4fe30c9a30800000000000000
      internalID: 4223254876899549954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_37
      rect:
        serializedVersion: 2
        x: 808
        y: 600
        width: 91
        height: 126
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 294ff3e77940fc8f0800000000000000
      internalID: -518190383419624302
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_38
      rect:
        serializedVersion: 2
        x: 1013
        y: 627
        width: 68
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 476f5db87020543f0800000000000000
      internalID: -917324717665945996
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_39
      rect:
        serializedVersion: 2
        x: 35
        y: 439
        width: 82
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cd9b25b441932cd0800000000000000
      internalID: -2584062036897194556
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_40
      rect:
        serializedVersion: 2
        x: 226
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76a4a72b99f0ae9a0800000000000000
      internalID: -6203128383950665113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_41
      rect:
        serializedVersion: 2
        x: 418
        y: 439
        width: 83
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1ac5111820e718f0800000000000000
      internalID: -569740365149124069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_42
      rect:
        serializedVersion: 2
        x: 614
        y: 411
        width: 148
        height: 119
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46c419f4714eca1f0800000000000000
      internalID: -1032199425804383132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_43
      rect:
        serializedVersion: 2
        x: 835
        y: 414
        width: 118
        height: 117
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff3a796dce7b596d0800000000000000
      internalID: -2984276950222658561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_44
      rect:
        serializedVersion: 2
        x: 1021
        y: 439
        width: 61
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 602bef8c1bba883a0800000000000000
      internalID: -6662886868624952826
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_45
      rect:
        serializedVersion: 2
        x: 1075
        y: 454
        width: 47
        height: 45
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 807ef4dd23adb28c0800000000000000
      internalID: -4022881930129185016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_46
      rect:
        serializedVersion: 2
        x: 882
        y: 412
        width: 29
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6949c22bb8c91f670800000000000000
      internalID: 8570803689666417814
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_47
      rect:
        serializedVersion: 2
        x: 74
        y: 247
        width: 64
        height: 101
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9df49241492afc540800000000000000
      internalID: 5030418065673244633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_48
      rect:
        serializedVersion: 2
        x: 264
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec23fc546216f6920800000000000000
      internalID: 2985711894977786574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_49
      rect:
        serializedVersion: 2
        x: 456
        y: 247
        width: 68
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b67530ac2f8178550800000000000000
      internalID: 6162922046130181995
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_50
      rect:
        serializedVersion: 2
        x: 622
        y: 248
        width: 118
        height: 109
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f357a079c2ac1e850800000000000000
      internalID: 6404622437958972735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_51
      rect:
        serializedVersion: 2
        x: 837
        y: 345
        width: 47
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 948a8311d4ad0f7a0800000000000000
      internalID: -6345331850428766135
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_52
      rect:
        serializedVersion: 2
        x: 851
        y: 248
        width: 86
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbb04a83254bdc980800000000000000
      internalID: -8516953055057146945
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_53
      rect:
        serializedVersion: 2
        x: 1035
        y: 247
        width: 81
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e08d4a9b0193f6b0800000000000000
      internalID: -5263704060446539546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_54
      rect:
        serializedVersion: 2
        x: 603
        y: 290
        width: 10
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 942365d0c81205f30800000000000000
      internalID: 4562183307929203273
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_55
      rect:
        serializedVersion: 2
        x: 613
        y: 307
        width: 17
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2d6e73e1ba0bcbb0800000000000000
      internalID: -4914822809202561747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_56
      rect:
        serializedVersion: 2
        x: 18
        y: 270
        width: 59
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae6cedb36458880a0800000000000000
      internalID: -6879101894109772054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_57
      rect:
        serializedVersion: 2
        x: 212
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3766199924a0844b0800000000000000
      internalID: -5456099667398793613
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_58
      rect:
        serializedVersion: 2
        x: 404
        y: 254
        width: 56
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3555d81dc50e740a0800000000000000
      internalID: -6897297615037311661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_59
      rect:
        serializedVersion: 2
        x: 75
        y: 55
        width: 99
        height: 94
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb6a6ecdf2feccc40800000000000000
      internalID: 5534061030980101819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_60
      rect:
        serializedVersion: 2
        x: 267
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 657bf9b5f43633960800000000000000
      internalID: 7580511790285436758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_61
      rect:
        serializedVersion: 2
        x: 459
        y: 55
        width: 101
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2828d3f002cdc2f0800000000000000
      internalID: -950890636958488530
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_62
      rect:
        serializedVersion: 2
        x: 584
        y: 56
        width: 148
        height: 102
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 092c435aeb80601a0800000000000000
      internalID: -6843772968834383216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_63
      rect:
        serializedVersion: 2
        x: 724
        y: 92
        width: 22
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4530abfcd1017ed40800000000000000
      internalID: 5613473180765782868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_64
      rect:
        serializedVersion: 2
        x: 772
        y: 94
        width: 72
        height: 61
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67afa8e634d74f510800000000000000
      internalID: 1582027097684769398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_65
      rect:
        serializedVersion: 2
        x: 826
        y: 56
        width: 74
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b4de25c214290720800000000000000
      internalID: 2812819105305777335
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_66
      rect:
        serializedVersion: 2
        x: 1000
        y: 86
        width: 31
        height: 57
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7674ffaba4f7d6890800000000000000
      internalID: -7463169048541444249
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Red_67
      rect:
        serializedVersion: 2
        x: 1032
        y: 55
        width: 62
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3b5988a1443c7e080800000000000000
      internalID: -9158136619964262989
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Warrior_Red_0: 4809932544684580446
      Warrior_Red_1: 2543577689735042842
      Warrior_Red_10: 6054411078291894343
      Warrior_Red_11: 8117055562494315405
      Warrior_Red_12: 9083920789369100618
      Warrior_Red_13: 6651091410808983154
      Warrior_Red_14: 705432133387022241
      Warrior_Red_15: -48003770214472717
      Warrior_Red_16: -7182564911176842938
      Warrior_Red_17: -8386167369878401206
      Warrior_Red_18: -643125589360662469
      Warrior_Red_19: -976986256928584092
      Warrior_Red_2: 3443903036709564267
      Warrior_Red_20: -2073025265035992272
      Warrior_Red_21: 7811779838078870718
      Warrior_Red_22: 4230333427372617479
      Warrior_Red_23: 8198125951179468388
      Warrior_Red_24: -1461406440695545633
      Warrior_Red_25: -1745663802244427199
      Warrior_Red_26: -7371467270822962544
      Warrior_Red_27: 8174127140272442543
      Warrior_Red_28: -8027030631863517758
      Warrior_Red_29: -7354539712258827513
      Warrior_Red_3: 2419534834386294328
      Warrior_Red_30: 6698726510534790426
      Warrior_Red_31: -3713856414385382096
      Warrior_Red_32: -3174078297123460466
      Warrior_Red_33: 4559795227268679481
      Warrior_Red_34: 6339360179002418142
      Warrior_Red_35: 90204345982682181
      Warrior_Red_36: 4223254876899549954
      Warrior_Red_37: -518190383419624302
      Warrior_Red_38: -917324717665945996
      Warrior_Red_39: -2584062036897194556
      Warrior_Red_4: 1043457302635204528
      Warrior_Red_40: -6203128383950665113
      Warrior_Red_41: -569740365149124069
      Warrior_Red_42: -1032199425804383132
      Warrior_Red_43: -2984276950222658561
      Warrior_Red_44: -6662886868624952826
      Warrior_Red_45: -4022881930129185016
      Warrior_Red_46: 8570803689666417814
      Warrior_Red_47: 5030418065673244633
      Warrior_Red_48: 2985711894977786574
      Warrior_Red_49: 6162922046130181995
      Warrior_Red_5: -1958050581624333892
      Warrior_Red_50: 6404622437958972735
      Warrior_Red_51: -6345331850428766135
      Warrior_Red_52: -8516953055057146945
      Warrior_Red_53: -5263704060446539546
      Warrior_Red_54: 4562183307929203273
      Warrior_Red_55: -4914822809202561747
      Warrior_Red_56: -6879101894109772054
      Warrior_Red_57: -5456099667398793613
      Warrior_Red_58: -6897297615037311661
      Warrior_Red_59: 5534061030980101819
      Warrior_Red_6: 3530665314724634731
      Warrior_Red_60: 7580511790285436758
      Warrior_Red_61: -950890636958488530
      Warrior_Red_62: -6843772968834383216
      Warrior_Red_63: 5613473180765782868
      Warrior_Red_64: 1582027097684769398
      Warrior_Red_65: 2812819105305777335
      Warrior_Red_66: -7463169048541444249
      Warrior_Red_67: -9158136619964262989
      Warrior_Red_7: 4193390506910184895
      Warrior_Red_8: -955471921561173437
      Warrior_Red_9: -7399781881338987461
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

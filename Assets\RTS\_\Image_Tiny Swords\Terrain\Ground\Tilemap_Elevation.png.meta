fileFormatVersion: 2
guid: 19258c62332a31e48863758890e9620c
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1913826389900762773
    second: Tilemap_Elevation_0
  - first:
      213: -3729094827456700806
    second: Tilemap_Elevation_1
  - first:
      213: -7266184672885777087
    second: Tilemap_Elevation_2
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Tilemap_Elevation_0
      rect:
        serializedVersion: 2
        x: 0
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b61623cc909b075e0800000000000000
      internalID: -1913826389900762773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_1
      rect:
        serializedVersion: 2
        x: 64
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7e52b313089f3cc0800000000000000
      internalID: -3729094827456700806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_2
      rect:
        serializedVersion: 2
        x: 128
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14d5144b983592b90800000000000000
      internalID: -7266184672885777087
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_3
      rect:
        serializedVersion: 2
        x: 192
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 52db78a333f4c174994f138cade06dd6
      internalID: 909393741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_4
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24d9b4488f9028d41b319df8d8a17126
      internalID: 1503396610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_5
      rect:
        serializedVersion: 2
        x: 64
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 927eade2659f7e14c9162493faf33421
      internalID: -385345904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_6
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0c0e78921c9df8d4383a827a072fb766
      internalID: 1629220294
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_7
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0bcf12e965142894da3d2092ca16598c
      internalID: 1667375819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_8
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1253a983601c4594a8918c04e8c3000d
      internalID: -562948113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_9
      rect:
        serializedVersion: 2
        x: 64
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1ef5316e8c6ddb43879475cab4458a7
      internalID: -306190689
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_10
      rect:
        serializedVersion: 2
        x: 128
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c10f8b81f0ecdf44bbc52a78674a8280
      internalID: -484212870
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_11
      rect:
        serializedVersion: 2
        x: 192
        y: 320
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 010f04a8cd64c8141a4dde08bb141cb4
      internalID: -689272030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_12
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5feb3cb151e183c47a6950c26e213744
      internalID: -1861485583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_13
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d273c0be1c79a7428506197c49c969a
      internalID: -1410968127
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_14
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 60c352b4b08185643a7190dce97e1676
      internalID: -2114271836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_15
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da7c23427375ba941a8287723c6db4f1
      internalID: 371131736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_16
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb94c7474c7c3464eb2ee2ba08288871
      internalID: -1075855411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_17
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e9bef649f95173943940e460b4a70858
      internalID: -638985323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_18
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ac64dc2ec8c9474495adb00875df1c9
      internalID: -2087620566
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_19
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7cd21b6fe884eb946a1c706fbc6fda0b
      internalID: 1245369216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_20
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd4aa4f748c1b754e9bcd2e934a05055
      internalID: -692332750
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_21
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8e142735c729f634ab6124356421318c
      internalID: 1016921664
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_22
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ff6c2c536f3aae4693f808d45dc0183
      internalID: 1879449496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_23
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3b1a6d3efad565047b4679ed26edbb08
      internalID: -1537111231
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_24
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38748cf58b14e924dbb0a8c3e125a5c3
      internalID: -2111132856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_25
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2512013d36d79f147904db85d42edb82
      internalID: 1915917175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_26
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c29462ee7c6814048982fef2ce1703ef
      internalID: 1034740503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tilemap_Elevation_27
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3ea3223b49321de479346f9f0beab861
      internalID: -2142259888
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 7fabf68fba9997d44af7cd121b3f1465
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Tilemap_Elevation_0: -1913826389900762773
      Tilemap_Elevation_1: -3729094827456700806
      Tilemap_Elevation_10: -484212870
      Tilemap_Elevation_11: -689272030
      Tilemap_Elevation_12: -1861485583
      Tilemap_Elevation_13: -1410968127
      Tilemap_Elevation_14: -2114271836
      Tilemap_Elevation_15: 371131736
      Tilemap_Elevation_16: -1075855411
      Tilemap_Elevation_17: -638985323
      Tilemap_Elevation_18: -2087620566
      Tilemap_Elevation_19: 1245369216
      Tilemap_Elevation_2: -7266184672885777087
      Tilemap_Elevation_20: -692332750
      Tilemap_Elevation_21: 1016921664
      Tilemap_Elevation_22: 1879449496
      Tilemap_Elevation_23: -1537111231
      Tilemap_Elevation_24: -2111132856
      Tilemap_Elevation_25: 1915917175
      Tilemap_Elevation_26: 1034740503
      Tilemap_Elevation_27: -2142259888
      Tilemap_Elevation_3: 909393741
      Tilemap_Elevation_4: 1503396610
      Tilemap_Elevation_5: -385345904
      Tilemap_Elevation_6: 1629220294
      Tilemap_Elevation_7: 1667375819
      Tilemap_Elevation_8: -562948113
      Tilemap_Elevation_9: -306190689
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 

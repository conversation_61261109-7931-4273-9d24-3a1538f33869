
using UnityEngine;

[CreateAssetMenu(fileName = "BuildAction", menuName = "RTS/Actions/BuildAction")]
/// <summary>
/// يمثل إجراء بناء هيكل في اللعبة، ويحتوي على جميع المعلومات اللازمة لعملية البناء مثل النماذج الأولية والتكلفة ووقت البناء.
/// </summary>
/// <remarks>
/// يرث من: ActionSO
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class BuildActionSO : ActionSO
{
    [SerializeField] private StructureUnit m_StructurePrefab;
    [SerializeField] private float m_ConstructionTime;
    [SerializeField] private Sprite m_PlacementSprite;
    [SerializeField] private Sprite m_FoundationSprite;
    [SerializeField] private Sprite m_CompletionSprite;

    [SerializeField] private Vector3Int m_BuildingSize;
    [SerializeField] private Vector3Int m_OriginOffset;

    [SerializeField] private int m_GoldCost;
    [SerializeField] private int m_WoodCost;





    public StructureUnit StructurePrefab => m_StructurePrefab;
    public float ConstructionTime => m_ConstructionTime;
    public Sprite PlacementSprite => m_PlacementSprite;
    public Sprite FoundationSprite => m_FoundationSprite;
    public Sprite CompletionSprite => m_CompletionSprite;

    public Vector3Int BuildingSize => m_BuildingSize;
    public Vector3Int OriginOffset => m_OriginOffset;

    public int GoldCost => m_GoldCost;
    public int WoodCost => m_WoodCost;



    /// <summary>
    /// تنفذ هذه الدالة الإجراء المرتبط بهذا الكائن، وهي جزء من واجهة الإجراءات في اللعبة.
    /// </summary>
    /// <param name="gameManager">معامل من نوع GameManager يستخدم في الدالة لتحديد gameManager.</param>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    public override void Execute(GameManager gameManager)
    {
        gameManager.StartBuildProcess(this);
    }













}//end class
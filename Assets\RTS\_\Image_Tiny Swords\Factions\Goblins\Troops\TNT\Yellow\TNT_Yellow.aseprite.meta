fileFormatVersion: 2
guid: f7a7113ba91642e43bc4cf20560e7515
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.44705883, y: -0.826087}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 85
      height: 69
    spriteID: b5f826e250810f648aff825e3a00083a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.44186047, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 4
      width: 86
      height: 68
    spriteID: a954d3da7166b4a4f8082906338db795
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 4}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.44578314, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 380
      y: 4
      width: 83
      height: 68
    spriteID: cc47c382b906840449e1c6cf61440b94
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 380, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.43373492, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 88
      y: 81
      width: 83
      height: 68
    spriteID: 6114071148570de48a2818d39d2af3aa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 88, y: 81}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.4302326, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 286
      y: 4
      width: 86
      height: 66
    spriteID: cff164e9cd9807b45812d7cc70e991c3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 286, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.4534884, y: -0.8769231}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 179
      y: 81
      width: 86
      height: 65
    spriteID: 6803fabf53f913949949fb966c814d87
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 179, y: 81}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.44827586, y: -0.8636364}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 191
      y: 4
      width: 87
      height: 66
    spriteID: 01fbecf83d42697459f2bcbf1ce360f2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 191, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.45000002, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 97
      y: 164
      width: 80
      height: 64
    spriteID: c63f3f71be0b12d48ba2e11461e86204
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 97, y: 164}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.4375, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 236
      width: 80
      height: 67
    spriteID: 57f96a9101e1da641bbb282ffd0ae9eb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 236}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.43373492, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 185
      y: 164
      width: 83
      height: 60
    spriteID: 7efe35ad06cd38e42bbc5f6a5d4de1a3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 185, y: 164}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.44705883, y: -0.921875}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 85
      height: 64
    spriteID: 3309d9bfdafa9ee488f4fba2ff1de771
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47560975, y: -0.880597}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 358
      y: 81
      width: 82
      height: 67
    spriteID: 2d386c1bf3b8b214289c9fa33f57cc00
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 358, y: 81}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.475, y: -0.9833333}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 276
      y: 164
      width: 80
      height: 60
    spriteID: f5596cc5bf8f32e4689fcb8860f26c8e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 276, y: 164}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5324675, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 273
      y: 81
      width: 77
      height: 72
    spriteID: 078d74f49b7eac14884066144f396507
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 273, y: 81}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.59210527, y: -0.76}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 81
      width: 76
      height: 75
    spriteID: 81b08589721d35b419505a24a4e8a80f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 81}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.53333336, y: -0.95}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 231
      y: 311
      width: 75
      height: 60
    spriteID: 00e1580f6ac76a54bb2ba58931ff2b7f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 231, y: 311}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.51351345, y: -0.9047619}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 364
      y: 164
      width: 74
      height: 63
    spriteID: 4e9b64a97a8acb2468cfd4446d87997a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 364, y: 164}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.53424656, y: -0.8382353}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 76
      y: 311
      width: 73
      height: 68
    spriteID: 0fcb6371269eac04f9031ec52d80ec8e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 76, y: 311}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.640625, y: -0.7037037}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 311
      width: 64
      height: 81
    spriteID: fce350678cde9854990b4b7f387fdbbf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 311}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5606061, y: -0.7916667}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 157
      y: 311
      width: 66
      height: 72
    spriteID: eb7d3e3ac1902d044b0dc8a0a0e6d55d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 157, y: 311}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 481
      y: 164
      width: 27
      height: 51
    spriteID: 9c000e65bdaeec548a40dc9e9265f769
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 481, y: 164}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.44444445, y: -1.4423077}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 446
      y: 164
      width: 27
      height: 52
    spriteID: b3e3af617e4344141b89e7477d83ff9b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 446, y: 164}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.46153846, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 314
      y: 311
      width: 26
      height: 51
    spriteID: ef00714990321164c97e25bc07a0ff53
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 314, y: 311}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.48, y: -1.5}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 483
      y: 81
      width: 25
      height: 50
    spriteID: ac42171740a9ce248b399267693d45bb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 483, y: 81}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.44444445, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 448
      y: 81
      width: 27
      height: 51
    spriteID: 312af55af6124a541bc2ab026570268f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 448, y: 81}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.42857143, y: -1.4705882}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 471
      y: 4
      width: 28
      height: 51
    spriteID: c32e456bcf5cf4b42af458f6c0187d13
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 471, y: 4}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3737586193
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: TNT_Yellow
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 85
        height: 69
      spriteId: b5f826e250810f648aff825e3a00083a
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 86
        height: 68
      spriteId: a954d3da7166b4a4f8082906338db795
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 83
        height: 68
      spriteId: cc47c382b906840449e1c6cf61440b94
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 57
        width: 83
        height: 68
      spriteId: 6114071148570de48a2818d39d2af3aa
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 86
        height: 66
      spriteId: cff164e9cd9807b45812d7cc70e991c3
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 86
        height: 65
      spriteId: 6803fabf53f913949949fb966c814d87
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 87
        height: 66
      spriteId: 01fbecf83d42697459f2bcbf1ce360f2
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 80
        height: 64
      spriteId: c63f3f71be0b12d48ba2e11461e86204
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 61
        y: 59
        width: 80
        height: 67
      spriteId: 57f96a9101e1da641bbb282ffd0ae9eb
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 60
        y: 59
        width: 83
        height: 60
      spriteId: 7efe35ad06cd38e42bbc5f6a5d4de1a3
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 85
        height: 64
      spriteId: 3309d9bfdafa9ee488f4fba2ff1de771
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 59
        width: 82
        height: 67
      spriteId: 2d386c1bf3b8b214289c9fa33f57cc00
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 59
        width: 80
        height: 60
      spriteId: f5596cc5bf8f32e4689fcb8860f26c8e
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 77
        height: 72
      spriteId: 078d74f49b7eac14884066144f396507
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 57
        width: 76
        height: 75
      spriteId: 81b08589721d35b419505a24a4e8a80f
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 57
        width: 75
        height: 60
      spriteId: 00e1580f6ac76a54bb2ba58931ff2b7f
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 58
        y: 57
        width: 74
        height: 63
      spriteId: 4e9b64a97a8acb2468cfd4446d87997a
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 57
        y: 57
        width: 73
        height: 68
      spriteId: 0fcb6371269eac04f9031ec52d80ec8e
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 57
        width: 64
        height: 81
      spriteId: fce350678cde9854990b4b7f387fdbbf
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 59
        y: 57
        width: 66
        height: 72
      spriteId: eb7d3e3ac1902d044b0dc8a0a0e6d55d
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: 9c000e65bdaeec548a40dc9e9265f769
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 52
      spriteId: b3e3af617e4344141b89e7477d83ff9b
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 26
        height: 51
      spriteId: ef00714990321164c97e25bc07a0ff53
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 25
        height: 50
      spriteId: ac42171740a9ce248b399267693d45bb
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 27
        height: 51
      spriteId: 312af55af6124a541bc2ab026570268f
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 84
        y: 75
        width: 28
        height: 51
      spriteId: c32e456bcf5cf4b42af458f6c0187d13
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 512}

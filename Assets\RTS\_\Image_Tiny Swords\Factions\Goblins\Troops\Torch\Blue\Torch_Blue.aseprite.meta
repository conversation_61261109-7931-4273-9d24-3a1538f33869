fileFormatVersion: 2
guid: 3d24704a08b6db840bcde170784e5796
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5633803, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 420
      width: 71
      height: 97
    spriteID: d333970c276d7374c94ea2ebf372c5c1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 420}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.6233766, y: -0.6082474}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 809
      width: 77
      height: 97
    spriteID: 8ab885a4c56a4564bbaee4b3ccb7864b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 809}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.35999998, y: -0.6020408}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 914
      width: 75
      height: 98
    spriteID: c94fccb95a7987c4f9a8c2002392d3c3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 914}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5810811, y: -0.7468354}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 281
      width: 74
      height: 79
    spriteID: e7672aa7c7bbb564989a3fd4c02ed240
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 281}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.59999996, y: -0.71951216}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 889
      width: 75
      height: 82
    spriteID: 7a2275d00d48d4045937f271e5a9677c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 889}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.59459454, y: -0.7108433}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 4
      width: 74
      height: 83
    spriteID: 1dd3a64b29cd4e34ba7c3c6bf7f9b439
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 4}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5833334, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 189
      width: 72
      height: 84
    spriteID: 98079754853ef484c8c95a7a26b7fa42
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 189}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.57746476, y: -0.68604654}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 381
      y: 95
      width: 71
      height: 86
    spriteID: 8a5e2bcf9bc451041bff652703940892
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 381, y: 95}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.57746476, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 793
      width: 71
      height: 88
    spriteID: 6cc47d86144aa22449c372473be89e8e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 793}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5633803, y: -0.6629213}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 696
      width: 71
      height: 89
    spriteID: 6236a908f5a0fc74ab75b79b911141f9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 696}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.7129629, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 85
      width: 108
      height: 73
    spriteID: 1913871dc4892604889736577e39c891
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 85}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.6868687, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 725
      width: 99
      height: 76
    spriteID: b2e920bf450da574db306983c20386fe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 725}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 166
      width: 105
      height: 73
    spriteID: 37b7d57c4f88fab43961d57cf2b29a04
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 166}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.6936937, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 4
      width: 111
      height: 73
    spriteID: 5d4e241b0107ac345ad5dd103e19a960
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 4}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.68, y: -0.77631575}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 641
      width: 100
      height: 76
    spriteID: 9bdd0038adeadcf49974e59d00e492ff
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 641}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.72380954, y: -0.80821913}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 247
      width: 105
      height: 73
    spriteID: 803594de9892b8a43bd6c68200fe81ca
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 247}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.65, y: -0.6145833}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 537
      width: 80
      height: 96
    spriteID: 2044a00d6acf35640a4203c1861a8a65
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 537}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.6627907, y: -0.57843137}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 229
      width: 86
      height: 102
    spriteID: 52a901a1ea3335748876a84563d9bc28
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 229}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.67777777, y: -0.5412844}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 112
      width: 90
      height: 109
    spriteID: 5c4c680dc1cb0284588e8e56ee8cc1e8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 112}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.4160584, y: -0.54716986}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 118
      width: 137
      height: 106
    spriteID: f52006c8fa582164f93f69e2d446b53e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 118}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.41481486, y: -0.53271025}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 232
      width: 135
      height: 107
    spriteID: 9c0e031adb3c81340b875910a06e7515
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 232}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.43089432, y: -0.5462963}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 585
      width: 123
      height: 108
    spriteID: 0bfc596f517f2f24694f540dccc9b0bf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 585}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.6202531, y: -0.7375}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 608
      width: 79
      height: 80
    spriteID: a8d9e6fb7b204144c95393b0ed4c2d1b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 608}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.6395349, y: -0.70238096}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 328
      width: 86
      height: 84
    spriteID: b29418c8fc574e040a25f0c3d4db6303
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 328}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.65168536, y: -0.67045456}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 441
      width: 89
      height: 88
    spriteID: 7774f6552e3b6d84e823100f8b620137
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 441}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.50862074, y: -0.2566372}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 701
      width: 116
      height: 113
    spriteID: 551876f6beeb51d4d87d52d2053d9445
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 701}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.48739493, y: -0.1965812}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 460
      width: 119
      height: 117
    spriteID: 09ef0882e7f670c47b3ebfd6f92b653e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 460}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.39215687, y: -0.26999998}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 4
      width: 102
      height: 100
    spriteID: 1f7c3a74da48e6c40b061c495ce4ad1c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 4}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.30769235, y: -0.78666663}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 262
      y: 525
      width: 91
      height: 75
    spriteID: b0f139e2dc658d3428010c2682836687
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 262, y: 525}
  - name: Frame_29
    originalName: 
    pivot: {x: 0.32978725, y: -0.4835165}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 923
      width: 94
      height: 91
    spriteID: 269de989387e4414a969844090618ea4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 923}
  - name: Frame_30
    originalName: 
    pivot: {x: 0.34065938, y: -0.43617022}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 152
      y: 339
      width: 91
      height: 94
    spriteID: d8163149cce6f4b4689f8a9b5c41c0c8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 152, y: 339}
  - name: Frame_31
    originalName: 
    pivot: {x: 0.5928571, y: -0.44339624}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 140
      height: 106
    spriteID: 24a090aab7b894e4286359ef417a4308
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_32
    originalName: 
    pivot: {x: 0.6058394, y: -0.4857143}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 347
      width: 137
      height: 105
    spriteID: 12859d6ee0cfdd445841f94edb0d6b9e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 347}
  - name: Frame_33
    originalName: 
    pivot: {x: 0.69827586, y: -0.63440853}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 822
      width: 116
      height: 93
    spriteID: 882eee6c76da0494aa7fed32b311104b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 822}
  spriteSheetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 3747220631
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Torch_Blue
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 97
      spriteId: d333970c276d7374c94ea2ebf372c5c1
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 48
        y: 59
        width: 77
        height: 97
      spriteId: 8ab885a4c56a4564bbaee4b3ccb7864b
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 69
        y: 59
        width: 75
        height: 98
      spriteId: c94fccb95a7987c4f9a8c2002392d3c3
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 53
        y: 59
        width: 74
        height: 79
      spriteId: e7672aa7c7bbb564989a3fd4c02ed240
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 51
        y: 59
        width: 75
        height: 82
      spriteId: 7a2275d00d48d4045937f271e5a9677c
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 52
        y: 59
        width: 74
        height: 83
      spriteId: 1dd3a64b29cd4e34ba7c3c6bf7f9b439
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 54
        y: 59
        width: 72
        height: 84
      spriteId: 98079754853ef484c8c95a7a26b7fa42
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 86
      spriteId: 8a5e2bcf9bc451041bff652703940892
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 55
        y: 59
        width: 71
        height: 88
      spriteId: 6cc47d86144aa22449c372473be89e8e
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 59
        width: 71
        height: 89
      spriteId: 6236a908f5a0fc74ab75b79b911141f9
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 108
        height: 73
      spriteId: 1913871dc4892604889736577e39c891
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 99
        height: 76
      spriteId: b2e920bf450da574db306983c20386fe
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: 37b7d57c4f88fab43961d57cf2b29a04
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 19
        y: 59
        width: 111
        height: 73
      spriteId: 5d4e241b0107ac345ad5dd103e19a960
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 28
        y: 59
        width: 100
        height: 76
      spriteId: 9bdd0038adeadcf49974e59d00e492ff
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 20
        y: 59
        width: 105
        height: 73
      spriteId: 803594de9892b8a43bd6c68200fe81ca
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 44
        y: 59
        width: 80
        height: 96
      spriteId: 2044a00d6acf35640a4203c1861a8a65
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 59
        width: 86
        height: 102
      spriteId: 52a901a1ea3335748876a84563d9bc28
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 35
        y: 59
        width: 90
        height: 109
      spriteId: 5c4c680dc1cb0284588e8e56ee8cc1e8
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 39
        y: 58
        width: 137
        height: 106
      spriteId: f52006c8fa582164f93f69e2d446b53e
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 40
        y: 57
        width: 135
        height: 107
      spriteId: 9c0e031adb3c81340b875910a06e7515
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 43
        y: 59
        width: 123
        height: 108
      spriteId: 0bfc596f517f2f24694f540dccc9b0bf
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 47
        y: 59
        width: 79
        height: 80
      spriteId: a8d9e6fb7b204144c95393b0ed4c2d1b
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 41
        y: 59
        width: 86
        height: 84
      spriteId: b29418c8fc574e040a25f0c3d4db6303
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 59
        width: 89
        height: 88
      spriteId: 7774f6552e3b6d84e823100f8b620137
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 37
        y: 29
        width: 116
        height: 113
      spriteId: 551876f6beeb51d4d87d52d2053d9445
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 38
        y: 23
        width: 119
        height: 117
      spriteId: 09ef0882e7f670c47b3ebfd6f92b653e
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 56
        y: 27
        width: 102
        height: 100
      spriteId: 1f7c3a74da48e6c40b061c495ce4ad1c
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 68
        y: 59
        width: 91
        height: 75
      spriteId: b0f139e2dc658d3428010c2682836687
    - name: Frame_29
      frameIndex: 29
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 44
        width: 94
        height: 91
      spriteId: 269de989387e4414a969844090618ea4
    - name: Frame_30
      frameIndex: 30
      additiveSortOrder: 0
      cellRect:
        x: 65
        y: 41
        width: 91
        height: 94
      spriteId: d8163149cce6f4b4689f8a9b5c41c0c8
    - name: Frame_31
      frameIndex: 31
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 47
        width: 140
        height: 106
      spriteId: 24a090aab7b894e4286359ef417a4308
    - name: Frame_32
      frameIndex: 32
      additiveSortOrder: 0
      cellRect:
        x: 13
        y: 51
        width: 137
        height: 105
      spriteId: 12859d6ee0cfdd445841f94edb0d6b9e
    - name: Frame_33
      frameIndex: 33
      additiveSortOrder: 0
      cellRect:
        x: 15
        y: 59
        width: 116
        height: 93
      spriteId: 882eee6c76da0494aa7fed32b311104b
    linkedCells: []
    parentIndex: -1
  platformSettings:
  - name: Default
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 192, y: 192}
  previousTextureSize: {x: 512, y: 1024}

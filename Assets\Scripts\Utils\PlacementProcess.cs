
using UnityEngine;
using UnityEngine.Tilemaps;

/// <summary>
/// يدير عملية وضع الهياكل في اللعبة، بما في ذلك التحقق من صحة الموقع. يتعامل مع معاينة البناء والتحقق من الصلاحية قبل البناء الفعلي.
/// </summary>
/// <remarks>
/// يرث من: None
/// يتعامل مع الوظائف الأساسية المرتبطة بهذا النوع من الكائنات في اللعبة.
/// </remarks>
public class PlacementProcess
{
    private GameObject m_PlacementOutline;
    private BuildActionSO m_BuildActionSO;

    private Vector3Int[] m_HighlightTilesPosition;

    private Sprite m_PlaceHolderTileSprite;

    private TilemapManager m_TilemapManager;

    private Color m_HighlightColor = new(0, 0.8f, 1, 0.4f);
    private Color m_BlockedColor = new(1, 0.4f, 0.4f, 0.9f);

    public BuildActionSO BuildAction => m_BuildActionSO;
    public int GoldCost => m_BuildActionSO.GoldCost;
    public int WoodCost => m_BuildActionSO.WoodCost;

    public PlacementProcess(BuildActionSO buildAction, TilemapManager tilemapManager)
    {
        m_PlaceHolderTileSprite = Resources.Load<Sprite>("Images/PlaceHolderTileSprite");
        m_BuildActionSO = buildAction;
        m_TilemapManager = tilemapManager;
    }

    public void Update()
    {
        if (m_PlacementOutline != null)
        {
            HighlightTiles(m_PlacementOutline.transform.position);
        }

        if (RTSUtils.IsPointerOverUIElement()) return;

        if (RTSUtils.TryGetHoldPosition(out Vector3 worldPosition))
        {
            m_PlacementOutline.transform.position = SnapToGrid(worldPosition);
        }
    }

    public void CleanupPlacement()
    {
        Object.Destroy(m_PlacementOutline);
        ClearHighlight();
    }

    public bool TryFinalizePlacement(out Vector3 buildPosition)
    {
        if (IsPlacementAreaValid())
        {
            ClearHighlight();
            buildPosition = m_PlacementOutline.transform.position;
            Object.Destroy(m_PlacementOutline);
            /// <summary>
            /// متغير من نوع return يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
            /// </summary>
            return true;
        }

        Debug.Log("Invalid area");
        buildPosition = Vector3.zero;
        /// <summary>
        /// متغير من نوع return يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        return false;
    }

    /// <summary>
    /// تنفذ دالة IsPlacementAreaValid وظيفة محددة في كلاس PlacementProcess.
    /// </summary>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    bool IsPlacementAreaValid()
    {
        foreach (var tilePosition in m_HighlightTilesPosition)
        {
            if (!m_TilemapManager.CanPlaceTile(tilePosition)) return false;
        }

        /// <summary>
        /// متغير من نوع return يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        return true;
    }

    public void ShowPlacementOutline()
    {
        m_PlacementOutline = new GameObject("PlacementOutline");
        /// <summary>
        /// متغير من نوع var يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        var renderer = m_PlacementOutline.AddComponent<SpriteRenderer>();
        renderer.sortingOrder = 999;

        //TODO:: change sorting layerName
        renderer.sortingLayerName = "Enemy";
        renderer.color = new Color(1, 1, 1, .5f);
        renderer.sprite = m_BuildActionSO.PlacementSprite;
    }

    /// <summary>
    /// تنفذ دالة SnapToGrid وظيفة محددة في كلاس PlacementProcess.
    /// </summary>
    /// <param name="worldPosition">معامل من نوع Vector3 يستخدم في الدالة لتحديد worldPosition.</param>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    Vector3 SnapToGrid(Vector3 worldPosition)
    {
        return new Vector3(Mathf.FloorToInt(worldPosition.x), Mathf.FloorToInt(worldPosition.y), 0);
    }


    /// <summary>
    /// تنفذ دالة HighlightTiles وظيفة محددة في كلاس PlacementProcess.
    /// </summary>
    /// <param name="outlinePosition">معامل من نوع Vector3 يستخدم في الدالة لتحديد outlinePosition.</param>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    void HighlightTiles(Vector3 outlinePosition)
    {
        /// <summary>
        /// متغير من نوع Vector3Int يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        Vector3Int buildingSize = m_BuildActionSO.BuildingSize;
        /// <summary>
        /// متغير من نوع Vector3 يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
        /// </summary>
        Vector3 correctPosition = outlinePosition + m_BuildActionSO.OriginOffset;

        ClearHighlight();

        m_HighlightTilesPosition = new Vector3Int[buildingSize.x * buildingSize.y];


        for (int x = 0; x < buildingSize.x; x++)
        {
            for (int y = 0; y < buildingSize.y; y++)
            {
                m_HighlightTilesPosition[x + y * buildingSize.x] = new Vector3Int((int)correctPosition.x + x, (int)correctPosition.y + y, 0);
            }
        }

        foreach (var tilePosition in m_HighlightTilesPosition)
        {
            /// <summary>
            /// متغير من نوع var يستخدم في كلاس PlacementProcess لتخزين بيانات مرتبطة بوظائف الكلاس.
            /// </summary>
            var tile = ScriptableObject.CreateInstance<Tile>();
            tile.sprite = m_PlaceHolderTileSprite;

            if (m_TilemapManager.CanPlaceTile(tilePosition))
            {
                tile.color = m_HighlightColor;
            }
            else
            {
                tile.color = m_BlockedColor;
            }

            m_TilemapManager.SetTileOverlay(tilePosition, tile);
        }

    }


    /// <summary>
    /// تنفذ دالة ClearHighlight وظيفة محددة في كلاس PlacementProcess.
    /// </summary>
    /// <returns>نتيجة تنفيذ الدالة، تختلف حسب الغرض من الدالة.</returns>
    void ClearHighlight()
    {
        if (m_HighlightTilesPosition == null) return;

        foreach (var tilePosition in m_HighlightTilesPosition)
        {
            m_TilemapManager.SetTileOverlay(tilePosition, null);
        }

    }









}// end class



using System;
using UnityEngine;

public class WorkerUnit : HumanoidUnit
{

    #region SerializeFeild
    [SerializeField] private float m_WoodGatherTickTime = 1f;
    [SerializeField] private int m_WoodPerTick = 1;
    [SerializeField] private float m_HitTreeFrecuancy = 0.5f;
    [SerializeField] private SpriteRenderer m_HoldingWoodSprite;
    [SerializeField] private SpriteRenderer m_HoldingGoldSprit;
    #endregion

    #region Private Variable
    private Tree m_AssignedTree;
    private GoldMine m_AssignedGoldMine;
    private StructureUnit m_AssignedWoodStorage;
    private StructureUnit m_AssignedGoldStorage;
    private float m_ChoppingTimer;
    private float m_HitTreeTimer;
    private int m_WoodCollected;
    private int m_GoldCollected;
    private int m_WoodCapacity = 5;
    private int m_GoldCapacity = 10;
    #endregion

    #region Public Variable
    public bool IsHoldingWood => m_WoodCollected > 0;
    public bool IsHoldingGold => m_GoldCollected > 0;
    public bool IsHoldingResource => IsHoldingWood || IsHoldingGold;
    #endregion

    protected override void UpdateBehavior()
    {
        if (CurrentTask == UnitTask.Build && HasTarget)
        {
            CheckForConstruction();
        }
        else if (CurrentTask == UnitTask.Chop && m_AssignedTree != null && m_WoodCollected < m_WoodCapacity)
        {
            HandleChoppingTask();
        }

        else if (CurrentTask == UnitTask.Mine && m_AssignedGoldMine != null && !IsHoldingGold)
        {
            HandleMiningTask();
        }
        else if (CurrentTask == UnitTask.ReturnResource)
        {
            if (IsHoldingWood && TryToReturnAnyResources(m_AssignedWoodStorage, 1.2f))
            {
                m_GameManager.ShowTextPopup(m_WoodCollected.ToString(), GetTopPosition(), Color.green);
                m_GameManager.AddResources(0, m_WoodCollected);
                m_WoodCollected = 0;
                TryMoveToClosestTree();
            }
            else if (IsHoldingGold && TryToReturnAnyResources(m_AssignedGoldStorage, .8f))
            {
                m_GameManager.ShowTextPopup(m_GoldCollected.ToString(), GetTopPosition(), Color.green);
                m_GameManager.AddResources(m_GoldCollected, 0);
                m_GoldCollected = 0;
                TryMoveToClosestGoldMine();
            }
        }
        // return wood resource to storager
        // else if (CurrentTask == UnitTask.ReturnResource && m_AssignedWoodStorage != null && IsHoldingWood)
        // {
        //     var closestPointOnStorage = m_AssignedWoodStorage.Collider.ClosestPoint(transform.position);
        //     var distance = Vector3.Distance(closestPointOnStorage, transform.position);

        //     if (distance <= 1.2f)
        //     {
        //         m_GameManager.ShowTextPopup(m_WoodCollected.ToString(), GetTopPosition(), Color.green);
        //         m_GameManager.AddResources(0, m_WoodCollected);
        //         m_WoodCollected = 0;
        //         TryMoveToClosestTree();
        //     }
        // }
        //  return Gold resource to storager
        // else if (CurrentTask == UnitTask.ReturnResource && m_AssignedGoldStorage != null && IsHoldingGold)
        // {
        //     var closestPointOnStorage = m_AssignedGoldStorage.Collider.ClosestPoint(transform.position);
        //     var distance = Vector3.Distance(closestPointOnStorage, transform.position);

        //     if (distance <= 1.2f)
        //     {
        //         m_GameManager.ShowTextPopup(m_GoldCollected.ToString(), GetTopPosition(), Color.green);
        //         m_GameManager.AddResources(m_GoldCollected, 0);
        //         m_GoldCollected = 0;
        //         TryMoveToClosestGoldMine();
        //     }
        // }

        if (CurrentState == UnitState.Chopping && m_WoodCollected < m_WoodCapacity)
        {
            StartChopping();
        }
        HandleResourceDisplay();
    }

    bool TryToReturnAnyResources(StructureUnit storage, float distanceTreshhold = 0.5f)
    {
        if (storage != null)
        {
            Vector2 closestPointOnStorage = storage.Collider.ClosestPoint(transform.position);
            float distance = Vector3.Distance(closestPointOnStorage, transform.position);
            return distance < distanceTreshhold;
        }

        return false;
    }



    protected override void OnSetDestination(DestinationSource destinationSource)
    {
        if (CurrentState == UnitState.Mining) return;

        SetUnitState(UnitState.Moving);
        ResetState();
    }

    public void SetWoodStorage(StructureUnit woodStorage)
    {
        m_AssignedWoodStorage = woodStorage;
    }
    public void SetGoldStorage(StructureUnit goldStorage)
    {
        m_AssignedGoldStorage = goldStorage;
    }

    public void OnBuildingFinished() => ResetState();

    public void SendToBuild(StructureUnit structure)
    {
        MoveTo(structure.transform.position);
        SetTarget(structure);
        SetUnitTask(UnitTask.Build);
    }

    public void SendToChop(Tree tree)
    {
        if (tree.TryToClaim())
        {
            MoveTo(tree.GetBottomPositionOfCollider());
            SetUnitTask(UnitTask.Chop);
            m_AssignedTree = tree;
        }
    }

    public void SendToMine(GoldMine goldMine)
    {
        // i need to make change for Clime variable on GldMine Class
        MoveTo(goldMine.GetBottomPosition());
        SetUnitTask(UnitTask.Mine);
        m_AssignedGoldMine = goldMine;

    }

    public void OnLeaveMining()
    {
        ShowWorker();
        m_GoldCollected = m_GoldCapacity;

        m_AssignedGoldStorage = m_GameManager.FindClosestGoldStorage(transform.position);


        if (m_AssignedGoldStorage != null)
        {

            MoveTo(m_AssignedGoldStorage.transform.position);
            SetUnitTask(UnitTask.ReturnResource);
        }

        SetUnitState(UnitState.Idle);

    }

    public void OnEnterMining()
    {
        HideWorker();

    }

    protected override void Die()
    {
        base.Die();

        if (m_AssignedTree != null) m_AssignedTree.Release();
    }



    void HandleResourceDisplay()
    {
        if (IsHoldingResource)
        {
            if (IsHoldingGold)
            {
                m_HoldingGoldSprit.gameObject.SetActive(true);
                m_HoldingWoodSprite.gameObject.SetActive(false);
            }
            else
            {
                m_HoldingGoldSprit.gameObject.SetActive(false);
                m_HoldingWoodSprite.gameObject.SetActive(true);
            }
            m_Animator.SetFloat("IsHoldingResource", 1f);
        }
        else
        {
            m_HoldingGoldSprit.gameObject.SetActive(false);
            m_HoldingWoodSprite.gameObject.SetActive(false);
            m_Animator.SetFloat("IsHoldingResource", 0);
        }
    }

    void HandleChoppingTask()
    {
        Vector3 treeBottomPosition = m_AssignedTree.GetBottomPositionOfCollider();
        Vector2 workerClosestPoint = Collider.ClosestPoint(treeBottomPosition);

        float distance = Vector3.Distance(treeBottomPosition, workerClosestPoint);

        if (distance <= 0.1f)
        {
            m_GoldCollected = 0;
            StopMovement();
            SetUnitState(UnitState.Chopping);

        }
    }

    void HandleMiningTask()
    {
        Vector3 goldMineBottomPosition = m_AssignedGoldMine.GetBottomPosition();
        Vector2 workerClosestPoint = Collider.ClosestPoint(goldMineBottomPosition);

        float distance = Vector3.Distance(goldMineBottomPosition, workerClosestPoint);

        if (distance <= 0.2f)
        {
            if (m_AssignedGoldMine.TryToEnterMine(this))
            {
                m_WoodCollected = 0;
                //m_GameManager.CancleActiveUnit();
                StopMovement();
                SetUnitState(UnitState.Mining);
            }

        }
    }

    void StartChopping()
    {
        m_Animator.SetBool("IsChopping", true);
        m_ChoppingTimer += Time.deltaTime;
        m_HitTreeTimer += Time.deltaTime;

        if (m_HitTreeTimer >= m_HitTreeFrecuancy)
        {
            m_HitTreeTimer = 0;
            m_AssignedTree.Hit();
        }

        if (m_ChoppingTimer > m_WoodGatherTickTime)
        {
            m_WoodCollected += m_WoodPerTick;
            m_ChoppingTimer = 0;

            if (m_WoodCollected == m_WoodCapacity)
            {
                HandleChppongFinished();
            }
        }
    }

    void HandleChppongFinished()
    {
        m_Animator.SetBool("IsChopping", false);

        m_AssignedWoodStorage = m_GameManager.FindClosestWoodStorage(transform.position);

        if (m_AssignedWoodStorage != null)
        {
            var closestPointOnStorage = m_AssignedWoodStorage.Collider.ClosestPoint(transform.position);
            MoveTo(closestPointOnStorage);
        }


        SetUnitState(UnitState.Idle);
        SetUnitTask(UnitTask.ReturnResource);
    }

    void CheckForConstruction()
    {

        var distanceToConstruction = Vector3.Distance(transform.position, Target.transform.position);
        if (distanceToConstruction <= m_ObjectDetectionRadius && CurrentState == UnitState.Idle)
        {
            StartBuilding(Target as StructureUnit);
        }
    }


    void StartBuilding(StructureUnit structureUnit)
    {
        SetUnitState(UnitState.Building);
        m_Animator.SetBool("IsBuilding", true);
        structureUnit.AssignWorkerToBuildProcess(this);
    }

    void TryMoveToClosestTree()
    {
        var closestTree = m_GameManager.FindClosestUnclaimedTree(transform.position);
        if (closestTree != null)
        {
            SendToChop(closestTree);
        }
    }

    void TryMoveToClosestGoldMine()
    {
        var closestGoldMine = m_GameManager.FindClosestUnclaimedGoldMine(transform.position);
        if (closestGoldMine != null)
        {
            SendToMine(closestGoldMine);
        }
    }

    void ResetState()
    {
        SetUnitTask(UnitTask.None);

        if (HasTarget) CleanupTarget();

        m_Animator.SetBool("IsBuilding", false);
        m_Animator.SetBool("IsChopping", false);

        m_ChoppingTimer = 0;

        if (m_AssignedTree != null)
        {
            m_AssignedTree.Release();
            m_AssignedTree = null;
        }
    }

    void CleanupTarget()
    {
        if (Target is StructureUnit structure)
        {
            structure.UnassignWorkerToBuildProcess();
        }

        SetTarget(null);
    }
}//end class

